{"row_number": 18405, "expected_answer": "\\(\\frac{1}{2}\\)", "problem": "Find \\(\\lim_{x\\to\\infty} x \\int_{0}^{x} e^{t^2 - x^2} \\, dt\\).", "generated_solution": "<think>\nOkay, so I need to find the limit as x approaches infinity of x times the integral from 0 to x of e^(t² - x²) dt. Hmm, let's break this down. The expression is x multiplied by the integral from 0 to x of e^{t² - x²} dt. That's the same as x times e^{-x²} times the integral from 0 to x of e^{t²} dt. Wait, because e^{t² - x²} is e^{t²} divided by e^{x²}, right? So, factoring out the e^{-x²}, which doesn't depend on t, we can write it as x * e^{-x²} * ∫₀ˣ e^{t²} dt.\n\nNow, the integral of e^{t²} dt from 0 to x... Hmm, I remember that the integral of e^{t²} doesn't have an elementary antiderivative, but maybe I can relate it to the error function or something else. The error function is erf(x) = (2/√π) ∫₀ˣ e^{-t²} dt, but that's e^{-t²}, not e^{t²}. So perhaps this integral is related to a different function, or maybe I need to use some asymptotic expansion?\n\nWait, since we're looking at the limit as x approaches infinity, maybe I can approximate the integral ∫₀ˣ e^{t²} dt for large x. Let's see. For t near x, t is large, so e^{t²} is growing very rapidly. But the integral from 0 to x of e^{t²} dt... As t increases, the integrand increases exponentially. So the main contribution to the integral should come from the upper limit near t = x. Maybe I can use a substitution to approximate the integral for large x.\n\nAlternatively, maybe integration by parts. Let me try substitution. Let’s set u = x - t. Then when t approaches x, u approaches 0. Let's see:\n\nLet u = x - t. Then t = x - u, dt = -du. The integral becomes ∫₀ˣ e^{(x - u)^2} (-du) from u = x to u = 0. Flipping the limits, it becomes ∫₀ˣ e^{(x - u)^2} du. Let's expand the exponent: (x - u)^2 = x² - 2xu + u². So e^{(x - u)^2} = e^{x² - 2xu + u²} = e^{x²} * e^{-2xu + u²}. Therefore, the integral becomes ∫₀ˣ e^{x²} e^{-2xu + u²} du = e^{x²} ∫₀ˣ e^{-2xu + u²} du. But the original integral was ∫₀ˣ e^{t²} dt = e^{x²} ∫₀ˣ e^{-2xu + u²} du. Therefore, substituting back into the original limit expression:\n\nThe original limit is x * e^{-x²} * ∫₀ˣ e^{t²} dt = x * e^{-x²} * e^{x²} ∫₀ˣ e^{-2xu + u²} du = x * ∫₀ˣ e^{-2xu + u²} du. So that simplifies to x times ∫₀ˣ e^{-2xu + u²} du.\n\nHmm, that's interesting. Now, maybe we can analyze this integral ∫₀ˣ e^{-2xu + u²} du. Let's write the exponent as u² - 2xu = u² - 2xu + x² - x² = (u - x)^2 - x². Wait, that's (u - x)^2 - x² = u² - 2xu + x² - x² = u² - 2xu. So the exponent is (u - x)^2 - x². Therefore, e^{-2xu + u²} = e^{(u - x)^2 - x²} = e^{(u - x)^2} * e^{-x²}. Wait, but that's different from before. Wait, maybe I made a mistake here. Let me check:\n\nWait, if u² - 2xu = (u - x)^2 - x², then e^{u² - 2xu} = e^{(u - x)^2 - x²} = e^{(u - x)^2} * e^{-x²}. But that would mean that the integral becomes ∫₀ˣ e^{(u - x)^2} * e^{-x²} du. However, when we substituted earlier, we already had e^{x²} from expanding (x - u)^2, so perhaps this is conflicting. Wait, maybe I need to re-express the integral properly.\n\nAlternatively, maybe another substitution. Let's consider that for large x, the variable u in the integral ∫₀ˣ e^{-2xu + u²} du is going from 0 to x. Let's make a substitution v = u. Wait, but if u is going up to x, maybe setting v = u/x, so that when u goes from 0 to x, v goes from 0 to 1. Let's try that. Let v = u/x, so u = v x, du = x dv. Then the integral becomes ∫₀¹ e^{-2x(v x) + (v x)^2} * x dv = x ∫₀¹ e^{-2x² v + v² x²} dv = x ∫₀¹ e^{x² ( -2v + v² ) } dv. Hmm, the exponent is x² (-2v + v²). Let's write that as x² (v² - 2v). Let's factor this: v² - 2v = v(v - 2). Hmm, but for v in [0,1], this is negative, because v - 2 is negative. So the exponent is negative. Therefore, e^{x² (v² - 2v)} = e^{-x² (2v - v²)}. So we have the integral x ∫₀¹ e^{-x² (2v - v²)} dv.\n\nNow, for large x, the exponent -x² (2v - v²) becomes very negative except where 2v - v² is minimized. Let's find the minimum of 2v - v² in [0,1]. The derivative is 2 - 2v. Setting to zero, 2 - 2v = 0 => v = 1. So the minimum occurs at v = 1, where 2v - v² = 2*1 - 1 = 1. Therefore, the maximum of the exponent -x² (2v - v²) is at v=1, but since it's multiplied by -x², the minimum of the exponent is at v=1, and the maximum of the function e^{-x² (2v - v²)} is at v=1. Wait, perhaps I should think in terms of Laplace's method for integrals. Laplace's method approximates integrals of the form ∫ e^{M f(v)} dv for large M, where the main contribution comes from the vicinity of the maximum of f(v). In our case, the exponent is -x² (2v - v²) = M f(v) where M = x² and f(v) = - (2v - v²). The maximum of f(v) is where 2v - v² is minimized. Since 2v - v² is a quadratic opening downward, its minimum on [0,1] is at v=1, as we found. So Laplace's method tells us that the integral is dominated by the contribution near v=1.\n\nTherefore, we can approximate the integral ∫₀¹ e^{-x² (2v - v²)} dv ≈ e^{-x² (2*1 - 1²)} * something. Wait, but Laplace's method for integrals where the maximum is at an endpoint. The standard Laplace method for ∫ e^{M f(v)} dv over [a,b], if the maximum occurs at an endpoint, then the integral is approximated by e^{M f(c)} * something. In our case, f(v) = - (2v - v²), so the maximum of f(v) is at v=1, where f(1) = - (2*1 -1) = -1. So the integral is approximated by e^{-x² (1)} multiplied by the integral near v=1. Wait, but actually, Laplace's method for endpoint maxima would give:\n\nIf the function f(v) has its maximum at the upper limit v=1, then the integral can be approximated by e^{M f(1)} * [something involving the derivative at v=1]. Let me recall the formula. For ∫_{a}^{b} e^{M f(v)} dv, if the maximum is at v=b, then expand f(v) around v=b: f(v) ≈ f(b) + f’(b)(v - b) + (1/2) f''(b)(v - b)^2 + ... But since at the maximum, the first derivative f’(b) should be negative (if the maximum is at the upper endpoint, the derivative is negative going into the interval). Let's compute f(v) = - (2v - v²), so f’(v) = - (2 - 2v). At v=1, f’(1) = - (2 - 2*1) = 0. Wait, that's interesting. So the first derivative is zero. Then we need to check the second derivative. f''(v) = - (-2) = 2. So at v=1, f''(v) = 2. Therefore, expanding f(v) around v=1: let’s set v = 1 - s, where s is small. Then f(v) = f(1 - s) = - [2(1 - s) - (1 - s)^2] = - [2 - 2s - (1 - 2s + s²)] = - [2 - 2s -1 + 2s - s²] = - [1 - s²] = -1 + s². Therefore, near v=1, f(v) ≈ -1 + s² where s = 1 - v. So the integral becomes ∫_{v=0}^{v=1} e^{M (-1 + s²)} dv = e^{-M} ∫_{s=0}^{s=1} e^{M s²} ds. But since we have v = 1 - s, then dv = -ds, and the limits change from s=1 to s=0. Wait, but that complicates it. Alternatively, let's make substitution s = 1 - v, so that as v approaches 1, s approaches 0. Then dv = -ds. So the integral ∫₀¹ e^{M f(v)} dv = ∫₀¹ e^{-M (2v - v²)} dv = ∫₀¹ e^{-M (2(1 - s) - (1 - s)^2)} (-ds) from s=1 to s=0, which is ∫₀¹ e^{-M [2 - 2s - (1 - 2s + s²)]} ds = ∫₀¹ e^{-M [2 - 2s -1 + 2s - s²]} ds = ∫₀¹ e^{-M [1 - s²]} ds = e^{-M} ∫₀¹ e^{M s²} ds.\n\nBut s ranges from 0 to 1. For large M, the main contribution to ∫₀¹ e^{M s²} ds comes from s near 1, since s² is largest there. Wait, but s² is largest at s=1. But again, the exponent is M s². So the integral ∫₀¹ e^{M s²} ds. Let me make another substitution. Let t = s sqrt(M), so s = t / sqrt(M), ds = dt / sqrt(M). Then the integral becomes ∫_{t=0}^{t=sqrt(M)} e^{t²} (dt / sqrt(M)) = (1 / sqrt(M)) ∫₀^{sqrt(M)} e^{t²} dt. However, the integral ∫₀^{sqrt(M)} e^{t²} dt for large M can be approximated. We know that ∫₀^{Y} e^{t²} dt ≈ (e^{Y²})/(2Y) for large Y. So if Y = sqrt(M), then ∫₀^{sqrt(M)} e^{t²} dt ≈ e^{M}/(2 sqrt(M)). Therefore, ∫₀¹ e^{M s²} ds ≈ (1 / sqrt(M)) * (e^{M}/(2 sqrt(M))) ) = e^{M}/(2 M). Therefore, the original integral ∫₀¹ e^{M f(v)} dv ≈ e^{-M} * (e^{M}/(2 M)) ) = 1/(2 M). Therefore, going back, our integral ∫₀¹ e^{-x² (2v - v²)} dv ≈ 1/(2 x²). Then the original expression we had was x times this integral, so x * [1/(2 x²)] = 1/(2x). Therefore, the limit as x approaches infinity of x * ∫₀ˣ e^{t² - x²} dt = limit as x approaches infinity of 1/(2x) = 0.\n\nWait, but that would suggest the limit is zero. However, that seems counterintuitive because the integral of e^{t²} from 0 to x grows very rapidly, and we're multiplying by x and dividing by e^{x²}. Maybe my approximation is missing something. Let me check the steps again.\n\nWait, when we did the substitution v = u/x, we transformed the integral into x ∫₀¹ e^{-x² (2v - v²)} dv. Then using Laplace's method, we approximated this integral as x * [1/(2x²)] = 1/(2x). So the overall expression tends to 1/(2x), whose limit as x approaches infinity is zero. But wait, let me check another approach. Maybe integrating by parts?\n\nAlternatively, let's consider the integral ∫₀ˣ e^{t²} dt. For large x, this integral behaves like e^{x²}/(2x). Because the integral of e^{t²} from 0 to x is dominated by the endpoint at t=x, and integrating e^{t²} near t=x would be similar to integrating e^{x²} over a small interval. But actually, the leading term is known to be e^{x²}/(2x) as x approaches infinity. Let me confirm this. Let’s consider the substitution t = x - s, so s = x - t. Then as t approaches x, s approaches 0. The integral becomes ∫₀ˣ e^{t²} dt = ∫₀^{x} e^{(x - s)^2} ds. Expanding (x - s)^2 = x² - 2xs + s². So the integral is e^{x²} ∫₀^{x} e^{-2xs + s²} ds. For large x, the main contribution comes from s near 0 because the exponent -2xs dominates. So approximate s² as negligible compared to -2xs. So the integral ≈ e^{x²} ∫₀^{∞} e^{-2xs} ds (extending the upper limit to infinity since the contribution is negligible beyond s = o(1)). The integral ∫₀^{∞} e^{-2xs} ds = 1/(2x). Therefore, ∫₀ˣ e^{t²} dt ≈ e^{x²}/(2x). Therefore, the original expression is x * e^{-x²} * ∫₀ˣ e^{t²} dt ≈ x * e^{-x²} * (e^{x²}/(2x)) ) = x * (1/(2x)) = 1/2. Therefore, the limit should be 1/2.\n\nWait, but this contradicts the previous conclusion. So which one is correct? Hmm. There must be a mistake in my application of Laplace's method earlier. Let me re-examine.\n\nFirst approach: After substitution, we had x * ∫₀ˣ e^{-2xu + u²} du. Then we set u = v x, leading to x ∫₀¹ e^{-2x² v + x² v²} x dv (Wait, no. Wait, u = v x, du = x dv, so ∫₀ˣ e^{-2xu + u²} du = ∫₀¹ e^{-2x (v x) + (v x)^2} x dv = x ∫₀¹ e^{-2x² v + x² v²} dv = x ∫₀¹ e^{x²(v² - 2v)} dv.\n\nFactor the exponent: v² - 2v = (v -1)^2 -1. So it's e^{x²[(v -1)^2 -1]} = e^{-x²} e^{x² (v -1)^2}. Therefore, the integral becomes x e^{-x²} ∫₀¹ e^{x² (v -1)^2} dv. Let’s make a substitution w = v -1, then when v=0, w=-1; when v=1, w=0. So the integral becomes x e^{-x²} ∫_{-1}^0 e^{x² w²} dw = x e^{-x²} ∫₀¹ e^{x² w²} dw, since the integrand is even function. Hmm, so it's x e^{-x²} times the integral from 0 to1 of e^{x² w²} dw. Now, the integral ∫₀¹ e^{x² w²} dw for large x. Let me change variables: let t = x w, so w = t/x, dw = dt/x. Then the integral becomes ∫₀ˣ e^{t²} (dt/x) ) = (1/x) ∫₀ˣ e^{t²} dt. But we already know that ∫₀ˣ e^{t²} dt ≈ e^{x²}/(2x). Therefore, ∫₀¹ e^{x² w²} dw ≈ (1/x) * (e^{x²}/(2x)) = e^{x²}/(2x²). Therefore, the original integral x e^{-x²} * (e^{x²}/(2x²)) = x * (1/(2x²)) = 1/(2x). So the limit as x→infty is 0. But this contradicts the second approach where we got 1/2. So which one is correct?\n\nWait, in the second approach, I used a substitution t = x - s and approximated the integral as e^{x²}/(2x). Then multiplying by x e^{-x²} gives 1/2. But the first approach with the substitution and Laplace's method gives 1/(2x). Therefore, there must be a miscalculation somewhere.\n\nWait, perhaps when I used the substitution t = x - s, I transformed the integral into e^{x²} ∫₀^x e^{-2xs + s²} ds. Then approximated this as e^{x²} times ∫₀^infty e^{-2xs} ds = e^{x²} * 1/(2x). Therefore, the integral ∫₀ˣ e^{t²} dt ≈ e^{x²}/(2x). Therefore, the original expression x * e^{-x²} * ∫₀ˣ e^{t²} dt ≈ x * e^{-x²} * e^{x²}/(2x) ) = x/(2x) = 1/2. Hence, the limit is 1/2.\n\nBut according to the first approach, through substitution u = v x, I end up with 1/(2x). So the problem is in the two different methods giving different answers. Therefore, I must have made a mistake in one of the steps.\n\nLet me check the first method again. After substitution u = v x, the integral becomes x ∫₀¹ e^{-2x² v + x² v²} dv = x ∫₀¹ e^{x² (v² - 2v)} dv. Then, completing the square: v² -2v = (v -1)^2 -1. Therefore, the integral is x e^{-x²} ∫₀¹ e^{x² (v -1)^2} dv. Then substitution w = 1 - v. Then integral becomes x e^{-x²} ∫₀¹ e^{x² w²} dw. Then substitute t = x w, which gives ∫₀¹ e^{x² w²} dw = (1/x) ∫₀^x e^{t²} dt. Then the integral becomes x e^{-x²} * (1/x) ∫₀ˣ e^{t²} dt = e^{-x²} ∫₀ˣ e^{t²} dt. But that brings us back to the original expression. Wait, so substituting the approximation ∫₀ˣ e^{t²} dt ≈ e^{x²}/(2x), this gives e^{-x²} * e^{x²}/(2x) = 1/(2x), so multiplying by x, we get 1/2.\n\nWait, now I'm confused. Let me write step by step:\n\nOriginal expression: x * ∫₀ˣ e^{t² - x²} dt = x * e^{-x²} ∫₀ˣ e^{t²} dt. Then ∫₀ˣ e^{t²} dt ≈ e^{x²}/(2x). Therefore, x * e^{-x²} * (e^{x²}/(2x)) = (x / (2x)) ) * (e^{-x²} e^{x²}) ) = (1/2) * 1 = 1/2. So this approach gives 1/2.\n\nBut when I did the other substitution (with u = v x), and got the integral as x ∫₀¹ e^{-2x² v + x² v²} dv, and then completed the square, leading to x e^{-x²} ∫₀¹ e^{x² (v -1)^2} dv. Then substitution w = v -1, but the integral ∫₀¹ e^{x² (v -1)^2} dv becomes ∫_{-1}^0 e^{x² w²} dw, which is the same as ∫₀¹ e^{x² w²} dw. Then that's (1/x) ∫₀^x e^{t²} dt. Wait, then:\n\nx e^{-x²} * ∫₀¹ e^{x² (v -1)^2} dv = x e^{-x²} * (1/x) ∫₀^x e^{t²} dt = e^{-x²} ∫₀ˣ e^{t²} dt. But this is exactly the original expression. So this approach leads us back to the same expression, so we can't compute it directly. Therefore, the correct approach is to use the approximation ∫₀ˣ e^{t²} dt ≈ e^{x²}/(2x), leading to the limit 1/2.\n\nBut why did the first method using Laplace's approximation with substitution v = u/x lead to 1/(2x)? Because perhaps the substitution v = u/x was applied incorrectly?\n\nWait, in the first method, after substitution v = u/x, the integral becomes x ∫₀¹ e^{-2x² v + x² v²} dv = x ∫₀¹ e^{x² (v² - 2v)} dv. As we said, this is x e^{-x²} ∫₀¹ e^{x² (v -1)^2} dv. Then we can write the integral ∫₀¹ e^{x² (v -1)^2} dv as ∫_{-1}^0 e^{x² w²} dw. For large x, the integrand e^{x² w²} is significant only near w=0 (i.e., near v=1). Therefore, we can approximate the integral ∫_{-1}^0 e^{x² w²} dw ≈ ∫_{-infty}^0 e^{x² w²} dw, since the contribution from w < -1 is negligible. Then ∫_{-infty}^0 e^{x² w²} dw = (1/x) ∫_{-infty}^0 e^{t²} dt (substituting t = x w). But ∫_{-infty}^0 e^{t²} dt is divergent. Wait, that can't be. Wait, no, e^{t²} is integrable only over finite intervals. Therefore, the substitution leading to ∫_{-infty}^0 e^{t²} dt is problematic because that integral diverges. Hence, the approximation must consider only the local behavior near w=0.\n\nTherefore, even though the original integral ∫_{-1}^0 e^{x² w²} dw is from w=-1 to 0, but for large x, the integrand e^{x² w²} is significant only when w is close to 0. Therefore, we can approximate the integral by extending the lower limit to negative infinity (even though the original integral only goes to -1). Because for large x, the integrand is negligible except near w=0. Therefore, ∫_{-1}^0 e^{x² w²} dw ≈ ∫_{-infty}^0 e^{x² w²} dw = (1/x) ∫_{-infty}^0 e^{t²} dt = (1/x) * (sqrt(π)/2). Wait, but ∫_{-infty}^infty e^{-t²} dt = sqrt(π), but here the integral is ∫_{-infty}^0 e^{t²} dt. Wait, but e^{t²} is not integrable over (-infty,0] because it blows up as t→-infty. Therefore, this approach is invalid. So there must be a mistake here.\n\nTherefore, maybe the only valid approach is the first substitution where we found that ∫₀ˣ e^{t²} dt ≈ e^{x²}/(2x). Hence, the original expression is x * e^{-x²} * e^{x²}/(2x) ) = 1/2. Therefore, the limit is 1/2. Let's verify this with another method. Maybe L’Hospital’s Rule.\n\nLet’s consider the expression: x ∫₀ˣ e^{t² - x²} dt. Let’s write it as x e^{-x²} ∫₀ˣ e^{t²} dt. Let’s set F(x) = ∫₀ˣ e^{t²} dt. Then the expression is x e^{-x²} F(x). To find the limit as x→infty. Let’s consider the ratio F(x)/(e^{x²}/x). If we can show that F(x) ~ e^{x²}/(2x), then the limit of x e^{-x²} * e^{x²}/(2x) is 1/2.\n\nAlternatively, apply L’Hospital’s Rule to F(x)/(e^{x²}/x). Let me write the expression as F(x)/(e^{x²}/x). The ratio tends to [something] as x→infty. If we can write this as [F(x) * x]/e^{x²} and take the limit as x→infty. Let’s compute the limit of [F(x) * x]/e^{x²}. As x→infty, both numerator and denominator go to infinity. So applying L’Hospital’s Rule:\n\nLimit = lim [d/dx (F(x) * x)] / [d/dx e^{x²}].\n\nCompute derivative of numerator: d/dx (F(x) * x) = F'(x) * x + F(x) * 1 = e^{x²} * x + F(x).\n\nDerivative of denominator: d/dx e^{x²} = 2x e^{x²}.\n\nSo the limit becomes [x e^{x²} + F(x)] / [2x e^{x²}]. We can split this into [x e^{x²}/(2x e^{x²})] + [F(x)/(2x e^{x²})] = 1/2 + [F(x)/(2x e^{x²})].\n\nNow, if we can show that F(x)/(2x e^{x²}) tends to 0 as x→infty, then the limit is 1/2. But F(x) = ∫₀ˣ e^{t²} dt ≈ e^{x²}/(2x). So F(x)/(2x e^{x²}) ≈ (e^{x²}/(2x)) / (2x e^{x²}) = 1/(4x²) → 0. Therefore, the limit is 1/2 + 0 = 1/2. Therefore, by L’Hospital’s Rule, the original limit is 1/2.\n\nTherefore, the correct answer should be 1/2. So initially, the first method had an error in the Laplace's approximation because the integral substitution led to a divergent integral, but using the substitution t = x - s and approximating the integral as e^{x²}/(2x), and then verifying with L’Hospital's Rule confirms that the limit is indeed 1/2.\n\n**Final Answer**\n\\boxed{\\dfrac{1}{2}}\n</think>To find the limit \\(\\lim_{x\\to\\infty} x \\int_{0}^{x} e^{t^2 - x^2} \\, dt\\), we start by rewriting the expression:\n\n\\[\nx \\int_{0}^{x} e^{t^2 - x^2} \\, dt = x e^{-x^2} \\int_{0}^{x} e^{t^2} \\, dt\n\\]\n\nFor large \\(x\\), we approximate the integral \\(\\int_{0}^{x} e^{t^2} \\, dt\\). Using the substitution \\(t = x - s\\), we have \\(dt = -ds\\) and the limits change from \\(t = 0\\) to \\(t = x\\) to \\(s = x\\) to \\(s = 0\\). Thus, the integral becomes:\n\n\\[\n\\int_{0}^{x} e^{t^2} \\, dt = \\int_{x}^{0} e^{(x-s)^2} (-ds) = \\int_{0}^{x} e^{(x-s)^2} \\, ds\n\\]\n\nExpanding the exponent \\((x-s)^2\\):\n\n\\[\n(x-s)^2 = x^2 - 2xs + s^2\n\\]\n\nSo the integral becomes:\n\n\\[\n\\int_{0}^{x} e^{x^2 - 2xs + s^2} \\, ds = e^{x^2} \\int_{0}^{x} e^{-2xs + s^2} \\, ds\n\\]\n\nFor large \\(x\\), the term \\(e^{-2xs}\\) dominates, and we can approximate \\(e^{s^2} \\approx 1\\) (since \\(s\\) is small):\n\n\\[\ne^{x^2} \\int_{0}^{x} e^{-2xs} \\, ds = e^{x^2} \\int_{0}^{x} e^{-2xs} \\, ds \\approx e^{x^2} \\int_{0}^{\\infty} e^{-2xs} \\, ds\n\\]\n\nEvaluating the integral:\n\n\\[\n\\int_{0}^{\\infty} e^{-2xs} \\, ds = \\left[ -\\frac{1}{2x} e^{-2xs} \\right]_{0}^{\\infty} = \\frac{1}{2x}\n\\]\n\nThus, the integral \\(\\int_{0}^{x} e^{t^2} \\, dt\\) is approximated by:\n\n\\[\n\\int_{0}^{x} e^{t^2} \\, dt \\approx \\frac{e^{x^2}}{2x}\n\\]\n\nSubstituting this back into the original expression:\n\n\\[\nx e^{-x^2} \\cdot \\frac{e^{x^2}}{2x} = \\frac{1}{2}\n\\]\n\nTo confirm this result, we can use L'Hôpital's Rule. Let \\(F(x) = \\int_{0}^{x} e^{t^2} \\, dt\\). The expression becomes:\n\n\\[\n\\frac{F(x) \\cdot x}{e^{x^2}}\n\\]\n\nApplying L'Hôpital's Rule to the form \\(\\frac{\\infty}{\\infty}\\):\n\n\\[\n\\lim_{x \\to \\infty} \\frac{d/dx (x F(x))}{d/dx (e^{x^2})} = \\lim_{x \\to \\infty} \\frac{e^{x^2} \\cdot x + F(x)}{2x e^{x^2}}\n\\]\n\nSplitting the numerator and simplifying:\n\n\\[\n\\frac{x e^{x^2}}{2x e^{x^2}} + \\frac{F(x)}{2x e^{x^2}} = \\frac{1}{2} + \\frac{F(x)}{2x e^{x^2}}\n\\]\n\nSince \\(F(x) \\approx \\frac{e^{x^2}}{2x}\\), the second term tends to 0:\n\n\\[\n\\frac{F(x)}{2x e^{x^2}} \\approx \\frac{e^{x^2}/(2x)}{2x e^{x^2}} = \\frac{1}{4x^2} \\to 0 \\text{ as } x \\to \\infty\n\\]\n\nThus, the limit is:\n\n\\[\n\\boxed{\\frac{1}{2}}\n\\]"}