{"row_number": 19221, "expected_answer": "\\( x = (A^TA)^{-1}A^Tb \\)", "problem": "Let \\( A \\) be a real \\( (m \\times n) \\) matrix. Find and classify the critical points of the function \\( f(x) = ||Ax - b||^2 \\), assuming that \\( A'A \\) is invertible.", "generated_solution": "<think>\nOkay, so I need to find and classify the critical points of the function f(x) = ||Ax - b||², where A is an m×n real matrix, and A'A is invertible. Hmm. Let me start by recalling what critical points are. Critical points are points where the gradient (or derivative) of the function is zero. Since this is a function from R^n to R, I need to compute its derivative, set it to zero, and solve for x. Then, classify those points as minima, maxima, or saddle points. \n\nFirst, let's write out the function more explicitly. The squared norm ||Ax - b||² is equal to (Ax - b)'(Ax - b), where ' denotes transpose. Expanding that, it becomes x'A'Ax - 2b'Ax + b'b. So, f(x) is a quadratic function in x. Since quadratic functions are convex if the matrix is positive definite, but maybe I should verify that later.\n\nTo find the critical points, take the gradient of f with respect to x and set it to zero. Let me compute the derivative. The derivative of x'A'Ax with respect to x is 2A'Ax (using the rule that derivative of x'Qx is 2Qx if Q is symmetric). The derivative of -2b'Ax is -2A'b, since the derivative of c'x is c. The derivative of b'b is zero. So putting it all together, the gradient ∇f(x) = 2A'Ax - 2A'b. Setting this equal to zero gives 2A'Ax - 2A'b = 0, which simplifies to A'Ax = A'b. \n\nSince we're given that A'A is invertible, we can solve for x by multiplying both sides by (A'A)^{-1}. So the critical point is x* = (A'A)^{-1}A'b. \n\nNow, to classify this critical point. Since f(x) is a quadratic function, its Hessian matrix is 2A'A. The Hessian is constant everywhere because it's a quadratic form. To determine if it's a minimum, maximum, or saddle point, we need to check the definiteness of the Hessian. \n\nA'A is a symmetric matrix because (A'A)' = A'A. For any non-zero vector z, z'A'Az = ||Az||², which is always greater than or equal to zero. So A'A is positive semi-definite. However, if A has full column rank (which is equivalent to A'A being invertible), then z ≠ 0 implies Az ≠ 0, so z'A'Az = ||Az||² > 0. Therefore, when A'A is invertible, it's positive definite. \n\nSince the Hessian is 2A'A, which is positive definite, the critical point x* is a local minimum. But since the function is convex (as the Hessian is positive definite everywhere), this local minimum is actually the global minimum. \n\nTherefore, the function f(x) has a unique critical point at x* = (A'A)^{-1}A'b, which is a global minimum.\n\nWait, just to check, is there any possibility that there could be other critical points? Since the gradient is linear in x (because f is quadratic), and the equation A'Ax = A'b is a linear system. If A'A is invertible, this system has exactly one solution. Therefore, the critical point is unique. So no other critical points exist. \n\nAnd classification: since the Hessian is positive definite, it's a local min, which is also global because the function is convex. \n\nSo I think that's it. The critical point is the least squares solution, which makes sense because minimizing ||Ax - b||² is the least squares problem. The solution is given by the normal equations, and here since A'A is invertible, it's unique. So the answer should be that the critical point is x* = (A'A)^{-1}A'b and it's a global minimum.\n</think>To find and classify the critical points of the function \\( f(x) = ||Ax - b||^2 \\), where \\( A \\) is an \\( m \\times n \\) real matrix and \\( A^\\top A \\) is invertible, we proceed as follows:\n\n1. **Express the function explicitly:**\n   \\[\n   f(x) = ||Ax - b||^2 = (Ax - b)^\\top (Ax - b)\n   \\]\n   Expanding this, we get:\n   \\[\n   f(x) = x^\\top A^\\top Ax - 2b^\\top Ax + b^\\top b\n   \\]\n\n2. **Compute the gradient of \\( f(x) \\):**\n   The gradient of \\( f(x) \\) is given by:\n   \\[\n   \\nabla f(x) = \\frac{\\partial}{\\partial x} \\left( x^\\top A^\\top Ax - 2b^\\top Ax + b^\\top b \\right)\n   \\]\n   Using the rules of matrix differentiation, we have:\n   \\[\n   \\nabla f(x) = 2A^\\top Ax - 2A^\\top b\n   \\]\n\n3. **Set the gradient to zero to find the critical points:**\n   \\[\n   2A^\\top Ax - 2A^\\top b = 0\n   \\]\n   Simplifying, we get:\n   \\[\n   A^\\top Ax = A^\\top b\n   \\]\n   Since \\( A^\\top A \\) is invertible, we can solve for \\( x \\):\n   \\[\n   x^* = (A^\\top A)^{-1} A^\\top b\n   \\]\n\n4. **Classify the critical point:**\n   To classify the critical point, we examine the Hessian matrix of \\( f(x) \\):\n   \\[\n   H = \\nabla^2 f(x) = \\frac{\\partial^2}{\\partial x \\partial x^\\top} \\left( x^\\top A^\\top Ax - 2b^\\top Ax + b^\\top b \\right) = 2A^\\top A\n   \\]\n   Since \\( A^\\top A \\) is symmetric and positive definite (as \\( A^\\top A \\) is invertible and for any non-zero vector \\( z \\), \\( z^\\top A^\\top A z = ||Az||^2 > 0 \\)), the Hessian \\( 2A^\\top A \\) is also positive definite.\n\n   A positive definite Hessian implies that the critical point \\( x^* \\) is a local minimum. Since \\( f(x) \\) is a convex function (as the Hessian is positive definite everywhere), this local minimum is also a global minimum.\n\nTherefore, the function \\( f(x) = ||Ax - b||^2 \\) has a unique critical point at:\n\\[\n\\boxed{x = (A^\\top A)^{-1} A^\\top b}\n\\]\nwhich is a global minimum."}