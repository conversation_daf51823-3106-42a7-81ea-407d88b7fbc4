{"row_number": 18534, "expected_answer": "10", "problem": "In a class of 100 students who like sports, 60 like football, 50 like tennis, and 70 like swimming. Additionally, 20 like both football and tennis, 30 like both football and swimming, and 40 like both tennis and swimming. How many students like all three sports (football, tennis, and swimming)?", "generated_solution": "<think>\nOkay, let's see. I need to figure out how many students like all three sports: football, tennis, and swimming. The class has 100 students who like at least one of these sports. The numbers given are 60 like football, 50 like tennis, and 70 like swimming. Then there are the overlaps: 20 like both football and tennis, 30 like both football and swimming, 40 like both tennis and swimming. Hmm, this sounds like a problem where I should use the principle of inclusion-exclusion for three sets. Let me recall the formula.\n\nThe formula for the union of three sets A, B, C is:\n\n|A ∪ B ∪ C| = |A| + |B| + |C| - |A ∩ B| - |A ∩ C| - |B ∩ C| + |A ∩ B ∩ C|\n\nHere, the total number of students is 100, which is the union of all three sets. So, plugging the numbers into the formula:\n\n100 = 60 (football) + 50 (tennis) + 70 (swimming) - 20 (football and tennis) - 30 (football and swimming) - 40 (tennis and swimming) + x (all three)\n\nLet me calculate that step by step. First, adding the individual sports: 60 + 50 + 70 = 180. Then subtract the pairwise overlaps: 20 + 30 + 40 = 90. So 180 - 90 = 90. Then the equation becomes 100 = 90 + x. So x, which is the number of students who like all three, should be 10. Wait, is that right?\n\nLet me check the calculation again. Maybe I made a mistake in the arithmetic. 60 + 50 is 110, plus 70 is 180. Then subtract 20 + 30 + 40. 20 + 30 is 50, plus 40 is 90. So 180 - 90 is 90. Then 100 = 90 + x. So x is 10. That seems correct.\n\nBut wait, let me make sure I didn't mix up anything. The formula subtracts all the pairwise overlaps but then adds back the triple overlap because it was subtracted too many times. So yes, the formula makes sense. So if the total is 100, then 100 equals 60+50+70=180 minus (20+30+40)=90 plus x. So 100=180-90 +x => 100=90 +x => x=10. Yeah, that seems right.\n\nIs there another way to think about it? Let's imagine the overlaps. The problem says 20 like both football and tennis, but that count includes those who also like swimming. Similarly, the 30 who like football and swimming include those who also like tennis, and the 40 who like tennis and swimming include those who like all three. So when we subtract those overlaps, we've subtracted the all-three group three times, but we should have subtracted them only once. Therefore, we need to add them back twice, but according to the inclusion-exclusion formula, we add them back once. Wait, maybe not. Let me think again.\n\nWait, the inclusion-exclusion formula starts by adding all individual sets. Then subtracts the pairwise intersections because those were counted twice. But when you subtract the pairwise intersections, if there are students in all three, they were subtracted three times (once for each pair they belong to). But they should only be subtracted twice because they were originally counted three times (once in each individual set). So the formula is: total = sum of individual sets - sum of pairwise intersections + sum of triple intersections. So yeah, adding back the triple intersection once.\n\nTherefore, in this case, with the numbers given, the calculation is 180 - 90 + x = 100. So x is 10. So the answer should be 10. I think that's correct. Let me check if there's any constraints I'm missing. For example, are the given pairwise overlaps possible? Let's see, for the football and tennis overlap of 20, but the total in football is 60 and tennis is 50. So the maximum possible overlap would be 50 (if all tennis lovers also like football). Similarly, the overlap can't be more than the smaller of the two. But 20 is less than 50, so that's fine. For football and swimming, 30 is less than 60 and 70. For tennis and swimming, 40 is less than 50 and 70. So all the pairwise overlaps are possible. Also, if x is 10, then how does that affect the individual overlaps?\n\nLet me see. For example, the number of students who like only football and tennis but not swimming would be 20 (total football and tennis) minus x (those who like all three). So 20 - 10 = 10. Similarly, football and swimming only: 30 -10=20, and tennis and swimming only:40 -10=30. Then, let's check the total. Football only would be total football (60) minus those who like football and tennis only (10), football and swimming only (20), and all three (10). So 60 -10-20-10=20. Similarly, tennis only: 50 -10 (tennis and football only) -30 (tennis and swimming only) -10 (all three) =50-10-30-10=0. Wait, that can't be right. If tennis only is zero, that's possible? Hmm. And swimming only would be total swimming (70) minus football and swimming only (20) minus tennis and swimming only (30) minus all three (10). So 70 -20-30-10=10. Then total students would be football only (20) + tennis only (0) + swimming only (10) + football and tennis only (10) + football and swimming only (20) + tennis and swimming only (30) + all three (10). Adding these up: 20 +0 +10 +10 +20 +30 +10 = 100. Which matches the total. So even though tennis only is zero, that's acceptable. So all numbers check out. Therefore, the answer should be 10. Seems valid. So the number of students who like all three sports is 10.\n</think>To determine how many students like all three sports (football, tennis, and swimming), we use the principle of inclusion-exclusion for three sets. Let's denote the sets as follows:\n- \\( A \\) for students who like football,\n- \\( B \\) for students who like tennis,\n- \\( C \\) for students who like swimming.\n\nWe are given:\n- \\( |A| = 60 \\)\n- \\( |B| = 50 \\)\n- \\( |C| = 70 \\)\n- \\( |A \\cap B| = 20 \\)\n- \\( |A \\cap C| = 30 \\)\n- \\( |B \\cap C| = 40 \\)\n- \\( |A \\cup B \\cup C| = 100 \\)\n\nWe need to find \\( |A \\cap B \\cap C| \\).\n\nThe principle of inclusion-exclusion for three sets states:\n\\[\n|A \\cup B \\cup C| = |A| + |B| + |C| - |A \\cap B| - |A \\cap C| - |B \\cap C| + |A \\cap B \\cap C|\n\\]\n\nSubstituting the given values into the formula, we get:\n\\[\n100 = 60 + 50 + 70 - 20 - 30 - 40 + |A \\cap B \\cap C|\n\\]\n\nSimplify the right-hand side:\n\\[\n100 = 180 - 90 + |A \\cap B \\cap C|\n\\]\n\nThis simplifies to:\n\\[\n100 = 90 + |A \\cap B \\cap C|\n\\]\n\nSolving for \\( |A \\cap B \\cap C| \\):\n\\[\n|A \\cap B \\cap C| = 10\n\\]\n\nTherefore, the number of students who like all three sports is \\(\\boxed{10}\\)."}