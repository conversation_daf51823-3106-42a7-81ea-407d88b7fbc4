{"row_number": 19019, "expected_answer": "\\(\\binom{n-k+1}{k}\\)", "problem": "Given a line of \\( n \\) people and a positive integer \\( k \\leq \\left\\lfloor \\frac{n}{2} \\right\\rfloor \\), how many ways are there to pick \\( k \\) people such that no two adjacent people are chosen?", "generated_solution": "<think>\nOkay, let's see. The problem is asking how many ways there are to pick k people from a line of n people such that no two of them are adjacent. And k is a positive integer that's at most floor(n/2). Hmm. Alright, so I need to figure out a combinatorial approach here.\n\nFirst, let me think about smaller examples to get a pattern. Maybe that'll help. Let's say n=2 and k=1. Then we have two people, and we need to pick 1 such that they are not adjacent. But since there are only two people, they are adjacent, but we have to pick one. But the condition is that no two are adjacent. Since we're only picking one, there's no two, so it's allowed. So the answer would be 2. Wait, but n=2, k=1. So 2 ways. Is that right?\n\nBut wait, the problem says k ≤ floor(n/2). For n=2, floor(2/2)=1, so k=1 is allowed. So yes, two ways. If n=3, k=1, then we can pick any of the three people. Since only one is picked, no adjacency issues. So 3 ways. If k=2 for n=3, floor(3/2)=1, so k=2 is not allowed. Wait, but maybe n=4. Let's take n=4, k=2. How many ways? Let's see. The people are in a line: 1,2,3,4. We need to pick two people with no two adjacent.\n\nPossible combinations: 1 and 3, 1 and 4, 2 and 4. So three ways. Is that right? Let's check. 1 and 2: adjacent. Not allowed. 1 and 3: okay. 1 and 4: okay. 2 and 3: adjacent. 2 and 4: okay. 3 and 4: adjacent. So yes, three ways. Hmm. So for n=4, k=2, the answer is 3. \n\nNow, trying to find a general formula here. Maybe this relates to combinations with restrictions. I remember that when you can't have adjacent items, you can model this using stars and bars or recursion.\n\nWait, let's think recursively. Let's denote f(n, k) as the number of ways to choose k non-adjacent people from n. Let's see.\n\nSuppose we consider the first person in the line. If we choose this person, then we cannot choose the second person. Then the remaining people we can choose are from the remaining n-2 people (since person 2 is excluded), and we need to pick k-1 people. So that would be f(n-2, k-1).\n\nAlternatively, if we don't choose the first person, then we can choose k people from the remaining n-1 people. So that's f(n-1, k).\n\nTherefore, the recurrence relation is f(n, k) = f(n-1, k) + f(n-2, k-1).\n\nIs that right? Let's check with n=4, k=2.\n\nf(4,2) = f(3,2) + f(2,1).\n\nNow, f(3,2): How many ways to pick 2 non-adjacent from 3 people? The people are 1,2,3. So possible choices: 1 and 3. Only one way. So f(3,2)=1. Then f(2,1)=2 (as we saw earlier). So 1 + 2 = 3, which matches the example. Good.\n\nAnother check: n=5, k=2.\n\nAccording to the formula, f(5,2) = f(4,2) + f(3,1). f(4,2)=3, f(3,1)=3. So 3+3=6. Let's verify. The people are 1,2,3,4,5. The valid selections: 1&3,1&4,1&5,2&4,2&5,3&5. That's 6. Correct. So the recurrence seems to hold.\n\nSimilarly, for n=3, k=1, f(3,1)=3, which is correct. So the recursion works here.\n\nNow, the base cases. When k=0, f(n,0)=1 (there's one way to choose nobody). If n < 2k-1? Wait, perhaps we can also consider that when n < 0 or k < 0, it's 0. But let's see.\n\nThe standard base cases would be:\n\n- f(n, 0) = 1 for any n ≥ 0.\n- f(0, k) = 0 for any k > 0.\n\nBut in our problem, k is at least 1. But for the recursion, when we go down, we might need these base cases.\n\nAlternatively, for the problem constraints k ≤ floor(n/2), which is necessary because if k > floor(n/2), you can't place k non-adjacent people in a line of n. For example, n=4, floor(4/2)=2, which is the maximum k. So the formula should handle k up to that.\n\nBut regardless, our recursion is f(n, k) = f(n-1, k) + f(n-2, k-1).\n\nNow, if we can find a closed-form formula for f(n, k), that would solve the problem. Alternatively, perhaps this relates to combinations. Let's see.\n\nWait, when arranging k non-adjacent people in n positions, another way to model this is to think of placing k people such that there's at least one person between each pair. This is similar to placing k objects in n slots with no two adjacent. The formula for that is C(n - k + 1, k). Let me verify.\n\nIn the case of n=4, k=2. Then C(4 - 2 +1, 2) = C(3, 2) = 3. Which matches. For n=5, k=2: C(5 -2 +1,2) = C(4,2)=6. Which matches. For n=3, k=1: C(3 -1 +1,1)=C(3,1)=3. Correct. For n=2, k=1: C(2 -1 +1,1)=C(2,1)=2. Correct. Hmm. So this formula seems to hold.\n\nWait, where does this formula come from? Let me think. If we have n people in a line and we need to choose k non-adjacent ones, we can model this as placing k objects with at least one space between them. To do this, imagine placing k objects and n - k non-objects. To prevent adjacency, we need to have at least one non-object between each pair of objects. So we can first place the k objects, which require k positions, and then we have k - 1 gaps between them, each requiring at least one non-object. So that uses up (k) + (k -1) = 2k -1 positions. The remaining non-objects are n - k - (k -1) = n - 2k +1. These can be distributed in the k +1 gaps (before the first object, between the objects, and after the last object). The number of ways to distribute these remaining n - 2k +1 non-objects into k +1 gaps is C((n - 2k +1) + (k +1) -1, (k +1) -1) ) = C(n - k +1, k). Wait, this is stars and bars. The formula for the number of ways to distribute m identical items into r distinct boxes with no restrictions is C(m + r -1, r -1). Here, the m is n - 2k +1, the remaining non-objects, and r is k +1. So the number of ways is C((n - 2k +1) + (k +1) -1, (k +1) -1) = C(n - k +1, k). Wait, the exponent is (k +1 -1) = k. Wait, that seems incorrect. Let me check:\n\nWait, stars and bars: the number of non-negative solutions to x1 + x2 + ... + x_{r} = m is C(m + r -1, r -1). So in this case, the m is the remaining non-objects: n - 2k +1. And the number of variables (gaps) is k +1. So it's C( (n - 2k +1) + (k +1) -1, (k +1) -1 ) = C(n - 2k +1 + k +1 -1, k) = C(n - k +1, k). Yes, that's correct.\n\nTherefore, the number of ways is C(n - k +1, k). So the answer should be the combination formula C(n - k +1, k). Let's check with the previous examples.\n\nFor n=4, k=2: C(4 - 2 +1, 2) = C(3,2)=3. Correct.\n\nFor n=5, k=2: C(5 -2 +1,2)=C(4,2)=6. Correct.\n\nFor n=3, k=1: C(3 -1 +1,1)=C(3,1)=3. Correct.\n\nFor n=2, k=1: C(2 -1 +1,1)=C(2,1)=2. Correct.\n\nSo that seems to be the correct formula. Therefore, the answer is C(n - k +1, k).\n\nBut wait, let me check another example. Let's take n=5, k=3. Wait, floor(5/2)=2, so k=3 would not be allowed. But according to the problem statement, k ≤ floor(n/2), so k=3 is not allowed for n=5. However, if we tried to compute, would the formula still hold? Let's see. If n=5, k=3: C(5 -3 +1,3)=C(3,3)=1. But in reality, how many ways? Trying to choose 3 non-adjacent people in 5 positions. Let's list them:\n\n1,3,5. That's the only possible combination. So yes, 1 way. So even though k=3 is allowed if n=5? Wait, floor(5/2)=2, so k=3 is not allowed as per the problem's constraint. But in our formula, it gives 1. But the problem states k ≤ floor(n/2), so the user is supposed to input k within that range. So as long as k is within that range, the formula works. So if n=5 and k=2, which is allowed, it gives 6, which is correct. So the formula works for any n and k where it's possible to place k non-adjacent people, which requires that n -k +1 ≥ k, which simplifies to n +1 ≥ 2k, or k ≤ (n +1)/2. Which is slightly more permissive than floor(n/2). For example, when n=5, floor(5/2)=2, but (5 +1)/2=3. So the formula allows k up to 3, but the problem statement restricts it to k ≤ 2. But since the problem says k ≤ floor(n/2), then the answer will still be C(n -k +1, k) for such k. Because even if n=5, k=2, the formula gives C(4,2)=6, which is correct.\n\nSo the final answer is the combination of (n -k +1) choose k. So boxed, it's \\boxed{\\dbinom{n - k + 1}{k}}.\n\nWait, but let me confirm one more case. Let's take n=6, k=3. Then floor(6/2)=3, so it's allowed. The formula gives C(6 -3 +1,3)=C(4,3)=4. Let's check manually. People 1,2,3,4,5,6. Choose 3 non-adjacent.\n\nPossible selections:\n\n1,3,5\n\n1,3,6\n\n1,4,6\n\n2,4,6\n\n2,3,5\n\nWait, wait. Wait, let's list all possibilities:\n\nStart with 1:\n\n1,3,5\n\n1,3,6\n\n1,4,6\n\n1,4,5? No, 1 and 4 are not adjacent, 4 and 5 are adjacent. So not allowed.\n\n1,5,6: adjacent.\n\nSo 1,3,5;1,3,6;1,4,6.\n\nThen start with 2:\n\n2,4,6\n\n2,4,5: 4 and 5 are adjacent.\n\n2,5,6: adjacent.\n\n2,3,5: 2 and 3 are not adjacent? Wait, 2 and 3 are adjacent, so choosing 2 and 3 would be invalid. Wait, if you pick 2, then you can't pick 3. So 2,4,6 is one.\n\nWait, is there another? Maybe 2,5, but 5 and 6 can't be chosen if 5 is selected. Wait, maybe 2,4,5. But 4 and 5 are adjacent. So no.\n\nSo perhaps starting with 2: only 2,4,6.\n\nThen starting with 3:\n\n3,5, but need to pick three. Wait, 3,5, and? If starting with 3, the next can be 5, then 7 (but n=6). So 3,5, and nothing else. So maybe not. Wait, maybe 3,5,6? But 5 and 6 are adjacent.\n\nHmm, maybe there's a mistake in my manual count. Wait, according to the formula, there should be 4 ways. But I can only find 3: 1,3,5; 1,3,6;1,4,6; and 2,4,6. That's four. Wait, I missed 2,4,6. So total four, which matches the formula. So okay. Therefore, the formula works here.\n\nTherefore, the answer is the combination formula C(n -k +1, k). So boxed, that's \\boxed{\\dbinom{n - k + 1}{k}}.\n</think>To find the number of ways to pick \\( k \\) people from a line of \\( n \\) people such that no two chosen people are adjacent, we can use the following combinatorial approach.\n\n### Step-by-Step Solution:\n\n1. **Understanding the Problem:**\n   - We need to choose \\( k \\) people from \\( n \\) people in a line such that no two chosen people are adjacent.\n   - This means that if person \\( i \\) is chosen, then person \\( i+1 \\) cannot be chosen.\n\n2. **Transforming the Problem:**\n   - To ensure no two chosen people are adjacent, we can think of placing \\( k \\) chosen people and \\( n - k \\) unchosen people in such a way that there is at least one unchosen person between any two chosen people.\n   - This can be visualized by first placing \\( k \\) chosen people, which automatically creates \\( k-1 \\) gaps between them (since there must be at least one unchosen person in each gap).\n\n3. **Counting the Gaps:**\n   - After placing \\( k \\) chosen people, we have \\( n - k \\) unchosen people to place.\n   - We need to place these \\( n - k \\) unchosen people in \\( k + 1 \\) possible gaps (before the first chosen person, between the chosen people, and after the last chosen person).\n\n4. **Using Combinatorics:**\n   - The problem now reduces to distributing \\( n - k \\) unchosen people into \\( k + 1 \\) gaps such that each of the \\( k-1 \\) gaps between the chosen people has at least one unchosen person.\n   - This can be simplified by first placing one unchosen person in each of the \\( k-1 \\) gaps, which uses up \\( k-1 \\) unchosen people.\n   - We then have \\( (n - k) - (k - 1) = n - 2k + 1 \\) unchosen people left to place freely in the \\( k + 1 \\) gaps.\n\n5. **Applying the Stars and Bars Theorem:**\n   - The number of ways to distribute \\( n - 2k + 1 \\) unchosen people into \\( k + 1 \\) gaps is given by the binomial coefficient \\( \\binom{(n - 2k + 1) + (k + 1) - 1}{(k + 1) - 1} \\).\n   - Simplifying the expression inside the binomial coefficient, we get \\( \\binom{n - k + 1}{k} \\).\n\n### Final Answer:\nThe number of ways to choose \\( k \\) non-adjacent people from \\( n \\) people in a line is given by the combination formula:\n\\[\n\\boxed{\\binom{n - k + 1}{k}}\n\\]"}