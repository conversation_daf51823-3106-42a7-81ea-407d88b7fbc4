{"row_number": 19006, "expected_answer": "\\(-\\frac{\\pi^2}{2}\\ln 2\\)", "problem": "Evaluate the integral\n\n$$\\int_0^\\pi x\\ln(\\sin(x))dx$$", "generated_solution": "<think>\nOkay, so I need to evaluate the integral from 0 to π of x times the natural logarithm of sin(x) dx. Hmm, let me think about how to approach this. Integrals involving logarithms can be tricky, especially with trigonometric functions. Maybe integration by parts? Since it's x multiplied by ln(sin x), integration by parts might work here. Let me recall the formula: ∫u dv = uv - ∫v du. \n\nAlright, so I need to choose u and dv. Let me set u = ln(sin x), which would make du = (cos x / sin x) dx, right? That simplifies to cot x dx. Then dv would be x dx, which means v = (1/2)x². Wait, but when I do integration by parts, the integral becomes uv evaluated from 0 to π minus the integral of v du. So that would be (1/2)x² ln(sin x) evaluated from 0 to π minus the integral of (1/2)x² cot x dx from 0 to π. Let me check the boundary terms first. At x = π, sin(π) is 0, so ln(0) is negative infinity, but multiplied by (1/2)π². Wait, that term would be (1/2)π² ln(0), which is negative infinity. But at x = 0, sin(0) is 0 as well, so ln(0) is also negative infinity. But maybe they cancel out? Wait, no, because x² is 0 at x=0. So maybe the boundary term is zero? Let me think. When x approaches 0 from the right, x² ln(sin x) approaches 0 * (-infty). Hmm, that's indeterminate. Let me check the limit: lim_{x→0+} x² ln(sin x). Sin x ~ x - x³/6, so ln(sin x) ~ ln(x) - x²/6. So ln(x) is -infty, but x² ln(x) approaches 0 because x² goes to 0 faster than ln(x) goes to -infty. So the boundary term at 0 is 0. Similarly, at π, x approaches π. Let me substitute t = π - x, so as x approaches π, t approaches 0. Then sin x = sin(π - t) = sin t ~ t. So ln(sin x) ~ ln t. So (1/2)x² ln(sin x) becomes (1/2)(π - t)^2 ln t. As t approaches 0, (π - t)^2 approaches π², and ln t approaches -infty. But again, multiplied by t^0 (since it's just ln t), so does the product go to 0? Wait, but the coefficient is (1/2)π² ln t. Wait, no, the coefficient is (1/2)(π - t)^2 which is (1/2)(π² - 2π t + t²), so as t approaches 0, that becomes (1/2)π². Then multiplied by ln t, which approaches -infty. Wait, so that term would be (1/2)π² * (-infty), which is -infty. But that's a problem. So our boundary term at π is -infty, but the original integral is from 0 to π. So maybe this approach is not working? Hmm. Did I make a mistake in choosing u and dv?\n\nAlternatively, maybe I should choose u = x and dv = ln(sin x) dx. Then du = dx, and v would be the integral of ln(sin x) dx. But I don't know the integral of ln(sin x) off the top of my head. Wait, but maybe there's a known result for the integral of ln(sin x) over 0 to π. Let me recall: I think ∫₀^π ln(sin x) dx = -π ln 2. Yes, that's a standard result. But here I need the antiderivative, not the definite integral. Hmm, maybe not helpful.\n\nAlternatively, perhaps there's a Fourier series expansion for ln(sin x)? Let me remember. I recall that ln(2 sin x) can be expressed as -∑_{n=1}^∞ (cos 2n x)/n. So, ln(sin x) = -ln 2 - ∑_{n=1}^∞ (cos 2n x)/n. Is that correct? Let me check. Yes, the Fourier series for ln(sin x) on the interval (0, π) is indeed -ln 2 - ∑_{n=1}^∞ (cos 2n x)/n. So that could be useful. Let me verify this. Let me recall that for x in (0, π), ln(sin x) = -ln 2 - ∑_{n=1}^∞ (cos 2n x)/n. Yes, I think that's right. Then, substituting this into the integral might help.\n\nSo, let's write the integral as ∫₀^π x [ -ln 2 - ∑_{n=1}^∞ (cos 2n x)/n ] dx. Then, we can split this into two integrals: -ln 2 ∫₀^π x dx - ∑_{n=1}^∞ (1/n) ∫₀^π x cos 2n x dx. Let's compute each part.\n\nFirst integral: -ln 2 ∫₀^π x dx. The integral of x dx from 0 to π is (1/2)π², so this part is -ln 2 * (1/2)π² = -(π²/2) ln 2.\n\nSecond integral: - ∑_{n=1}^∞ (1/n) ∫₀^π x cos 2n x dx. Let's compute the integral ∫ x cos 2n x dx. Integration by parts: let u = x, dv = cos 2n x dx. Then du = dx, v = (1/(2n)) sin 2n x. Then, ∫x cos 2n x dx = (x/(2n)) sin 2n x - ∫(1/(2n)) sin 2n x dx. The integral of sin 2n x dx is - (1/(2n)) cos 2n x. So the whole integral becomes (x/(2n)) sin 2n x + (1/(4n²)) cos 2n x evaluated from 0 to π.\n\nLet's compute this at π and 0. First, at x = π: (π/(2n)) sin(2nπ) + (1/(4n²)) cos(2nπ). Sin(2nπ) is 0, and cos(2nπ) is 1. So this term is (0) + (1/(4n²))(1) = 1/(4n²).\n\nAt x = 0: (0/(2n)) sin(0) + (1/(4n²)) cos(0) = 0 + (1/(4n²))(1) = 1/(4n²).\n\nSo subtracting the lower limit from the upper limit: [1/(4n²) - 1/(4n²)] = 0. Wait, that can't be right. Wait, the integral from 0 to π would be [upper limit] - [lower limit]. But both upper and lower limits give 1/(4n²). So the integral is 1/(4n²) - 1/(4n²) = 0? That seems strange. But wait, hold on. Let me check my steps again.\n\nWait, ∫x cos(2n x) dx from 0 to π: when I plug in x=π, the first term is (π/(2n)) sin(2nπ), which is zero because sin(2nπ)=0 for integer n. Then, the second term is (1/(4n²)) cos(2nπ) = 1/(4n²).\n\nAt x=0, the first term is 0, and the second term is (1/(4n²)) cos(0) = 1/(4n²).\n\nTherefore, the integral is [1/(4n²) - 1/(4n²)] = 0. Wait, so that means ∫₀^π x cos(2n x) dx = 0? That can't be. Maybe I made a mistake in the antiderivative. Let me check:\n\n∫x cos(a x) dx = (x/a) sin(a x) - ∫(1/a) sin(a x) dx = (x/a) sin(a x) + (1/a²) cos(a x) + C. So evaluated from 0 to π: [(π/a) sin(a π) + (1/a²) cos(a π)] - [0 + (1/a²) cos(0)] = (π/a) sin(a π) + (1/a²)[cos(a π) - 1].\n\nIn our case, a = 2n. So plugging in:\n\n(π/(2n)) sin(2n π) + (1/(4n²))[cos(2n π) - 1]. Since sin(2n π) = 0, cos(2n π) = 1. So this becomes 0 + (1/(4n²))(1 - 1) = 0. So yes, the integral is zero. Hmm, but that seems counterintuitive. So for all n, ∫₀^π x cos(2n x) dx = 0. Then the entire sum term would be zero. But that would mean the original integral is just - (π²/2) ln 2. But I remember that the integral ∫₀^π x ln(sin x) dx is supposed to be something like - (π²/2) ln 2, but maybe not exactly. Wait, but according to this calculation, it is. But I need to verify this because I might have made a mistake.\n\nWait, but I think I recall that ∫₀^π x ln(sin x) dx = - (π²/2) ln 2. Wait, maybe that's the answer. But let me check again. If the Fourier series approach gives that, then maybe it's correct. Alternatively, perhaps I messed up the expansion.\n\nWait, let me check the Fourier series again. For ln(sin x) over (0, π). I think that's correct. The Fourier series of ln(sin x) is -ln 2 - Σ_{n=1}^∞ (cos 2n x)/n. Let me verify that. For example, we know that ln(2 sin x) = -Σ_{n=1}^∞ (cos 2n x)/n, so ln(sin x) = -ln 2 - Σ_{n=1}^∞ (cos 2n x)/n. Yes, that's correct. So that part is right.\n\nThen substituting into the integral, splitting into two parts. The first part gives - (π²/2) ln 2, the second part is - Σ_{n=1}^∞ (1/n) * 0 = 0. Therefore, the integral is - (π²/2) ln 2. But wait, is that possible? Let me check the integral using another method to confirm.\n\nAlternatively, perhaps there's symmetry in the integral. Let me make substitution x = π - t. Then when x goes from 0 to π, t goes from π to 0. So ∫₀^π x ln(sin x) dx = ∫_π^0 (π - t) ln(sin(π - t)) (-dt) = ∫₀^π (π - t) ln(sin t) dt. Because sin(π - t) = sin t. Then, adding the original integral I and the transformed version:\n\nI = ∫₀^π x ln(sin x) dx = ∫₀^π (π - x) ln(sin x) dx.\n\nSo adding them together: 2I = π ∫₀^π ln(sin x) dx. Therefore, I = (π/2) ∫₀^π ln(sin x) dx. But ∫₀^π ln(sin x) dx = -π ln 2. So I = (π/2)( -π ln 2 ) = - (π²/2) ln 2. Which matches the previous result.\n\nWait, so both methods give the same answer. So even though when I tried integration by parts initially, I thought there was a problem with the boundary term, but maybe the answer is indeed - (π²/2) ln 2. So why did the first approach with integration by parts seem problematic? Let me check again. \n\nIn the first approach, I had ∫ x ln(sin x) dx = uv - ∫ v du, which was [(1/2)x² ln(sin x)] from 0 to π minus ∫ (1/2)x² cot x dx. But according to the other method, the integral is - (π²/2) ln 2. However, in the first approach, the boundary term was [(1/2)x² ln(sin x)] from 0 to π, which, as we saw, at x=0, the term is 0, and at x=π, it's (1/2)π² ln(0) which is -infty. But the other term is - ∫ (1/2)x² cot x dx. So how does that resolve? Maybe despite the boundary term being -infty, the integral term cancels it out. Let's see. If the first approach must yield the correct answer, then even if the boundary term is problematic, the combination of the two terms must give a finite result. Let me try to compute the integral ∫₀^π (1/2)x² cot x dx. Maybe this integral is divergent, but in reality, when combined with the boundary term, the infinities cancel? That seems complicated. Alternatively, perhaps my initial integration by parts was incorrect because the integral of x² cot x dx is not straightforward. Maybe the Fourier series approach and the symmetry argument are more reliable here. Since both give the same answer, and the integration by parts approach seems to have complications with divergent terms, it's safer to go with the symmetry result. So the answer should be - (π²/2) ln 2. \n\nBut let me check with another approach. Suppose I use the Fourier series expansion and verified via symmetry. Both methods give the same result. So the answer is - (π²/2) ln 2. Therefore, the integral evaluates to this.\n\nWait, but let me confirm once more. Let me check the integral using substitution. Let me make substitution x = π/2 - t. Then sin x = cos t, and the integral becomes ∫_{-π/2}^{π/2} (π/2 - t) ln(cos t) dt. But this might not help. Alternatively, maybe use the expansion ln(sin x) and integrate term by term, but that's similar to what I did earlier. Alternatively, consider differentiating under the integral sign. Let me see. Suppose I consider I(a) = ∫₀^π x ln(a sin x) dx. Then differentiate with respect to a. I'(a) = ∫₀^π x * (sin x)/(a sin x) dx = (1/a) ∫₀^π x dx = (1/a)(π²/2). Then integrate from a=0 to a=1. Wait, but ln(0) is problematic. Hmm, maybe not the best approach. Alternatively, perhaps consider a different parameterization. \n\nAlternatively, integrate from 0 to π/2 and use symmetry? Wait, but the original integral is from 0 to π. Alternatively, split the integral into two parts, 0 to π/2 and π/2 to π. But since we already used the substitution x = π - t and found that I = (π/2) ∫₀^π ln(sin x) dx, which gives - (π²/2) ln 2, that seems more straightforward.\n\nAlternatively, check the integral numerically for a specific case. For example, compute it numerically with π=3.1416 and ln(2)≈0.6931. Then - (π²/2) ln 2 ≈ - (9.8696/2) * 0.6931 ≈ -4.9348 * 0.6931 ≈ -3.423. Let me see if that makes sense. But computing the actual integral numerically would take time, but I can reason about it. Since ln(sin x) is negative over (0, π), so the integral of x times a negative function would be negative. The value -3.423 seems reasonable. \n\nAlternatively, think about if there's an alternative representation. Wait, if I recall, there's an integral formula: ∫₀^π x ln(sin x) dx = - (π²/2) ln 2. So yes, the answer should be that. \n\nTherefore, after considering multiple approaches—Fourier series, symmetry substitution, and known integrals—it seems the correct answer is - (π²/2) ln 2. Therefore, I can conclude that the integral evaluates to this value.\n</think>To evaluate the integral\n\n\\[\n\\int_0^\\pi x \\ln(\\sin x) \\, dx,\n\\]\n\nwe will use the symmetry of the integrand and a known result for the integral of \\(\\ln(\\sin x)\\) over the interval \\([0, \\pi]\\).\n\nFirst, let's make the substitution \\( x = \\pi - t \\). When \\( x = 0 \\), \\( t = \\pi \\), and when \\( x = \\pi \\), \\( t = 0 \\). Thus, the integral transforms as follows:\n\n\\[\n\\int_0^\\pi x \\ln(\\sin x) \\, dx = \\int_\\pi^0 (\\pi - t) \\ln(\\sin(\\pi - t)) (-dt).\n\\]\n\nSince \\(\\sin(\\pi - t) = \\sin t\\), we have:\n\n\\[\n\\int_0^\\pi x \\ln(\\sin x) \\, dx = \\int_0^\\pi (\\pi - t) \\ln(\\sin t) \\, dt.\n\\]\n\nNow, let \\( I \\) denote the original integral:\n\n\\[\nI = \\int_0^\\pi x \\ln(\\sin x) \\, dx.\n\\]\n\nFrom the substitution, we have:\n\n\\[\nI = \\int_0^\\pi (\\pi - t) \\ln(\\sin t) \\, dt.\n\\]\n\nAdding the two expressions for \\( I \\):\n\n\\[\n2I = \\int_0^\\pi x \\ln(\\sin x) \\, dx + \\int_0^\\pi (\\pi - t) \\ln(\\sin t) \\, dt.\n\\]\n\nSince the variable of integration is a dummy variable, we can combine the integrals:\n\n\\[\n2I = \\int_0^\\pi x \\ln(\\sin x) \\, dx + \\int_0^\\pi (\\pi - x) \\ln(\\sin x) \\, dx.\n\\]\n\nThis simplifies to:\n\n\\[\n2I = \\int_0^\\pi x \\ln(\\sin x) \\, dx + \\int_0^\\pi \\pi \\ln(\\sin x) \\, dx - \\int_0^\\pi x \\ln(\\sin x) \\, dx.\n\\]\n\nThe first and third integrals cancel each other out:\n\n\\[\n2I = \\pi \\int_0^\\pi \\ln(\\sin x) \\, dx.\n\\]\n\nWe know from a standard result that:\n\n\\[\n\\int_0^\\pi \\ln(\\sin x) \\, dx = -\\pi \\ln 2.\n\\]\n\nSubstituting this result into the equation for \\( 2I \\):\n\n\\[\n2I = \\pi (-\\pi \\ln 2) = -\\pi^2 \\ln 2.\n\\]\n\nThus, solving for \\( I \\):\n\n\\[\nI = -\\frac{\\pi^2}{2} \\ln 2.\n\\]\n\nTherefore, the value of the integral is:\n\n\\[\n\\boxed{-\\frac{\\pi^2}{2} \\ln 2}.\n\\]"}