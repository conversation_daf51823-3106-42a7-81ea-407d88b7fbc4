{"row_number": 18357, "expected_answer": "\\(x + \\frac{x^2}{4} + \\frac{x^3}{18} + \\cdots\\)", "problem": "Find the <PERSON><PERSON><PERSON> expansion of $\\int_{0}^{x} \\frac{e^{t} -1}{t} \\, dt$.", "generated_solution": "<think>\nOkay, so I need to find the <PERSON><PERSON>rin expansion of the integral from 0 to x of (e^t - 1)/t dt. Let me start by recalling what a Maclaurin series is. It's just a Taylor series expansion around 0. So, I need to expand the integrand (e^t -1)/t as a power series and then integrate term by term from 0 to x.\n\nFirst, let me think about the <PERSON><PERSON>rin series for e^t. I remember that e^t is the sum from n=0 to infinity of t^n / n!. So, if I subtract 1 from e^t, that would remove the first term (which is 1), so e^t - 1 is the sum from n=1 to infinity of t^n / n!.\n\nNow, the integrand is (e^t -1)/t. If I divide each term of the series by t, that would decrease the exponent of t by 1. So, (e^t -1)/t = sum from n=1 to infinity of t^{n-1}/n! = sum from n=0 to infinity of t^n/(n+1)!.\n\nWait, let me check that substitution. If n starts at 1, then n-1 starts at 0. So, changing the index: let k = n-1. Then when n=1, k=0, so the sum becomes sum from k=0 to infinity of t^k/( (k+1)! ). So, yes, that's correct. So, (e^t -1)/t = sum_{k=0}^\\infty t^k / (k+1)!.\n\nSo, the integrand is a power series, so integrating term by term from 0 to x should give the integral. So, the integral from 0 to x of (e^t -1)/t dt is the integral from 0 to x of [sum_{k=0}^\\infty t^k / (k+1)!] dt.\n\nSince power series can be integrated term by term within their interval of convergence, I can interchange the sum and the integral. So, this becomes sum_{k=0}^\\infty [ integral from 0 to x of t^k / (k+1)! dt ].\n\nIntegrate each term: integral of t^k dt is t^{k+1}/(k+1). So, multiply that by 1/(k+1)! gives t^{k+1}/[ (k+1)(k+1)! ) ].\n\nBut (k+1)(k+1)! is equal to (k+1)! multiplied by (k+1), which is (k+2)! divided by something? Wait, no. Wait, (k+1)! = (k+1)*k!, so (k+1)(k+1)! = (k+1)^2 *k! Hmm, perhaps not. Let me compute that again.\n\nWait, if I have 1/(k+1)! multiplied by t^{k+1}/(k+1), that's the same as t^{k+1}/( (k+1)!*(k+1) ). So, the term becomes x^{k+1}/[ (k+1)!*(k+1) ) ].\n\nWait, perhaps another way to write that. Let me note that (k+1)!*(k+1) = (k+1)!*(k+1) = (k+2)! / (k+2) ? Wait, no. Let me think. Wait, (k+2)! = (k+2)(k+1)! So, (k+1)! * (k+1) = (k+1)! * (k+1) = (k+1)^{2} *k!.\n\nWait, maybe it's better to just leave it as x^{k+1}/[ (k+1)*(k+1)! ) ].\n\nBut maybe there's a better way to index the series. Let me see. Let me set m = k+1. Then when k starts at 0, m starts at 1. So, the sum becomes sum_{m=1}^\\infty x^m / (m * m! ). Because (k+1) = m, so k = m -1, so the term is x^{m}/( m * m! ). So, the integral is sum_{m=1}^\\infty x^m / (m * m! ).\n\nTherefore, the Maclaurin expansion of the given integral is the sum from m=1 to infinity of x^m / (m * m! ). So, in sigma notation, that's Σ_{m=1}^∞ [x^m / (m * m!)].\n\nBut maybe we can write the first few terms to make it clearer. Let's compute the first few terms. For m=1: x/(1*1!) = x. For m=2: x^2/(2*2!) = x^2/(4). For m=3: x^3/(3*6) = x^3/18. For m=4: x^4/(4*24) = x^4/96, etc.\n\nSo, the expansion starts as x + x^2/4 + x^3/18 + x^4/96 + ... So, the general term is x^n / (n * n!) for n starting at 1.\n\nTherefore, the Maclaurin series is the sum from n=1 to infinity of x^n / (n * n!) ).\n\nAlternatively, perhaps we can relate this to known functions. Wait, but I don't recall a standard function with that series. Maybe we can express it in terms of exponential integrals or something, but the problem just asks for the Maclaurin expansion, so expressing it as a sum is sufficient.\n\nAlternatively, check if there's another way to write it. Let me think. Let me recall that the exponential integral function Ei(x) has a series expansion, but that's usually around infinity or has different terms. But perhaps not relevant here.\n\nAlternatively, note that integrating (e^t -1)/t. Another approach: The integral of e^t /t dt from 0 to x minus integral of 1/t dt from 0 to x. Wait, but 1/t integrated from 0 to x is ln(x) - ln(0), which diverges. But wait, the original integral is (e^t -1)/t. When t approaches 0, (e^t -1)/t approaches 1, so the integrand is continuous at 0, so the integral is well-defined. But in our earlier approach, we found the expansion as sum_{n=1}^\\infty x^n/(n *n!). So, that should be correct.\n\nWait, but just to make sure, perhaps compute the integral another way.\n\nAlternatively, notice that the integral from 0 to x of (e^t -1)/t dt can be expressed as the integral from 0 to x [sum_{k=1}^\\infty t^{k-1}/k! ] dt. Then integrating term by term gives sum_{k=1}^\\infty integral from 0 to x t^{k-1}/k! dt = sum_{k=1}^\\infty [x^k/(k *k! )].\n\nYes, that's the same result as before. So, that confirms it.\n\nAlternatively, check for the first few coefficients by computing derivatives at 0. Let's confirm the first term.\n\nLet f(x) = ∫₀ˣ (eᵗ -1)/t dt. Then, f(0) = 0. Then, f’(x) = (e^x -1)/x. To find the Maclaurin series, compute derivatives at 0. But (e^x -1)/x at x=0 is 1, so f’(0) is 1. Wait, but f’(x) = (e^x -1)/x. However, directly substituting x=0 gives 0/0, so we need to take the limit as x approaches 0. Using L’Hospital's Rule: lim_{x→0} (e^x -1)/x = lim_{x→0} e^x /1 = 1. So, f’(0) = 1.\n\nThen, f’’(x) would be derivative of (e^x -1)/x. Let's compute that. Using quotient rule: [ (e^x * x - (e^x -1) *1 ) ] / x². At x=0, again, need to compute the limit as x→0. Let's compute f’’(x):\n\nNumerator derivative: d/dx [ (e^x -1)/x ] = [e^x * x - (e^x -1) ] / x²\n\nSimplify numerator: e^x x - e^x +1 = e^x (x -1) +1.\n\nAt x=0, numerator is e^0 (0 -1) +1 = (-1) +1 = 0. So, again 0/0. Apply L’Hospital’s Rule for the limit as x→0: lim [ (e^x (x -1) +1 ) / x² ]\n\nTake derivative of numerator and denominator:\n\nNumerator derivative: e^x (x -1) + e^x *1 + 0 = e^x (x -1 +1) = e^x x\n\nDenominator derivative: 2x\n\nSo, lim_{x→0} (e^x x) / (2x) ) = lim_{x→0} e^x /2 = 1/2. So, f''(0) = 1/2.\n\nWait, but according to the series expansion we found, the coefficient of x^2 is 1/(2*2!) = 1/4, so the second derivative at 0 would be 2! * coefficient of x^2, which is 2!*(1/4) = 1/2. So that matches. Similarly, let's check coefficient of x^1. The first derivative at 0 is 1, which is 1*1!*(coefficient of x^1). The coefficient of x^1 in the expansion is 1/(1*1!) = 1, so 1! *1 =1, which matches f’(0)=1.\n\nSimilarly, f''(0) =1/2 as computed, which is 2!*(1/(2*2!))=2!*(1/4)= 2*(1/4)=1/2. So, that's consistent.\n\nLet's check the third derivative. Compute f'''(0). From f''(x)= [e^x x - e^x +1 ] /x². Then f'''(x) is derivative of that. Let's compute it:\n\nFirst, f''(x) = [e^x (x -1) +1 ] /x². Let’s compute f'''(x):\n\nUsing quotient rule again: numerator derivative is [e^x (x-1) + e^x (1) ] *x² - [e^x (x-1) +1 ]*2x all over x^4.\n\nSimplify numerator:\n\nFirst term: [e^x (x -1 +1) ]*x² = e^x x *x² = e^x x^3\n\nSecond term: - [e^x (x -1) +1 ]*2x = -2x e^x (x -1) -2x\n\nSo, total numerator: e^x x^3 -2x e^x (x -1) -2x\n\nFactor out x: x [e^x x^2 - 2 e^x (x -1) -2 ]\n\nAt x=0, numerator becomes 0 [0 - 2 e^0 (-1) -2 ] = 0*[0 - (-2) -2] = 0*0=0. Denominator is x^4, so again 0/0. Apply L’Hospital’s Rule again. Let me compute the limit as x→0 of the numerator and denominator:\n\nNumerator: e^x x^3 -2x e^x (x -1) -2x\n\nFirst, factor out x: x [e^x x^2 - 2 e^x (x -1) -2 ]\n\nLet me compute the limit as x→0 of [e^x x^2 - 2 e^x (x -1) -2 ] /x^3 (since the original limit is numerator/x^4, which is [x * something]/x^4 = something/x^3. So, the limit becomes lim_{x→0} [e^x x^2 - 2 e^x (x -1) -2 ] /x^3.\n\nAgain, plugging x=0 gives [0 - 2(-1) -2]/0 = (0 +2 -2)/0=0/0. Apply L’Hospital:\n\nTake derivative of numerator and denominator:\n\nNumerator derivative: e^x x^2 + 2 e^x x -2 [e^x (x -1) + e^x ] -0\n\nSimplify:\n\n= e^x x^2 + 2 e^x x -2 e^x (x -1 +1 )\n\n= e^x x^2 + 2 e^x x -2 e^x x\n\n= e^x x^2\n\nDenominator derivative: 3x²\n\nSo, the limit is lim_{x→0} e^x x^2 / 3x² = lim_{x→0} e^x /3 = 1/3.\n\nSo, the original limit for f'''(0) is 1/3. Therefore, f'''(0) = 1/3.\n\nBut according to our series, the coefficient of x^3 is 1/(3*3!)=1/18. Then, the third derivative at 0 is 3! * (1/18) = 6*(1/18)=1/3, which matches. So, that's correct.\n\nTherefore, the Maclaurin series we obtained by integrating term by term is correct, as confirmed by computing the derivatives up to the third order. So, the answer is the sum from n=1 to infinity of x^n / (n *n! ), which can be written as Σ_{n=1}^∞ x^n/(n·n!).\n\nAlternatively, since the problem says \"Find the Maclaurin expansion\", it might expect the answer in the form of a series written out. So, putting it together, the expansion is:\n\nx + x²/(2·2!) + x³/(3·3!) + x^4/(4·4!) + … \n\nWhich can be written as:\n\nsum_{n=1}^∞ x^n / (n * n!)\n\nThus, the boxed answer should be \\boxed{\\sum_{n=1}^{\\infty} \\frac{x^n}{n \\cdot n!}}.\n</think>To find the Maclaurin expansion of the integral \\(\\int_{0}^{x} \\frac{e^{t} - 1}{t} \\, dt\\), we follow these steps:\n\n1. **Maclaurin Series for \\(e^t\\):**\n   The Maclaurin series for \\(e^t\\) is given by:\n   \\[\n   e^t = \\sum_{n=0}^{\\infty} \\frac{t^n}{n!}\n   \\]\n   Subtracting 1 from \\(e^t\\), we get:\n   \\[\n   e^t - 1 = \\sum_{n=1}^{\\infty} \\frac{t^n}{n!}\n   \\]\n\n2. **Dividing by \\(t\\):**\n   Dividing the series \\(e^t - 1\\) by \\(t\\), we have:\n   \\[\n   \\frac{e^t - 1}{t} = \\sum_{n=1}^{\\infty} \\frac{t^{n-1}}{n!}\n   \\]\n   Reindexing with \\(k = n - 1\\), we get:\n   \\[\n   \\frac{e^t - 1}{t} = \\sum_{k=0}^{\\infty} \\frac{t^k}{(k+1)!}\n   \\]\n\n3. **Integrating Term by Term:**\n   We integrate the series term by term from 0 to \\(x\\):\n   \\[\n   \\int_{0}^{x} \\frac{e^t - 1}{t} \\, dt = \\int_{0}^{x} \\sum_{k=0}^{\\infty} \\frac{t^k}{(k+1)!} \\, dt\n   \\]\n   Since the series converges uniformly on any finite interval, we can interchange the sum and the integral:\n   \\[\n   \\int_{0}^{x} \\sum_{k=0}^{\\infty} \\frac{t^k}{(k+1)!} \\, dt = \\sum_{k=0}^{\\infty} \\int_{0}^{x} \\frac{t^k}{(k+1)!} \\, dt\n   \\]\n\n4. **Evaluating the Integrals:**\n   Each term is integrated as follows:\n   \\[\n   \\int_{0}^{x} \\frac{t^k}{(k+1)!} \\, dt = \\frac{1}{(k+1)!} \\int_{0}^{x} t^k \\, dt = \\frac{1}{(k+1)!} \\cdot \\frac{x^{k+1}}{k+1} = \\frac{x^{k+1}}{(k+1)(k+1)!}\n   \\]\n   Thus, the series becomes:\n   \\[\n   \\sum_{k=0}^{\\infty} \\frac{x^{k+1}}{(k+1)(k+1)!}\n   \\]\n\n5. **Reindexing the Series:**\n   Let \\(m = k + 1\\). Then the series reindexes to:\n   \\[\n   \\sum_{m=1}^{\\infty} \\frac{x^m}{m \\cdot m!}\n   \\]\n\nTherefore, the Maclaurin expansion of the integral \\(\\int_{0}^{x} \\frac{e^{t} - 1}{t} \\, dt\\) is:\n\\[\n\\boxed{\\sum_{n=1}^{\\infty} \\frac{x^n}{n \\cdot n!}}\n\\]"}