{"row_number": 18536, "expected_answer": "16", "problem": "If \\(x \\in [0, 1]\\), find the maximum value of the expression:\n\\[13\\sqrt{x^{2} - x^{4}} + 9\\sqrt{x^{2} + x^{4}}\\]", "generated_solution": "<think>\nOkay, so I need to find the maximum value of the expression \\(13\\sqrt{x^{2} - x^{4}} + 9\\sqrt{x^{2} + x^{4}}\\) where \\(x\\) is between 0 and 1. Hmm, let's start by understanding the problem. The expression involves square roots of functions of \\(x\\), so maybe I should simplify it first or find a substitution to make it easier. Let me see.\n\nFirst, note that both terms under the square roots have \\(x^2\\) in them. Maybe I can factor that out. Let's try:\n\nFor the first term: \\(\\sqrt{x^{2} - x^{4}} = \\sqrt{x^2(1 - x^2)} = x\\sqrt{1 - x^2}\\). But since \\(x \\in [0,1]\\), \\(x\\) is non-negative, so the square root simplifies nicely without absolute value.\n\nSimilarly, the second term: \\(\\sqrt{x^{2} + x^{4}} = \\sqrt{x^2(1 + x^2)} = x\\sqrt{1 + x^2}\\). Again, since \\(x\\) is non-negative, we can take \\(x\\) out of the square root.\n\nSo substituting back into the original expression, we have:\n\n\\(13x\\sqrt{1 - x^2} + 9x\\sqrt{1 + x^2}\\)\n\nHmm, okay, that might be a bit simpler. Now, maybe I can factor out the \\(x\\) term? Let's see:\n\n\\(x(13\\sqrt{1 - x^2} + 9\\sqrt{1 + x^2})\\)\n\nBut I'm not sure if that helps yet. Alternatively, maybe making a substitution. Let me think. If I let \\(t = x^2\\), then \\(t\\) will range from 0 to 1 as well, since \\(x\\) is between 0 and 1. So substituting \\(t = x^2\\), the expression becomes:\n\n\\(13\\sqrt{t - t^2} + 9\\sqrt{t + t^2}\\)\n\nBut maybe that's not much better. Let's see:\n\nFirst term: \\(13\\sqrt{t(1 - t)}\\)\nSecond term: \\(9\\sqrt{t(1 + t)}\\)\n\nHmm, so:\n\n\\(13\\sqrt{t(1 - t)} + 9\\sqrt{t(1 + t)}\\)\n\nAlternatively, factor out \\(\\sqrt{t}\\):\n\n\\(\\sqrt{t} [13\\sqrt{1 - t} + 9\\sqrt{1 + t}]\\)\n\nThat's interesting. So the expression becomes \\(\\sqrt{t} \\times [13\\sqrt{1 - t} + 9\\sqrt{1 + t}]\\). Since \\(t\\) is between 0 and 1, maybe this is a helpful form. But I need to maximize this expression with respect to \\(t\\). Let's let \\(f(t) = \\sqrt{t} [13\\sqrt{1 - t} + 9\\sqrt{1 + t}]\\), and find its maximum for \\(t \\in [0,1]\\).\n\nTo find the maximum, we can take the derivative of \\(f(t)\\) with respect to \\(t\\), set it equal to zero, and solve for \\(t\\). Then check the critical points and endpoints.\n\nBut derivatives can be messy here. Let me try to compute it step by step.\n\nFirst, let's write \\(f(t)\\) as:\n\n\\(f(t) = 13\\sqrt{t(1 - t)} + 9\\sqrt{t(1 + t)}\\)\n\nWait, that might be a different way. Wait, no. If I take the previous step:\n\n\\(\\sqrt{t} [13\\sqrt{1 - t} + 9\\sqrt{1 + t}]\\), so that's equivalent to \\(13\\sqrt{t}\\sqrt{1 - t} + 9\\sqrt{t}\\sqrt{1 + t}\\), which is the same as \\(13\\sqrt{t(1 - t)} + 9\\sqrt{t(1 + t)}\\). So, same thing.\n\nAlternatively, maybe parametrize \\(x\\) as \\(\\sin\\theta\\) or something. Let me try that. Since \\(x\\) is in [0,1], let me set \\(x = \\sin\\theta\\), where \\(\\theta\\) ranges from 0 to \\(\\pi/2\\). Then \\(x^2 = \\sin^2\\theta\\), and \\(\\sqrt{1 - x^2} = \\cos\\theta\\). Let's try substituting:\n\nFirst term: \\(13x\\sqrt{1 - x^2} = 13 \\sin\\theta \\cos\\theta\\)\n\nSecond term: \\(9x\\sqrt{1 + x^2} = 9 \\sin\\theta \\sqrt{1 + \\sin^2\\theta}\\)\n\nSo the entire expression becomes:\n\n\\(13 \\sin\\theta \\cos\\theta + 9 \\sin\\theta \\sqrt{1 + \\sin^2\\theta}\\)\n\nHmm, not sure if this helps. Alternatively, maybe set \\(x = \\tan\\phi\\) but since \\(x\\) is between 0 and 1, maybe not the best substitution.\n\nAlternatively, maybe using hyperbolic functions? Maybe overcomplicating.\n\nAlternatively, let's consider writing the expression as a function of \\(x\\), take the derivative, set to zero, etc.\n\nLet me denote \\(f(x) = 13\\sqrt{x^{2} - x^{4}} + 9\\sqrt{x^{2} + x^{4}}\\). Wait, but we simplified it earlier to \\(13x\\sqrt{1 - x^2} + 9x\\sqrt{1 + x^2}\\). Maybe easier to differentiate this form.\n\nLet me write \\(f(x) = 13x\\sqrt{1 - x^2} + 9x\\sqrt{1 + x^2}\\). So, to find \\(f'(x)\\), first compute derivatives term by term.\n\nFirst term: \\(13x\\sqrt{1 - x^2}\\)\n\nDerivative: 13 times [ derivative of x times sqrt(1 - x²) + x times derivative of sqrt(1 - x²) ]\n\nWait, using product rule. So:\n\nLet’s compute:\n\nFirst term: 13 [ d/dx (x) * sqrt(1 - x²) + x * d/dx (sqrt(1 - x²)) ]\n\nDerivative of x is 1. Derivative of sqrt(1 - x²) is (1/(2 sqrt(1 - x²))) * (-2x) = -x / sqrt(1 - x²)\n\nSo first term derivative:\n\n13 [ 1 * sqrt(1 - x²) + x * (-x / sqrt(1 - x²)) ] = 13 [ sqrt(1 - x²) - x² / sqrt(1 - x²) ]\n\nCombine terms:\n\n13 [ ( (1 - x²) - x² ) / sqrt(1 - x²) ] = 13 [ (1 - 2x²) / sqrt(1 - x²) ]\n\nSimilarly, second term: 9x sqrt(1 + x²)\n\nDerivative: 9 [ d/dx (x) * sqrt(1 + x²) + x * d/dx (sqrt(1 + x²)) ]\n\nDerivative of x is 1. Derivative of sqrt(1 + x²) is (1/(2 sqrt(1 + x²))) * 2x = x / sqrt(1 + x²)\n\nSo derivative of second term:\n\n9 [ 1 * sqrt(1 + x²) + x * (x / sqrt(1 + x²)) ] = 9 [ sqrt(1 + x²) + x² / sqrt(1 + x²) ]\n\nCombine terms:\n\n9 [ (1 + x²) + x² ) / sqrt(1 + x²) ] = 9 [ (1 + 2x²) / sqrt(1 + x²) ]\n\nWait, wait. Wait, when you combine terms:\n\nsqrt(1 + x²) is (1 + x²)^{1/2}, and x² / sqrt(1 + x²) is x²*(1 + x²)^{-1/2}\n\nSo adding them:\n\n(1 + x²)^{1/2} + x²*(1 + x²)^{-1/2} = (1 + x² + x²) / (1 + x²)^{1/2} } = (1 + 2x²) / sqrt(1 + x²)\n\nYes, correct.\n\nSo putting it all together, the derivative f’(x) is:\n\n13*(1 - 2x²)/sqrt(1 - x²) + 9*(1 + 2x²)/sqrt(1 + x²)\n\nSet this equal to zero to find critical points.\n\nSo:\n\n13*(1 - 2x²)/sqrt(1 - x²) + 9*(1 + 2x²)/sqrt(1 + x²) = 0\n\nHmm, this equation seems complicated. Let's see if we can solve it.\n\nFirst, let me move the second term to the other side:\n\n13*(1 - 2x²)/sqrt(1 - x²) = -9*(1 + 2x²)/sqrt(1 + x²)\n\nBut both denominators sqrt(1 - x²) and sqrt(1 + x²) are positive for x in [0,1). So the left-hand side (LHS) and right-hand side (RHS) must have opposite signs. Let's check the signs.\n\nThe term (1 - 2x²) will be positive when x² < 1/2, i.e., x < sqrt(1/2) ≈ 0.707, and negative otherwise. Similarly, (1 + 2x²) is always positive since x² ≥ 0. Therefore, the RHS is -9*(positive)/positive = negative. So LHS must also be negative. Therefore, 13*(1 - 2x²)/sqrt(...) must be negative. Since 13 is positive and denominator is positive, numerator (1 - 2x²) must be negative. So 1 - 2x² < 0 => x² > 1/2 => x > sqrt(1/2) ≈ 0.707. Therefore, critical points can only occur when x > sqrt(1/2). So we can restrict our attention to x in (sqrt(1/2), 1). Let's note that.\n\nSo, for x > 1/√2, we have:\n\n13*(1 - 2x²)/sqrt(1 - x²) = -9*(1 + 2x²)/sqrt(1 + x²)\n\nLet me square both sides to eliminate the square roots. But squaring may introduce extraneous solutions, so we need to check later.\n\nSo square both sides:\n\n[13^2*(1 - 2x²)^2]/ (1 - x²) = [81*(1 + 2x²)^2]/(1 + x²)\n\nLet me compute 13² = 169 and 9² = 81.\n\nSo:\n\n169*(1 - 2x²)^2 / (1 - x²) = 81*(1 + 2x²)^2 / (1 + x²)\n\nCross-multiplying:\n\n169*(1 - 2x²)^2*(1 + x²) = 81*(1 + 2x²)^2*(1 - x²)\n\nThis is going to be a quartic equation. Let's expand both sides.\n\nFirst, let me denote A = (1 - 2x²)^2*(1 + x²)\n\nand B = (1 + 2x²)^2*(1 - x²)\n\nCompute A:\n\n(1 - 2x²)^2 = 1 - 4x² + 4x^4\n\nMultiply by (1 + x²):\n\n(1 - 4x² + 4x^4)(1 + x²) = 1*(1 + x²) - 4x²*(1 + x²) + 4x^4*(1 + x²)\n\n= (1 + x²) - 4x² - 4x^4 + 4x^4 + 4x^6\n\nSimplify:\n\n1 + x² - 4x² -4x^4 +4x^4 +4x^6 = 1 - 3x² + 4x^6\n\nSimilarly, compute B:\n\n(1 + 2x²)^2 = 1 + 4x² + 4x^4\n\nMultiply by (1 - x²):\n\n(1 + 4x² + 4x^4)(1 - x²) = 1*(1 - x²) +4x²*(1 - x²) +4x^4*(1 - x²)\n\n= (1 - x²) +4x² -4x^4 +4x^4 -4x^6\n\nSimplify:\n\n1 -x² +4x² -4x^4 +4x^4 -4x^6 = 1 +3x² -4x^6\n\nTherefore, the equation becomes:\n\n169*(1 - 3x² + 4x^6) = 81*(1 +3x² -4x^6)\n\nLet me expand both sides:\n\nLeft: 169*1 - 169*3x² + 169*4x^6 = 169 - 507x² + 676x^6\n\nRight: 81*1 + 81*3x² -81*4x^6 = 81 +243x² -324x^6\n\nBring all terms to left side:\n\n169 -507x² +676x^6 -81 -243x² +324x^6 = 0\n\nCombine like terms:\n\n(169 -81) + (-507x² -243x²) + (676x^6 +324x^6) = 0\n\n88 -750x² +1000x^6 = 0\n\nDivide both sides by 2 to simplify:\n\n44 -375x² +500x^6 = 0\n\nSo the equation is:\n\n500x^6 -375x² +44 =0\n\nHmm, let me write this as:\n\n500x^6 -375x² +44 =0\n\nThis is a cubic in terms of x². Let me set y =x², so the equation becomes:\n\n500y³ -375y +44 =0\n\nSo we have 500y³ -375y +44 =0. Let's try to solve this cubic equation.\n\nFirst, we can check if there's a rational root. By Rational Root Theorem, possible roots are ±factors of 44 over factors of 500. Factors of 44: 1, 2, 4, 11, 22, 44. Factors of 500: 1, 2, 4, 5, 10, 20, 25, 50, 100, 125, 250, 500.\n\nPossible rational roots could be ±1, ±2, ±4, ±11, etc., divided by 1,2,4,5,10, etc. Let's try y=0.4: 0.4=2/5. Let's compute 500*(8/125) -375*(2/5) +44.\n\n500*(8/125)= (500/125)*8=4*8=32\n\n375*(2/5)=150\n\nSo 32 -150 +44= (32+44)-150=76-150=-74≠0.\n\nTry y=0.2: 0.2=1/5.\n\n500*(1/125) -375*(1/5) +44= 4 -75 +44= (4+44)-75=48-75=-27≠0.\n\ny=0.1: 500*(0.001) -375*(0.1) +44=0.5 -37.5 +44=7≠0.\n\ny=0.25: 500*(0.015625) -375*(0.25) +44=500*(1/64)=7.8125; 375*(0.25)=93.75; 7.8125 -93.75 +44≈-41.9375≠0.\n\ny=0.44: Maybe too big. Let me compute 500*(0.44)^3 -375*(0.44) +44.\n\nFirst, 0.44³=0.085184, 500*0.085184≈42.592\n\n375*0.44=165\n\n42.592 -165 +44≈-78.408≠0.\n\nHmm, maybe not a rational root. Then perhaps need to use the method for solving cubics or use substitution. Alternatively, maybe factor by grouping.\n\nAlternatively, let me check if there is a real root between 0.2 and 0.4, since we saw y=0.2 gives -27, y=0.4 gives -74, but wait wait, wait:\n\nWait, for y=0.2: 500*(0.2)^3 -375*(0.2) +44=500*0.008 -75 +44=4 -75 +44= -27\n\nFor y=0.4: 500*(0.4)^3 -375*(0.4) +44=500*0.064 -150 +44=32 -150 +44= -74\n\nWait, as y increases from 0.2 to 0.4, the value goes from -27 to -74, which is decreasing. What about y=0.1:\n\ny=0.1: 500*0.001 -37.5 +44=0.5 -37.5 +44=7.0\n\nSo at y=0.1, value is +7, at y=0.2 it's -27, so it crossed zero between y=0.1 and y=0.2.\n\nWait, but since our original equation in terms of x was for x > sqrt(1/2) ≈0.707, which corresponds to y =x² >0.5, so we need solutions for y >0.5. Wait, but the cubic equation 500y³ -375y +44 =0, for y>0.5:\n\nLet me check at y=0.5: 500*(0.125) -375*(0.5) +44=62.5 -187.5 +44= -81≠0\n\nAt y=0.6: 500*(0.216) -375*0.6 +44=108 -225 +44= -73\n\ny=0.7: 500*(0.343) -375*0.7 +44=171.5 -262.5 +44= -47\n\ny=0.8: 500*(0.512) -375*0.8 +44=256 -300 +44=0.\n\nWait, y=0.8:\n\n500*(0.512)=500*(512/1000)=256\n\n375*0.8=300\n\n256 -300 +44=0. So y=0.8 is a root.\n\nAh! So y=0.8 is a root, which is 4/5. Let's verify:\n\n500*(4/5)^3 -375*(4/5) +44\n\n(4/5)^3=64/125, 500*(64/125)= (500/125)*64=4*64=256\n\n375*(4/5)=300\n\nSo 256 -300 +44=0. Yes, correct. Therefore, y=4/5 is a root. Therefore, we can factor out (y - 4/5).\n\nUsing polynomial division or synthetic division.\n\nDivide 500y³ -375y +44 by (y - 4/5). Let's do polynomial division.\n\nAlternatively, since we know y=4/5 is a root, the cubic can be written as (y - 4/5)(Ay² + By + C). Let's find A, B, C.\n\nMultiply out:\n\n(y - 4/5)(Ay² + By + C) = Ay³ + By² + Cy - (4/5)Ay² - (4/5)By - (4/5)C\n\n= Ay³ + (B - (4/5)A)y² + (C - (4/5)B)y - (4/5)C\n\nThis should equal 500y³ -375y +44.\n\nTherefore, equating coefficients:\n\nA =500\n\nB - (4/5)A =0 (since there's no y² term in the original cubic)\n\nC - (4/5)B = -375\n\n-(4/5)C =44\n\nFrom the last equation: -(4/5)C =44 => C =44*(-5/4)= -55.\n\nThen, from third equation: C - (4/5)B = -375 => -55 - (4/5)B = -375 => -(4/5)B = -375 +55= -320 => (4/5)B =320 => B=320*(5/4)=400.\n\nFrom the second equation: B - (4/5)A=0. But B=400, and (4/5)A= (4/5)*500=400. So 400 -400=0, which checks out.\n\nTherefore, the cubic factors as:\n\n(y - 4/5)(500y² +400y -55)=0\n\nTherefore, the roots are y=4/5 and solutions to 500y² +400y -55=0.\n\nLet me solve 500y² +400y -55=0.\n\nQuadratic formula:\n\ny = [-400 ±sqrt(400² -4*500*(-55))]/(2*500)\n\nCompute discriminant:\n\n160000 + 4*500*55=160000 +4*27500=160000 +110000=270000\n\nsqrt(270000)=sqrt(270*1000)=sqrt(270)*sqrt(1000)=sqrt(9*30)*10*sqrt(10)=3sqrt(30)*10*sqrt(10)= but maybe just sqrt(270000)=300*sqrt(3) (since 270000=300²*3). Wait:\n\nsqrt(270000)=sqrt(270000)=sqrt(100*2700)=10*sqrt(2700)=10*sqrt(100*27)=10*10*sqrt(27)=100*3*sqrt(3)=300sqrt(3). Wait, 270000=270000=27*10000=27*(100)^2, so sqrt(27*100^2)=100*sqrt(27)=100*3*sqrt(3)=300sqrt(3). Yes, discriminant is 300sqrt(3).\n\nTherefore,\n\ny = [-400 ±300\\sqrt{3}]/1000 = [-400 ±300*1.732]/1000.\n\nBut let's keep it exact:\n\ny = [ -400 ± 300\\sqrt{3} ] / 1000 = [ -4 ± 3\\sqrt{3} ] /10\n\nCompute the two roots:\n\nFirst root: (-4 + 3√3)/10 ≈ (-4 +5.196)/10≈1.196/10≈0.1196\n\nSecond root: (-4 -3√3)/10≈ (-4 -5.196)/10≈-9.196/10≈-0.9196, which is negative.\n\nTherefore, the roots are y=4/5=0.8, y≈0.1196, and y≈-0.9196.\n\nBut since y=x²≥0, we discard the negative root. So the roots are y=0.8 and y≈0.1196.\n\nBut recall earlier that we were looking for y>0.5 (since x>sqrt(1/2)≈0.707 implies y=x²>0.5). So y=0.8 is in this interval, while y≈0.1196 is not. Therefore, the only relevant critical point is at y=0.8, i.e., x=sqrt(0.8)=√(4/5)=2/√5≈0.894.\n\nSo we have critical points at x=0, x=1 (endpoints) and x=2/√5≈0.894. Wait, but also check if y≈0.1196 is within the range for x. Since x can be from 0 to1, and y=x² is 0.1196, so x≈sqrt(0.1196)≈0.346, but earlier when we derived the derivative equation, we saw that critical points can only occur when x>sqrt(1/2)≈0.707. Therefore, the other critical point y≈0.1196 would correspond to x≈0.346, which is less than sqrt(1/2), so we had already determined that when x < sqrt(1/2), the equation 13*(1 - 2x²)/sqrt(...) + ... would not satisfy the sign condition. Therefore, the only critical point in the domain x in [0,1] is at x=2/√5.\n\nTherefore, the maximum must be either at x=0, x=1, or x=2/√5.\n\nCompute f(0):\n\nf(0) = 13*0 +9*0=0.\n\nf(1):\n\nf(1)=13*sqrt(1 -1) +9*sqrt(1 +1)=0 +9*sqrt(2)=9*1.414≈12.726, but we can keep it exact as 9√2.\n\nf(2/√5):\n\nCompute x=2/√5. So x²=4/5. Let's compute each term:\n\nFirst term:13x sqrt(1 -x²)\n\nx=2/√5, sqrt(1 -x²)=sqrt(1 -4/5)=sqrt(1/5)=1/√5\n\nSo first term:13*(2/√5)*(1/√5)=13*(2/(5))=26/5=5.2\n\nSecond term:9x sqrt(1 +x²)\n\nsqrt(1 +x²)=sqrt(1 +4/5)=sqrt(9/5)=3/√5\n\nSo second term:9*(2/√5)*(3/√5)=9*(6/5)=54/5=10.8\n\nTherefore, total f(2/√5)=26/5 +54/5=80/5=16.\n\nWait, that's nice. So f(2/√5)=16. Which is bigger than f(1)=9√2≈12.727, and f(0)=0. Therefore, the maximum value is 16.\n\nTherefore, the answer is 16.\n\nBut let me verify my calculation for x=2/√5.\n\nx=2/√5, x²=4/5.\n\nFirst term:13*sqrt(x² -x⁴)=13*sqrt(4/5 - (16/25))=13*sqrt( (20/25 -16/25) )=13*sqrt(4/25)=13*(2/5)=26/5=5.2.\n\nSecond term:9*sqrt(x² +x⁴)=9*sqrt(4/5 +16/25)=sqrt( (20/25 +16/25) )=sqrt(36/25)=6/5. So 9*(6/5)=54/5=10.8.\n\nSum:26/5 +54/5=80/5=16. Correct.\n\nTherefore, maximum is 16 at x=2/√5. Therefore, the answer is \\boxed{16}.\n</think>To find the maximum value of the expression \\(13\\sqrt{x^{2} - x^{4}} + 9\\sqrt{x^{2} + x^{4}}\\) for \\(x \\in [0, 1]\\), we start by simplifying the expression. Factor out \\(x^2\\) from under the square roots:\n\n\\[\n13\\sqrt{x^{2} - x^{4}} + 9\\sqrt{x^{2} + x^{4}} = 13x\\sqrt{1 - x^2} + 9x\\sqrt{1 + x^2}\n\\]\n\nThis can be written as:\n\n\\[\nf(x) = x \\left( 13\\sqrt{1 - x^2} + 9\\sqrt{1 + x^2} \\right)\n\\]\n\nTo find the critical points, we take the derivative of \\(f(x)\\) and set it to zero. The derivative is:\n\n\\[\nf'(x) = 13\\sqrt{1 - x^2} + 9\\sqrt{1 + x^2} + x \\left( \\frac{-13x}{\\sqrt{1 - x^2}} + \\frac{9x}{\\sqrt{1 + x^2}} \\right)\n\\]\n\nSetting \\(f'(x) = 0\\):\n\n\\[\n13\\sqrt{1 - x^2} + 9\\sqrt{1 + x^2} + x \\left( \\frac{-13x}{\\sqrt{1 - x^2}} + \\frac{9x}{\\sqrt{1 + x^2}} \\right) = 0\n\\]\n\nSimplifying and solving this equation, we find that the critical points are \\(x = 0\\), \\(x = 1\\), and \\(x = \\frac{2}{\\sqrt{5}}\\).\n\nWe now evaluate \\(f(x)\\) at these points:\n\n1. At \\(x = 0\\):\n\n\\[\nf(0) = 13 \\cdot 0 \\cdot \\sqrt{1 - 0^2} + 9 \\cdot 0 \\cdot \\sqrt{1 + 0^2} = 0\n\\]\n\n2. At \\(x = 1\\):\n\n\\[\nf(1) = 13 \\cdot 1 \\cdot \\sqrt{1 - 1^2} + 9 \\cdot 1 \\cdot \\sqrt{1 + 1^2} = 0 + 9\\sqrt{2} = 9\\sqrt{2} \\approx 12.727\n\\]\n\n3. At \\(x = \\frac{2}{\\sqrt{5}}\\):\n\n\\[\nf\\left( \\frac{2}{\\sqrt{5}} \\right) = \\frac{2}{\\sqrt{5}} \\left( 13\\sqrt{1 - \\left( \\frac{2}{\\sqrt{5}} \\right)^2} + 9\\sqrt{1 + \\left( \\frac{2}{\\sqrt{5}} \\right)^2} \\right)\n\\]\n\nCalculate the terms inside the parentheses:\n\n\\[\n1 - \\left( \\frac{2}{\\sqrt{5}} \\right)^2 = 1 - \\frac{4}{5} = \\frac{1}{5} \\quad \\text{and} \\quad 1 + \\left( \\frac{2}{\\sqrt{5}} \\right)^2 = 1 + \\frac{4}{5} = \\frac{9}{5}\n\\]\n\nThus,\n\n\\[\n13\\sqrt{\\frac{1}{5}} = 13 \\cdot \\frac{1}{\\sqrt{5}} = \\frac{13}{\\sqrt{5}}\n\\]\n\n\\[\n9\\sqrt{\\frac{9}{5}} = 9 \\cdot \\frac{3}{\\sqrt{5}} = \\frac{27}{\\sqrt{5}}\n\\]\n\nSo,\n\n\\[\nf\\left( \\frac{2}{\\sqrt{5}} \\right) = \\frac{2}{\\sqrt{5}} \\left( \\frac{13}{\\sqrt{5}} + \\frac{27}{\\sqrt{5}} \\right) = \\frac{2}{\\sqrt{5}} \\cdot \\frac{40}{\\sqrt{5}} = \\frac{80}{5} = 16\n\\]\n\nTherefore, the maximum value of the expression is \\(\\boxed{16}\\)."}