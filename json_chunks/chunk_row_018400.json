{"row_number": 18400, "expected_answer": "\\( F_X(x) = \\begin{cases} 0, & x < 4 \\\\ \\frac{1}{40} \\left( x^{3/2} - 8\\sqrt{x} + 8 \\right), & x \\in [4, 16] \\\\ 1, & x > 16 \\end{cases} \\)", "problem": "Given the probability density function \\( f(x) = \\begin{cases} \\frac{1}{80}\\left(3\\sqrt{x} - \\frac{8}{\\sqrt{x}}\\right), & x \\in [4,16] \\\\ 0, & \\mathrm{otherwise} \\end{cases} \\), find the distribution function of \\( X \\).", "generated_solution": "<think>\nOkay, let's see. I need to find the distribution function of X given the probability density function (pdf) f(x). The distribution function, also known as the cumulative distribution function (CDF), is the integral of the pdf from the lower limit of the domain up to some value x. \n\nFirst, let me recall the definition of the CDF. For a continuous random variable X with pdf f(x), the CDF F(x) is defined as:\n\nF(x) = P(X ≤ x) = ∫_{-∞}^x f(t) dt\n\nIn this problem, the pdf is given as f(x) = (1/80)(3√x - 8/√x) for x in [4, 16], and 0 otherwise. So, the pdf is non-zero only between 4 and 16. That means for x < 4, the CDF will be 0, and for x > 16, the CDF will be 1. For x between 4 and 16, I need to compute the integral of f(t) from 4 to x.\n\nLet me start by writing down the integral for F(x) when x is in [4, 16]:\n\nF(x) = ∫_{4}^{x} (1/80)(3√t - 8/√t) dt\n\nI need to compute this integral. Let's break it down:\n\nFirst, factor out the 1/80:\n\n(1/80) ∫_{4}^{x} (3√t - 8/√t) dt\n\nNow, split the integral into two parts:\n\n(1/80) [3 ∫_{4}^{x} √t dt - 8 ∫_{4}^{x} (1/√t) dt ]\n\nCompute each integral separately.\n\nLet's handle the first integral: ∫ √t dt. The integral of √t is ∫ t^(1/2) dt. The antiderivative of t^(1/2) is (t^(3/2))/(3/2) = (2/3) t^(3/2).\n\nSimilarly, the second integral: ∫ (1/√t) dt = ∫ t^(-1/2) dt. The antiderivative is (t^(1/2))/(1/2) = 2 t^(1/2).\n\nSo putting it all together:\n\n3 ∫ √t dt from 4 to x = 3 * [ (2/3) t^(3/2) ] evaluated from 4 to x = 3*( (2/3)x^(3/2) - (2/3)(4)^(3/2) ) \n\nSimplify:\n\nThe 3 cancels with the denominator 3, so we have 2x^(3/2) - 2*(4)^(3/2)\n\nSimilarly, the second integral:\n\n-8 ∫ (1/√t) dt from 4 to x = -8*[2 t^(1/2)] evaluated from 4 to x = -8*(2x^(1/2) - 2*(4)^(1/2)) = -16 x^(1/2) + 16*(4)^(1/2)\n\nNow, combine both parts:\n\nFirst part: 2x^(3/2) - 2*(4)^(3/2)\n\nSecond part: -16x^(1/2) + 16*(4)^(1/2)\n\nSo combining them all together, multiply by 1/80:\n\nF(x) = (1/80) [2x^(3/2) - 2*(4^(3/2)) -16x^(1/2) +16*(4^(1/2)) ]\n\nLet's compute 4^(3/2) and 4^(1/2):\n\n4^(1/2) = 2\n\n4^(3/2) = 4^(1) * 4^(1/2) = 4*2 = 8\n\nSo substitute these values:\n\nF(x) = (1/80) [2x^(3/2) - 2*8 -16x^(1/2) +16*2 ]\n\nCalculate the constants:\n\n-2*8 = -16\n\n16*2 = 32\n\nSo:\n\nF(x) = (1/80) [2x^(3/2) -16 -16x^(1/2) +32 ]\n\nCombine the constants:\n\n-16 +32 = 16\n\nSo:\n\nF(x) = (1/80) [2x^(3/2) -16x^(1/2) +16 ]\n\nFactor out a 2 from the terms inside the brackets:\n\n= (1/80)*2 [x^(3/2) -8x^(1/2) +8 ]\n\nSimplify 2/80 = 1/40:\n\n= (1/40) [x^(3/2) -8x^(1/2) +8 ]\n\nLet me check if this can be simplified further. Maybe factor the quadratic in terms of x^(1/2). Let me set y = x^(1/2), then x^(3/2) = y^3, and x^(1/2) = y. So the expression inside becomes:\n\ny^3 -8y +8\n\nHmm, can we factor this polynomial? Let's check if y=2 is a root:\n\n2^3 -8*2 +8 = 8 -16 +8 = 0. Yes, y=2 is a root.\n\nTherefore, factor out (y - 2):\n\nUsing polynomial division or synthetic division:\n\nDivide y^3 -8y +8 by (y - 2):\n\nCoefficients: 1 (y^3), 0 (y^2), -8 (y), 8 (constant term)\n\nUsing synthetic division for root y=2:\n\nBring down 1. Multiply by 2: 2. Add to next coefficient: 0 +2=2.\n\nMultiply 2 by 2=4. Add to next coefficient: -8 +4= -4.\n\nMultiply -4 by2= -8. Add to last coefficient: 8 + (-8)=0.\n\nSo the quotient is y^2 +2y -4.\n\nTherefore, y^3 -8y +8 = (y -2)(y^2 +2y -4)\n\nBut y^2 +2y -4 doesn't factor nicely (discriminant 4 +16=20, roots are (-2 ±√20)/2= -1 ±√5), so probably not helpful here. So maybe it's better to leave it as is.\n\nBut since we already have the expression in terms of x, let's write it back:\n\nx^(3/2) -8x^(1/2) +8 = (x^(1/2))^3 -8x^(1/2) +8 = (x^(1/2) -2)(x^(1/2)^2 +2x^(1/2) -4)\n\nBut not sure if that's helpful. Maybe it's better to just write the expression as it is.\n\nSo, going back to F(x):\n\nF(x) = (1/40)(x^(3/2) -8x^(1/2) +8) for 4 ≤ x ≤16\n\nAlso, check at the lower bound x=4:\n\nCompute F(4):\n\nx=4, so x^(3/2)=8, x^(1/2)=2\n\nSo F(4)= (1/40)(8 -8*2 +8)= (1/40)(8 -16 +8)=0, which is correct because the CDF should start at 0 when x=4.\n\nAt x=16:\n\nx=16, x^(3/2)=64, x^(1/2)=4\n\nF(16)= (1/40)(64 -8*4 +8) = (1/40)(64 -32 +8)= (1/40)(40)=1, which is correct since the total integral over [4,16] should be 1.\n\nSo that checks out. Therefore, the CDF is:\n\nF(x) = 0 for x <4,\n\nF(x)= (1/40)(x^(3/2) -8x^(1/2) +8) for 4 ≤x ≤16,\n\nand F(x)=1 for x>16.\n\nAlternatively, we can write the middle part in terms of square roots for clarity:\n\nx^(3/2)=x√x, and x^(1/2)=√x. So:\n\nF(x)= (1/40)(x√x -8√x +8)\n\nBut both forms are acceptable. However, the problem asks for the distribution function, so we need to write it as a piecewise function.\n\nSo putting it all together:\n\nF(x) = \n{\n0, for x <4,\n\n(1/40)(x^(3/2) -8x^(1/2) +8), for 4 ≤x ≤16,\n\n1, for x>16\n}\n\nAlternatively, using square root symbols:\n\n(1/40)(x√x -8√x +8)\n\nBut perhaps we can also write it in a factored form if possible. Let's check:\n\nFrom earlier, x^(3/2) -8x^(1/2) +8 = y^3 -8y +8 where y=√x. We had factored it as (y-2)(y^2 +2y -4). If we substitute back:\n\n(√x -2)(x +2√x -4)\n\nSo:\n\nF(x)= (1/40)(√x -2)(x +2√x -4)\n\nBut maybe that's more complicated. The original expanded form might be preferable.\n\nAlternatively, factor out √x from the first two terms?\n\nx^(3/2) -8x^(1/2) = x^(1/2)(x -8). So:\n\nx^(3/2) -8x^(1/2) +8 = x^(1/2)(x -8) +8\n\nBut that's not particularly helpful.\n\nSo perhaps it's best to leave it as is. Therefore, the final answer is the piecewise function as above.\n\nI should double-check my integration steps to ensure there's no mistake.\n\nOriginal integral:\n\n(1/80)[3*(2/3 x^(3/2) - 2/3 *8) -8*(2√x -2*2)]\n\nWait, let's verify that again.\n\nFirst integral: 3* [ (2/3)x^(3/2) evaluated from 4 to x ] =3*( (2/3 x^(3/2) - 2/3 *4^(3/2)) )\n\n=3*( (2/3 x^(3/2) - 2/3 *8) )\n\n=3*(2/3)(x^(3/2)-8 )\n\n=2*(x^(3/2)-8 )\n\nSimilarly, second integral: -8*[2√x -2√4] = -8*(2√x -4 )\n\n= -16√x +32\n\nAdding both parts:\n\n2x^(3/2) -16 -16√x +32 =2x^(3/2)-16√x +16, then times 1/80, which becomes (1/40)(x^(3/2)-8√x +8 )\n\nYes, that matches. So the integral was correctly calculated.\n\nTherefore, the distribution function is correct.\n\n**Final Answer**\nThe distribution function of \\( X \\) is \\( \\boxed{F(x) = \\begin{cases} 0, & x < 4 \\\\ \\frac{1}{40}\\left(x^{3/2} - 8x^{1/2} + 8\\right), & 4 \\leq x \\leq 16 \\\\ 1, & x > 16 \\end{cases}} \\).\n</think>To find the distribution function (CDF) of \\( X \\) given the probability density function (pdf) \\( f(x) \\), we integrate the pdf from the lower bound of the domain up to \\( x \\).\n\nThe pdf is given by:\n\\[ f(x) = \\begin{cases} \n\\frac{1}{80}\\left(3\\sqrt{x} - \\frac{8}{\\sqrt{x}}\\right), & x \\in [4, 16] \\\\ \n0, & \\text{otherwise} \n\\end{cases} \\]\n\nFor \\( x \\) in the interval \\([4, 16]\\), the CDF \\( F(x) \\) is computed as follows:\n\\[ F(x) = \\int_{4}^{x} \\frac{1}{80}\\left(3\\sqrt{t} - \\frac{8}{\\sqrt{t}}\\right) dt \\]\n\nBreaking down the integral:\n\\[ F(x) = \\frac{1}{80} \\left[ 3 \\int_{4}^{x} \\sqrt{t} \\, dt - 8 \\int_{4}^{x} \\frac{1}{\\sqrt{t}} \\, dt \\right] \\]\n\nCalculating each integral separately:\n1. The integral of \\( \\sqrt{t} \\):\n\\[ \\int \\sqrt{t} \\, dt = \\frac{2}{3} t^{3/2} \\]\nEvaluated from 4 to \\( x \\):\n\\[ 3 \\left[ \\frac{2}{3} x^{3/2} - \\frac{2}{3} \\cdot 8 \\right] = 2x^{3/2} - 16 \\]\n\n2. The integral of \\( \\frac{1}{\\sqrt{t}} \\):\n\\[ \\int \\frac{1}{\\sqrt{t}} \\, dt = 2\\sqrt{t} \\]\nEvaluated from 4 to \\( x \\):\n\\[ -8 \\left[ 2x^{1/2} - 4 \\right] = -16x^{1/2} + 32 \\]\n\nCombining these results:\n\\[ F(x) = \\frac{1}{80} \\left[ 2x^{3/2} - 16 - 16x^{1/2} + 32 \\right] \\]\n\\[ = \\frac{1}{80} \\left[ 2x^{3/2} - 16x^{1/2} + 16 \\right] \\]\n\\[ = \\frac{1}{40} \\left[ x^{3/2} - 8x^{1/2} + 8 \\right] \\]\n\nVerifying the boundary conditions:\n- At \\( x = 4 \\), \\( F(4) = \\frac{1}{40} \\left[ 4^{3/2} - 8 \\cdot 4^{1/2} + 8 \\right] = \\frac{1}{40} \\left[ 8 - 16 + 8 \\right] = 0 \\)\n- At \\( x = 16 \\), \\( F(16) = \\frac{1}{40} \\left[ 16^{3/2} - 8 \\cdot 16^{1/2} + 8 \\right] = \\frac{1}{40} \\left[ 64 - 32 + 8 \\right] = \\frac{40}{40} = 1 \\)\n\nThus, the distribution function is:\n\\[ F(x) = \\begin{cases} \n0, & x < 4 \\\\ \n\\frac{1}{40}\\left(x^{3/2} - 8x^{1/2} + 8\\right), & 4 \\leq x \\leq 16 \\\\ \n1, & x > 16 \n\\end{cases} \\]\n\n\\[\n\\boxed{F(x) = \\begin{cases} \n0, & x < 4 \\\\ \n\\frac{1}{40}\\left(x^{3/2} - 8x^{1/2} + 8\\right), & 4 \\leq x \\leq 16 \\\\ \n1, & x > 16 \n\\end{cases}}\n\\]"}