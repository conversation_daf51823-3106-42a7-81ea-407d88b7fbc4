{"row_number": 18774, "expected_answer": "2013", "problem": "Let \\( x_1, x_2, x_3, \\ldots, x_{2014} \\) be positive real numbers such that \\( \\sum_{j=1}^{2014} x_j = 1 \\). Determine the smallest constant \\( K \\) such that\n\\[ K \\sum_{j=1}^{2014} \\frac{x_j^2}{1 - x_j} \\geq 1. \\]", "generated_solution": "<think>\nOkay, so I need to find the smallest constant K such that the inequality K times the sum from j=1 to 2014 of (x_j squared)/(1 - x_j) is greater than or equal to 1, given that all the x_j are positive real numbers and their sum is 1. Hmm, this looks like an optimization problem where I have to minimize K such that the inequality holds for all possible positive x_j summing to 1. \n\nFirst, let me restate the problem to make sure I understand it. We have variables x1, x2, ..., x2014, all positive, adding up to 1. We need to find the minimal K where K multiplied by the sum of each x_j squared divided by (1 - x_j) is at least 1. So K is the minimal number that makes this inequality true no matter how we choose the x_j's as long as they satisfy the sum condition.\n\nI remember that inequalities like this often use techniques like C<PERSON>chy-<PERSON> or Lagrange multipliers. Since all the variables are positive and sum to 1, maybe some form of <PERSON><PERSON><PERSON>-<PERSON> or maybe even the method of Lagrange multipliers could be applicable here. Let me think about how to approach this.\n\nLet’s consider the expression we need to bound: sum_{j=1}^{2014} [x_j^2 / (1 - x_j)]. We need to find the minimal K such that K times this sum is >=1. So essentially, K must be the reciprocal of the maximum value of the sum. Wait, no. If we need K times the sum to be at least 1, then K must be at least 1 divided by the sum. But since we need this for all possible x_j, K has to be the smallest number that is greater or equal to 1 divided by the minimal possible sum. Wait, no, that seems contradictory. Let me clarify.\n\nLet me think again. The inequality is K * sum >=1. So rearranged, we need sum >=1/K. To ensure that the sum is always at least 1/K, regardless of the x_j's (which sum to 1), then the minimal K is the reciprocal of the minimal possible value of the sum. Wait, no, actually, if K is the minimal constant such that K * sum >=1 for all x_j, then K must be the supremum of 1/sum over all possible x_j. Because if the sum can get arbitrarily small, then 1/sum would get arbitrarily large, so K would have to be infinity. But since the x_j are positive and sum to 1, maybe the sum cannot be too small. So perhaps the minimal K is the maximum of 1/sum over all possible x_j. Therefore, we need to find the minimal K where K is the supremum of 1/sum, which is equivalent to K being 1 divided by the infimum of the sum. So the problem reduces to finding the minimal value of the sum S = sum_{j=1}^n [x_j^2 / (1 - x_j)] given that sum x_j =1 and x_j >0. Then K would be 1 divided by that minimal S.\n\nTherefore, the key is to find the minimal possible value of S, and then K is the reciprocal of that. So the problem becomes minimizing S = sum [x_j^2 / (1 - x_j)] under the constraint that sum x_j =1, x_j >0.\n\nHow do I minimize this sum? Let's consider if there's symmetry in the problem. If all variables are equal, then each x_j =1/2014. Let's compute S in that case. Each term would be (1/2014)^2 / (1 - 1/2014) = (1/2014^2) / (2013/2014) )= 1/(2014 * 2013). Then the sum over 2014 terms would be 2014 * [1/(2014*2013)] = 1/2013. So if all x_j are equal, S=1/2013, so K would have to be at least 2013 to satisfy K * (1/2013) >=1, which gives K>=2013. But is this the minimal K? Because maybe if some variables are larger and others are smaller, the sum S could be smaller? Or maybe larger? Wait, let's test with a different configuration.\n\nSuppose we have one variable close to 1 and the rest close to 0. Let's take x1 =1 - ε, where ε is small, and the other x_j = ε/(2013). Then compute S.\n\nFirst term: (1 - ε)^2 / (1 - (1 - ε)) = (1 - 2ε + ε^2)/ε ≈ (1 - 2ε)/ε ≈ 1/ε -2.\n\nThe other 2013 terms: each x_j^2 / (1 - x_j) ≈ [ (ε/2013)^2 ] / (1 - ε/2013 ) ≈ (ε^2 / 2013^2) /1 = ε^2 / 2013^2. So sum of these is 2013*( ε^2 / 2013^2 ) = ε^2 /2013. So total S ≈ (1/ε -2) + ε^2 /2013. As ε approaches 0, this expression approaches infinity. So S can be made arbitrarily large by making one variable approach 1. Therefore, the sum can be as large as possible, but we are interested in the minimal S.\n\nWait, but in the problem statement, we need K such that K*S >=1 for all x_j. So if S can be made arbitrarily large, then K can be arbitrarily small? No, but wait, no. Wait, in the problem, K must satisfy K*S >=1 for all x_j. If S can be made very large, then K can be very small, but since we need it to hold for all x_j, including those where S is minimal. Wait, actually, the problem is reversed: K needs to be such that even for the x_j that make S the smallest, K*S is still >=1. Therefore, K must be at least 1/(minimal S). So the minimal K is 1 divided by the minimal possible S. So if S can get as small as possible, then K needs to be large. But if S has a lower bound greater than zero, then K can be 1 over that lower bound.\n\nWait, this is a bit confusing. Let me think again.\n\nSuppose that the minimal value of S is m. Then, for all x_j, S >= m. Therefore, to have K*S >=1, we can take K >=1/m. Therefore, the minimal such K is 1/m. Therefore, the problem reduces to finding the minimal possible value of S, which is sum [x_j^2/(1 - x_j)] with sum x_j =1 and x_j>0.\n\nSo now the task is to find the minimal S. How to approach this?\n\nI can use Lagrange multipliers. Let me set up the optimization problem. We need to minimize S = sum_{j=1}^n [x_j^2/(1 - x_j)] subject to the constraint sum_{j=1}^n x_j =1, and x_j >0. Here n=2014.\n\nTo apply Lagrange multipliers, consider the Lagrangian function:\n\nL = sum_{j=1}^n [x_j^2/(1 - x_j)] - λ (sum_{j=1}^n x_j -1 )\n\nTake the derivative of L with respect to each x_j and set to zero.\n\nFor each x_j, the derivative is [ (2x_j (1 - x_j) + x_j^2 ) / (1 - x_j)^2 ] - λ = [ (2x_j - 2x_j^2 + x_j^2 ) / (1 - x_j)^2 ] - λ = [ (2x_j - x_j^2 ) / (1 - x_j)^2 ] - λ =0.\n\nSo, for each j, (2x_j - x_j^2 ) / (1 - x_j)^2 = λ.\n\nSince the problem is symmetric, maybe the minimum occurs when all variables are equal. Let's test that.\n\nIf all x_j =1/n, then plug into the derivative expression:\n\n(2*(1/n) - (1/n)^2 ) / (1 - 1/n)^2 = (2/n -1/n² ) / ( (n-1)/n )² ) = ( (2n -1)/n² ) / ( (n-1)^2 /n² ) ) = (2n -1)/(n -1)^2.\n\nIf all variables are equal, then each term in the derivative is equal, so λ would be the same for all, so this is a critical point. But is this a minimum?\n\nAlternatively, maybe the minimal S occurs when as many variables as possible are equal. Let's suppose that in the optimal case, all variables except one are equal. Wait, but with 2014 variables, this might get complicated. Let's consider a simpler case with two variables and see if the minimal is achieved when they are equal. Wait, but in the two-variable case, maybe the minimal is different. Wait, but in our problem, n=2014 is very large, so maybe the minimal is when all variables are equal, given the symmetry.\n\nAlternatively, suppose that in the minimal case, all variables are equal. Then S =n * [ (1/n)^2 / (1 -1/n) ] = n * [1/(n²(n -1)/n) ] = n * [1/(n(n -1)) ] =1/(n -1). For n=2014, that would give S=1/2013. Then K=2013.\n\nBut we need to check whether this is indeed the minimal value. Let's consider if making some variables larger and others smaller could lead to a smaller S.\n\nSuppose we have two variables, x and y, and the rest are zero. Wait, but all variables have to be positive. So if we set two variables to some values and the rest to ε approaching zero. Let’s suppose we have two variables x and y, and the rest are ε approaching zero. Then sum x + y + (n-2)ε =1. Let’s compute S.\n\nThe main terms would be x²/(1 -x) + y²/(1 - y) + (n -2) [ ε²/(1 - ε) ].\n\nAs ε approaches zero, the last term goes to zero. So we need to minimize x²/(1 -x) + y²/(1 - y) with x + y =1.\n\nWait, in this case, with two variables, x + y =1. Let's see what the minimal S would be.\n\nLet’s denote x + y =1, then y=1 -x. Then S becomes x²/(1 -x) + (1 -x)^2/x.\n\nLet’s compute this for x in (0,1).\n\nCompute derivative with respect to x:\n\nd/dx [ x²/(1 -x) + (1 -x)^2/x ]\n\nFirst term derivative: [2x(1 -x) +x² ]/(1 -x)^2 = [2x -2x² +x² ]/(1 -x)^2 = (2x -x²)/(1 -x)^2.\n\nSecond term derivative: [ -2(1 -x)x - (1 -x)^2 ]/x² = [ -2x +2x² - (1 -2x +x²) ]/x² = [ -2x +2x² -1 +2x -x² ]/x² = (x² -1)/x².\n\nSet the sum of derivatives to zero:\n\n(2x -x²)/(1 -x)^2 + (x² -1)/x² =0.\n\nThis seems complicated. Let me check at x=1/2:\n\nFirst term: (2*(1/2) - (1/2)^2)/(1 -1/2)^2 = (1 -1/4)/(1/2)^2 = (3/4)/(1/4) =3.\n\nSecond term: ((1/2)^2 -1)/( (1/2)^2 ) = (-3/4)/(1/4)= -3. So total 3 -3=0. So x=1/2 is a critical point. Let's check the second derivative or test around that point.\n\nAt x=0.6:\n\nFirst term: (2*0.6 -0.36)/(0.4)^2 = (1.2 -0.36)/0.16=0.84/0.16=5.25.\n\nSecond term: (0.36 -1)/0.36= (-0.64)/0.36≈-1.777. So total≈5.25 -1.777≈3.473>0.\n\nAt x=0.4:\n\nFirst term: (0.8 -0.16)/(0.6)^2=0.64/0.36≈1.777.\n\nSecond term: (0.16 -1)/0.16= (-0.84)/0.16≈-5.25. Total≈1.777 -5.25≈-3.473<0.\n\nSo the derivative goes from negative to positive as x increases through 0.5, indicating a minimum at x=1/2. Therefore, in the two-variable case, the minimal S is achieved when x=y=1/2. Then S= ( (1/2)^2 )/(1 -1/2) + ( (1/2)^2 )/(1 -1/2 )= (1/4)/(1/2) + (1/4)/(1/2)= 1/2 +1/2=1. So in two variables, the minimal S is 1, achieved when both variables are 1/2, giving K=1. But if all variables are equal, with two variables, S=2*( (1/2)^2 / (1 -1/2) )= 2*(1/4 /1/2 )=2*(1/2)=1. So same result. Therefore, in the two-variable case, equal distribution gives the minimal S. Maybe this generalizes.\n\nIn our original problem with 2014 variables, perhaps the minimal S is achieved when all variables are equal, leading to S=1/(n-1), hence K=n-1=2013. Let's check another case.\n\nSuppose we have three variables. Let's set all three equal: x=1/3. Then S=3*( (1/3)^2 / (1 -1/3) )=3*(1/9 / (2/3))=3*(1/9 *3/2)=3*(1/6)=1/2. If we set two variables to 1/2 and one to -0 (but variables must be positive). Wait, but they have to be positive. Suppose we set two variables to 1/2 - ε and one to 2ε. Let ε approach 0. Then compute S:\n\nFirst two terms: ( (1/2 - ε)^2 ) / (1 - (1/2 - ε) ) = (1/4 - ε + ε^2)/(1/2 + ε ) ≈ (1/4)/(1/2) =1/2 each. So two terms ≈1/2 each.\n\nThird term: (2ε)^2 / (1 -2ε ) ≈4ε² /1≈0. So total S≈1. Whereas when all variables are equal, S=1/2. So the sum is smaller when variables are equal. Therefore, this suggests that the minimal S occurs when variables are equal.\n\nTherefore, in general, perhaps distributing the sum equally among all variables gives the minimal S. Therefore, the minimal S is 1/(n-1), hence K=n-1. For n=2014, K=2013. So the answer is \\boxed{2013}.\n\nBut wait, let me test with three variables. Suppose instead of equal, we have two variables at a and one at 1 -2a. Let's find the minimal S.\n\nS = 2*(a²/(1 -a)) + ((1 -2a)^2)/(2a). Let’s set derivative with respect to a to zero.\n\nFirst term derivative: 2*[2a(1 -a) +a²]/(1 -a)^2 =2*(2a -2a² +a²)/(1 -a)^2=2*(2a -a²)/(1 -a)^2.\n\nSecond term derivative: [ -4a(1 -2a) + (-2)(2a) ]/(2a)^2? Wait, maybe compute derivative step by step.\n\nLet f(a) = (1 -2a)^2 / (2a).\n\nThen f’(a) = [ 2(1 -2a)(-2)*2a - (1 -2a)^2 *2 ] / (2a)^2.\n\nWait, perhaps better to rewrite f(a):\n\nf(a) = (1 -2a)^2 / (2a) = (1 -4a +4a²)/(2a) =1/(2a) -2 +2a.\n\nThen f’(a) = -1/(2a²) +2.\n\nSo total derivative:\n\nFor S =2*(a²/(1 -a)) + (1 -2a)^2/(2a),\n\ndS/da = 2*[ (2a(1 -a) +a² )/(1 -a)^2 ] + [ -1/(2a²) +2 ]\n\n= 2*(2a -2a² +a²)/(1 -a)^2 + (-1/(2a²) +2 )\n\n= 2*(2a -a²)/(1 -a)^2 -1/(2a²) +2.\n\nSet derivative to zero:\n\n2*(2a -a²)/(1 -a)^2 -1/(2a²) +2 =0.\n\nThis equation is quite complicated, but let's test a=1/3.\n\nPlug a=1/3:\n\nFirst term: 2*(2*(1/3) - (1/3)^2 )/(1 -1/3)^2 =2*(2/3 -1/9)/(4/9)=2*(5/9)/(4/9)=2*(5/4)=10/4=2.5.\n\nSecond term: -1/(2*(1/9)) +2= -9/2 +2= -4.5 +2= -2.5.\n\nSo total:2.5 -2.5 +0=0. Therefore, a=1/3 is a critical point.\n\nCheck if this is a minimum. Let’s compute S at a=1/3:\n\nS=2*( (1/3)^2 / (2/3) ) + (1 - 2/3)^2/(2/3)=2*(1/9 / 2/3) + (1/9)/(2/3)=2*(1/6) + (1/9)*(3/2)=1/3 +1/6=1/2.\n\nIf we take a=1/4:\n\nS=2*( (1/4)^2 / (3/4) ) + (1 - 2*(1/4))^2/(2*(1/4))=2*(1/16 / 3/4) + (1/2)^2 / (1/2)=2*(1/12) + (1/4)/(1/2)=1/6 +1/2=2/3 +1/2=Wait, 1/6 is approximately 0.1667, 1/2 is 0.5, total 0.6667, which is larger than 1/2. So S is larger here. Similarly, if a=1/5:\n\nS=2*( (1/5)^2 / (4/5) ) + (1 - 2/5)^2/(2/5)=2*(1/25 /4/5) + (9/25)/(2/5)=2*(1/20) +9/25*(5/2)=1/10 +9/10=10/10=1, which is even larger. Therefore, the minimal S occurs at a=1/3, giving S=1/2, which matches the equal distribution case (each variable 1/3). Therefore, even with three variables, the minimal S is achieved when all variables are equal.\n\nThis suggests that, in general, the minimal S occurs when all variables are equal. Therefore, for n variables, S_min =n*( (1/n)^2 / (1 -1/n) )=n*(1/n² / ((n-1)/n))=n*(1/(n(n-1)))=1/(n-1). Therefore, K=1/S_min=n-1.\n\nThus, for n=2014, K=2013. So the answer should be \\boxed{2013}.\n\n**Final Answer**\n\\boxed{2013}\n</think>To determine the smallest constant \\( K \\) such that\n\\[ K \\sum_{j=1}^{2014} \\frac{x_j^2}{1 - x_j} \\geq 1 \\]\ngiven that \\( \\sum_{j=1}^{2014} x_j = 1 \\) and each \\( x_j \\) is a positive real number, we need to find the minimum value of the sum \\( \\sum_{j=1}^{2014} \\frac{x_j^2}{1 - x_j} \\).\n\nFirst, consider the case where all \\( x_j \\) are equal. If each \\( x_j = \\frac{1}{2014} \\), then the sum becomes:\n\\[\n\\sum_{j=1}^{2014} \\frac{\\left(\\frac{1}{2014}\\right)^2}{1 - \\frac{1}{2014}} = 2014 \\cdot \\frac{\\frac{1}{2014^2}}{\\frac{2013}{2014}} = 2014 \\cdot \\frac{1}{2014 \\cdot 2013} = \\frac{1}{2013}.\n\\]\n\nTo confirm that this is indeed the minimum value, we use the method of Lagrange multipliers. The function we want to minimize is:\n\\[\nf(x_1, x_2, \\ldots, x_{2014}) = \\sum_{j=1}^{2014} \\frac{x_j^2}{1 - x_j}\n\\]\nsubject to the constraint:\n\\[\ng(x_1, x_2, \\ldots, x_{2014}) = \\sum_{j=1}^{2014} x_j - 1 = 0.\n\\]\n\nThe Lagrangian function is:\n\\[\n\\mathcal{L}(x_1, x_2, \\ldots, x_{2014}, \\lambda) = \\sum_{j=1}^{2014} \\frac{x_j^2}{1 - x_j} + \\lambda \\left( \\sum_{j=1}^{2014} x_j - 1 \\right).\n\\]\n\nTaking the partial derivatives and setting them to zero, we get:\n\\[\n\\frac{\\partial \\mathcal{L}}{\\partial x_j} = \\frac{2x_j (1 - x_j) + x_j^2}{(1 - x_j)^2} + \\lambda = 0 \\quad \\text{for each } j,\n\\]\nwhich simplifies to:\n\\[\n\\frac{2x_j - x_j^2}{(1 - x_j)^2} + \\lambda = 0.\n\\]\n\nSince the problem is symmetric, the critical points occur when all \\( x_j \\) are equal. Thus, \\( x_j = \\frac{1}{2014} \\) for all \\( j \\), and we have already calculated that the sum in this case is \\( \\frac{1}{2013} \\).\n\nTo ensure this is the minimum, we consider other configurations. For example, if one variable is close to 1 and the others are close to 0, the sum can become very large. Therefore, the minimum value of the sum is indeed \\( \\frac{1}{2013} \\).\n\nThus, the smallest constant \\( K \\) is the reciprocal of this minimum value:\n\\[\nK = \\frac{1}{\\frac{1}{2013}} = 2013.\n\\]\n\nThe final answer is \\(\\boxed{2013}\\)."}