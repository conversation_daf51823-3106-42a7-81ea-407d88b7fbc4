{"row_number": 19220, "expected_answer": "0", "problem": "Find the limit of the sequence \\( a_n \\) where \\( a_n = \\prod_{k=1}^n \\left(1 - \\frac{1}{2k}\\right) \\).", "generated_solution": "<think>\nOkay, so I have this problem where I need to find the limit of the sequence \\( a_n = \\prod_{k=1}^n \\left(1 - \\frac{1}{2k}\\right) \\). Hmm, product of terms from k=1 to n. Let me think about how to approach this.\n\nFirst, I remember that products can sometimes be converted into sums by taking the logarithm. That might make it easier to handle. Let me try that. If I take the natural logarithm of \\( a_n \\), that would turn the product into a sum:\n\n\\( \\ln a_n = \\sum_{k=1}^n \\ln\\left(1 - \\frac{1}{2k}\\right) \\).\n\nNow, I need to evaluate the limit of this sum as n approaches infinity. If I can find the sum, then exponentiating the result will give me the limit of \\( a_n \\).\n\nBut wait, how do I handle the sum \\( \\sum_{k=1}^\\infty \\ln\\left(1 - \\frac{1}{2k}\\right) \\)? That seems tricky. Maybe I can use some approximation for the logarithm. For small x, \\( \\ln(1 - x) \\approx -x - \\frac{x^2}{2} - \\frac{x^3}{3} - \\dots \\), but here x is \\( \\frac{1}{2k} \\), which becomes small as k increases. So maybe for large k, the terms can be approximated by their first-order term. But if I do that, the sum would look like \\( -\\sum_{k=1}^\\infty \\frac{1}{2k} \\), which is the harmonic series multiplied by -1/2, and we know the harmonic series diverges. That would imply the logarithm goes to negative infinity, so the original product would go to zero. But is this correct?\n\nWait, but approximating the logarithm might not capture the behavior correctly. Let me check with specific values. Let's compute a few terms manually.\n\nFor n=1: \\( a_1 = 1 - 1/2 = 1/2 = 0.5 \\)\n\nn=2: \\( a_2 = (1 - 1/2)(1 - 1/4) = (1/2)(3/4) = 3/8 = 0.375 \\)\n\nn=3: \\( a_3 = (1/2)(3/4)(5/6) = 15/48 = 0.3125 \\)\n\nn=4: (1/2)(3/4)(5/6)(7/8) = 105/384 ≈ 0.2734\n\nn=5: 105/384 * 9/10 = 945/3840 ≈ 0.2461\n\nHmm, the terms are decreasing each time. So maybe the limit is zero? But I need to verify this more rigorously.\n\nAlternatively, maybe I can express the product in terms of factorials or gamma functions. Let me see. Each term is \\( 1 - \\frac{1}{2k} = \\frac{2k - 1}{2k} \\). So the product becomes:\n\n\\( \\prod_{k=1}^n \\frac{2k - 1}{2k} = \\prod_{k=1}^n \\frac{(2k - 1)}{2k} \\).\n\nThat looks similar to the Wallis product for π/2, but I'm not sure. Let me write out the product:\n\n\\( \\frac{1}{2} \\times \\frac{3}{4} \\times \\frac{5}{6} \\times \\dots \\times \\frac{2n - 1}{2n} \\).\n\nThis is the same as \\( \\frac{(2n)!}{(2^n n!)^2} \\). Wait, let me check that.\n\nThe product of the first n terms (2k -1) is 1 * 3 * 5 * ... * (2n -1) which is equal to \\( \\frac{(2n)!}{2^n n!} \\). Because the product of even numbers up to 2n is 2*4*6*...*2n = 2^n n!, so the product of odd numbers is \\( \\frac{(2n)!}{2^n n!} \\). Therefore, the product \\( \\prod_{k=1}^n (2k -1) = \\frac{(2n)!}{2^n n!} \\).\n\nTherefore, the original product is \\( \\frac{(2n)!}{(2^n n!)^2} \\). So, \\( a_n = \\frac{(2n)!}{(2^n n!)^2} \\).\n\nNow, perhaps using Stirling's approximation to approximate the factorials. Stirling's formula is \\( n! \\approx \\sqrt{2\\pi n} \\left( \\frac{n}{e} \\right)^n \\).\n\nLet me apply this to (2n)! and (n!)^2.\n\nFirst, approximate (2n)!:\n\n\\( (2n)! \\approx \\sqrt{2\\pi (2n)} \\left( \\frac{2n}{e} \\right)^{2n} = \\sqrt{4\\pi n} \\left( \\frac{2n}{e} \\right)^{2n} = 2 \\sqrt{\\pi n} \\left( \\frac{2n}{e} \\right)^{2n} \\).\n\nThen, approximate (2^n n!)^2:\n\nFirst, n! ≈ \\( \\sqrt{2\\pi n} \\left( \\frac{n}{e} \\right)^n \\), so 2^n n! ≈ \\( 2^n \\sqrt{2\\pi n} \\left( \\frac{n}{e} \\right)^n \\).\n\nTherefore, (2^n n!)^2 ≈ \\( (2^n)^2 (2\\pi n) \\left( \\frac{n}{e} \\right)^{2n} = 2^{2n} \\cdot 2\\pi n \\cdot \\left( \\frac{n}{e} \\right)^{2n} \\).\n\nWait, hold on. Let me recheck:\n\nWait, (2^n n!)^2 = (2^n)^2 * (n!)^2 = 4^n * (n!)^2. So applying Stirling's approximation:\n\n\\( (n!)^2 \\approx ( \\sqrt{2\\pi n} (n/e)^n )^2 = 2\\pi n (n/e)^{2n} \\).\n\nThus, (2^n n!)^2 ≈ 4^n * 2\\pi n (n/e)^{2n} = 2\\pi n (4^n)(n^{2n}/e^{2n}).\n\nBut the numerator (2n)! approximated was:\n\n\\( 2\\sqrt{\\pi n} (2n/e)^{2n} \\).\n\nTherefore, the ratio \\( \\frac{(2n)!}{(2^n n!)^2} \\) becomes:\n\n\\( \\frac{2\\sqrt{\\pi n} (2n/e)^{2n}}{2\\pi n \\cdot 4^n (n/e)^{2n}} \\).\n\nSimplify numerator and denominator:\n\nNumerator: \\( 2\\sqrt{\\pi n} \\times (2n)^{2n}/e^{2n} \\).\n\nDenominator: \\( 2\\pi n \\times 4^n \\times n^{2n}/e^{2n} \\).\n\nLet's write 4^n as (2^2)^n = 2^{2n}.\n\nSo denominator: \\( 2\\pi n \\times 2^{2n} \\times n^{2n}/e^{2n} \\).\n\nNow, numerator: 2√(πn) * (2n)^{2n}/e^{2n} = 2√(πn) * 2^{2n} n^{2n}/e^{2n}.\n\nTherefore, the ratio is:\n\n[2√(πn) * 2^{2n} n^{2n}/e^{2n}] / [2π n * 2^{2n} n^{2n}/e^{2n}] ]\n\nNotice that 2^{2n}, n^{2n}, and e^{2n} all cancel out, leaving:\n\n[2√(πn)] / [2π n] = [√(πn)] / [π n] = 1 / (√(πn)).\n\nTherefore, the ratio \\( \\frac{(2n)!}{(2^n n!)^2} \\approx \\frac{1}{\\sqrt{\\pi n}} \\).\n\nTherefore, as n approaches infinity, \\( a_n \\approx \\frac{1}{\\sqrt{\\pi n}} \\), which tends to zero. Therefore, the limit is zero.\n\nAlternatively, maybe I made a miscalculation here, but this seems to align with the numerical examples earlier where the terms were decreasing towards zero. Let me verify the approximation.\n\nWait, Stirling's approximation is asymptotic, so as n becomes large, the approximation becomes better. Therefore, if \\( a_n \\approx \\frac{1}{\\sqrt{\\pi n}} \\), then as n → ∞, \\( a_n \\) tends to zero. Therefore, the limit of the sequence is zero.\n\nBut let me cross-verify using another method. Perhaps using the logarithm approach.\n\nEarlier, we had \\( \\ln a_n = \\sum_{k=1}^n \\ln\\left(1 - \\frac{1}{2k}\\right) \\). Let's approximate this sum for large n.\n\nWe can use the expansion \\( \\ln(1 - x) \\approx -x - \\frac{x^2}{2} - \\frac{x^3}{3} - \\dots \\). For x = 1/(2k), which is small when k is large, so the first term approximation is -1/(2k). But if we sum that from k=1 to n, we get - (1/2) H_n, where H_n is the nth harmonic number. H_n ≈ ln n + γ + o(1), so the sum would be approximately - (1/2)(ln n + γ). Therefore, \\( \\ln a_n \\approx - (1/2)(ln n + γ) \\), exponentiating gives \\( a_n \\approx e^{ - (1/2)(ln n + γ) } = e^{-γ/2} / \\sqrt{n} \\). Comparing with the previous result from Stirling's formula, which was \\( 1/\\sqrt{\\pi n} \\), this suggests that maybe there is a constant factor discrepancy. Wait, but in the first approach, we found \\( 1/\\sqrt{\\pi n} \\), and here it's \\( e^{-γ/2}/\\sqrt{n} \\). Are these consistent?\n\nWait, maybe the expansion using the logarithm is missing higher-order terms? Because when we approximate \\( \\ln(1 - x) \\) as -x, but in reality, the sum would be \\( \\sum_{k=1}^n \\left( -\\frac{1}{2k} - \\frac{1}{8k^2} - \\dots \\right) \\). The first term gives - (1/2) H_n, the next term would be -1/8 \\sum 1/k^2, which converges to -π^2/48 as n→infty. So the logarithm would be approximately - (1/2)(ln n + γ) - π^2/48 + ... So exponentiating gives \\( e^{- (1/2)(ln n + γ) - π^2/48 + ... } = e^{-γ/2 - π^2/48} / \\sqrt{n} \\). However, the leading term is still 1/\\sqrt{n}, so regardless of the constant factors, as n approaches infinity, a_n tends to zero.\n\nTherefore, both methods suggest that the limit is zero. The numerical examples also seem to confirm that the terms are getting smaller and approaching zero. Thus, I can conclude that the limit of the sequence \\( a_n \\) is zero.\n\n**Final Answer**\nThe limit of the sequence is \\boxed{0}.\n</think>To find the limit of the sequence \\( a_n = \\prod_{k=1}^n \\left(1 - \\frac{1}{2k}\\right) \\), we start by converting the product into a sum using the natural logarithm:\n\n\\[\n\\ln a_n = \\sum_{k=1}^n \\ln\\left(1 - \\frac{1}{2k}\\right)\n\\]\n\nFor large \\( k \\), we can approximate \\( \\ln\\left(1 - \\frac{1}{2k}\\right) \\) using the first-order Taylor expansion:\n\n\\[\n\\ln\\left(1 - \\frac{1}{2k}\\right) \\approx -\\frac{1}{2k}\n\\]\n\nThus, the sum becomes:\n\n\\[\n\\ln a_n \\approx -\\frac{1}{2} \\sum_{k=1}^n \\frac{1}{k}\n\\]\n\nThe sum \\( \\sum_{k=1}^n \\frac{1}{k} \\) is the \\( n \\)-th harmonic number \\( H_n \\), which can be approximated for large \\( n \\) as:\n\n\\[\nH_n \\approx \\ln n + \\gamma\n\\]\n\nwhere \\( \\gamma \\) is the Euler-Mascheroni constant. Substituting this approximation, we get:\n\n\\[\n\\ln a_n \\approx -\\frac{1}{2} (\\ln n + \\gamma)\n\\]\n\nExponentiating both sides, we find:\n\n\\[\na_n \\approx e^{-\\frac{1}{2} (\\ln n + \\gamma)} = e^{-\\frac{\\gamma}{2}} \\cdot e^{-\\frac{1}{2} \\ln n} = e^{-\\frac{\\gamma}{2}} \\cdot n^{-\\frac{1}{2}} = \\frac{e^{-\\frac{\\gamma}{2}}}{\\sqrt{n}}\n\\]\n\nAs \\( n \\to \\infty \\), the term \\( \\frac{1}{\\sqrt{n}} \\) approaches zero, so:\n\n\\[\n\\lim_{n \\to \\infty} a_n = 0\n\\]\n\nTo further confirm, we express the product using factorials and apply Stirling's approximation. The product can be written as:\n\n\\[\na_n = \\frac{(2n)!}{(2^n n!)^2}\n\\]\n\nUsing Stirling's approximation \\( n! \\approx \\sqrt{2\\pi n} \\left( \\frac{n}{e} \\right)^n \\), we approximate the factorials:\n\n\\[\n(2n)! \\approx \\sqrt{4\\pi n} \\left( \\frac{2n}{e} \\right)^{2n}\n\\]\n\\[\n(2^n n!)^2 \\approx 2^{2n} \\left( \\sqrt{2\\pi n} \\left( \\frac{n}{e} \\right)^n \\right)^2 = 2^{2n} \\cdot 2\\pi n \\left( \\frac{n}{e} \\right)^{2n}\n\\]\n\nThus, the ratio is:\n\n\\[\na_n \\approx \\frac{\\sqrt{4\\pi n} \\left( \\frac{2n}{e} \\right)^{2n}}{2^{2n} \\cdot 2\\pi n \\left( \\frac{n}{e} \\right)^{2n}} = \\frac{\\sqrt{4\\pi n} \\cdot (2n)^{2n} / e^{2n}}{2^{2n} \\cdot 2\\pi n \\cdot n^{2n} / e^{2n}} = \\frac{\\sqrt{4\\pi n} \\cdot 2^{2n} n^{2n} / e^{2n}}{2^{2n} \\cdot 2\\pi n \\cdot n^{2n} / e^{2n}} = \\frac{\\sqrt{4\\pi n}}{2\\pi n} = \\frac{2\\sqrt{\\pi n}}{2\\pi n} = \\frac{1}{\\sqrt{\\pi n}}\n\\]\n\nAs \\( n \\to \\infty \\), the term \\( \\frac{1}{\\sqrt{\\pi n}} \\) also approaches zero, confirming that:\n\n\\[\n\\lim_{n \\to \\infty} a_n = 0\n\\]\n\nThus, the limit of the sequence \\( a_n \\) is:\n\n\\[\n\\boxed{0}\n\\]"}