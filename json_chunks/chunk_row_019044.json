{"row_number": 19044, "expected_answer": "1287", "problem": "How many 5-digit positive integers $\\overline{x_1x_2x_3x_4x_5}$ are there such that $x_1 \\le x_2 \\le x_3 \\le x_4 \\le x_5$?", "generated_solution": "<think>\nAlright, so I need to figure out how many 5-digit numbers there are where each digit is non-decreasing. That is, the digits satisfy x₁ ≤ x₂ ≤ x₃ ≤ x₄ ≤ x₅. Hmm, okay. Let's think about this step by step.\n\nFirst off, a 5-digit number can't start with a zero, so the first digit x₁ has to be at least 1. The rest of the digits can be from 0 to 9, but each subsequent digit has to be greater than or equal to the previous one. So, for example, numbers like 11234 or 12344 would be valid, but something like 13245 wouldn't because 3 isn't ≤ 2.\n\nWait, but how do I count all such numbers without having to list them all? That seems impossible since there could be thousands. There must be a combinatorial method for this.\n\nI remember something about combinations with repetition. Let me recall. The formula for combinations with repetition is C(n + k - 1, k), where n is the number of types of items and k is the number of items to choose. But how does that apply here?\n\nIn this problem, the digits have to be non-decreasing, which is similar to arranging items where order matters but with repetition allowed. Since the digits are non-decreasing, once we choose the digits, their order is determined. So, essentially, we're choosing 5 digits where each is at least as large as the previous one. But since the first digit can't be zero, we need to adjust for that.\n\nWait, let's think differently. Suppose we consider the digits x₁, x₂, x₃, x₄, x₅ such that 1 ≤ x₁ ≤ x₂ ≤ x₃ ≤ x₅ ≤ x₅ ≤ 9. Wait, no. Because x₁ is from 1 to 9, and the others can be from x₁ to 9, but each subsequent digit can be equal or larger. But maybe we can model this as a stars and bars problem?\n\nStars and bars is used for distributing identical objects into distinct bins, but here we want to count the number of non-decreasing sequences. Hmm. Let's see. If we have digits that can repeat, but are non-decreasing, this is equivalent to the number of multisets of size 5 where elements are digits from 1 to 9 (for the first digit) and 0 to 9 for the rest. Wait, but actually, since all digits after the first must be at least the first digit?\n\nNo, wait. If we have a non-decreasing sequence, then each digit must be at least the previous one. But the first digit is at least 1, so the subsequent digits can't be less than 1. So, actually, all digits are from 1 to 9, right? Wait, no. Because if the first digit is 1, the next can be 1 or higher, but if the first digit is 2, the next can be 2 or higher, and so on. But once the first digit is fixed, the rest are constrained.\n\nBut this seems complicated. Let me think of a transformation. Let's change variables to make it easier. Suppose we define y₁ = x₁ - 1, so that y₁ is from 0 to 8 (since x₁ was from 1 to 9). Then the remaining digits x₂, x₃, x₄, x₅ are from x₁ to 9, which would be equivalent to y₂ = x₂ - x₁, y₃ = x₃ - x₂, ..., but that might not simplify things.\n\nAlternatively, since the digits are non-decreasing, maybe we can model the problem by considering the number of times each digit appears. For example, if we have digits d₁ ≤ d₂ ≤ d₃ ≤ d₄ ≤ d₅, then the count would be the number of ways to choose 5 digits with repetition allowed, but with d₁ ≥ 1.\n\nWait, but that's not exactly correct. If we allowed leading zeros, the number would be C(10 + 5 -1, 5) = C(14,5) = 2002. But since we can't have leading zeros, we need to subtract the sequences where the first digit is zero. However, in the standard stars and bars for non-decreasing digits allowing leading zeros, the total is indeed C(10 + 5 -1,5) = 2002. But if we want numbers without leading zeros, then the first digit must be at least 1, so the count would be C(9 + 5 -1, 5). Wait, is that right?\n\nWait, hold on. If we model non-decreasing 5-digit numbers where digits can start with zero, that's the same as all multisets of size 5 from 0-9, which is C(10 + 5 -1,5). But since we need 5-digit numbers (so first digit cannot be zero), we need multisets of size 5 from 1-9, which would be C(9 +5 -1,5) = C(13,5) = 1287. But wait, is that accurate?\n\nWait, let me confirm. In stars and bars, the number of non-decreasing sequences of length k where each term is between 1 and n inclusive is C(n + k -1, k). So in our case, if digits are from 1-9 (since no leading zeros), then n=9, k=5. So C(9+5-1,5)=C(13,5)=1287. So is that the answer?\n\nBut wait, in the original problem, are the digits allowed to be from 0-9 except the first one? No, wait. If we have a non-decreasing sequence where the first digit is at least 1, then all subsequent digits are at least as big as the previous one. Therefore, once the first digit is 1, the next can be 1-9, but non-decreasing. So if the first digit is 1, the rest can be 1 or higher. If the first digit is 2, the rest can be 2 or higher, etc. So each such number is a multiset of 5 digits where the first digit is at least 1, but the rest can be equal or higher. Hmm.\n\nBut stars and bars can model this as combinations with repetition. Let's see. The problem is equivalent to selecting 5 digits with repetition allowed from the digits 1 through 9, where order doesn't matter, and then arranging them in non-decreasing order. But since the order is enforced by non-decreasing, the number of such numbers is exactly the number of multisets, which is C(9 +5 -1,5) = C(13,5). Calculating that: 13 choose 5 is 1287. So the answer is 1287?\n\nBut let me double-check with a simpler case. Let's take 2-digit numbers. How many non-decreasing 2-digit numbers are there? Using the formula, it should be C(9 + 2 -1, 2)=C(10,2)=45. Let's count manually. For two digits, x1 ≤ x2, x1 from 1-9, x2 from x1-9.\n\nFor x1=1, x2 can be 1-9: 9 numbers.\n\nx1=2, x2=2-9: 8 numbers.\n\n...\n\nx1=9, x2=9:1 number.\n\nTotal: 9+8+7+...+1 = 45. Which matches C(10,2). So that works.\n\nSimilarly, if we have 3-digit numbers, the formula would be C(9+3-1,3)=C(11,3)=165. Let's verify for x1=1, x2 can be 1-9, x3 can be x2-9. Wait, but this seems more complicated. Alternatively, the number of non-decreasing 3-digit numbers is equal to the sum from d=1 to 9 of the number of non-decreasing sequences of length 2 starting with d. For each d, the number of sequences is C( (9 - d +1) + 2 -1, 2 ). Wait, maybe not. Alternatively, perhaps the formula still holds because the transformation is valid.\n\nAlternatively, perhaps think of the digits as a combination with repetition. For example, choosing 3 digits with repetition from 1-9, which gives C(9 +3 -1, 3)=165. Let's take a smaller case, like 3-digit numbers where digits are non-decreasing. For numbers starting with 1, the next two digits can be 1-9, non-decreasing. The number of such sequences is C(9 + 2 -1,2)=C(10,2)=45. For numbers starting with 2, the next two digits can be 2-9: C(8 +2 -1,2)=C(9,2)=36. Then starting with 3: C(7 +2 -1,2)=C(8,2)=28. Continuing this way:\n\nStart with 1: C(10,2)=45\n\nStart with 2: C(9,2)=36\n\nStart with 3: C(8,2)=28\n\nStart with 4: C(7,2)=21\n\nStart with 5: C(6,2)=15\n\nStart with 6: C(5,2)=10\n\nStart with 7: C(4,2)=6\n\nStart with 8: C(3,2)=3\n\nStart with 9: C(2,2)=1\n\nAdding these up: 45+36=81, +28=109, +21=130, +15=145, +10=155, +6=161, +3=164, +1=165. Which matches the formula C(11,3)=165. So that works.\n\nTherefore, by analogy, the formula holds for 5-digit numbers. So the number of non-decreasing 5-digit numbers is C(9 +5 -1,5)=C(13,5)=1287.\n\nTherefore, the answer should be 1287.\n\nWait, but let me confirm another way. Sometimes when dealing with non-decreasing sequences, we can use stars and bars by considering the problem as placing 5 indistinct balls into 9 distinct boxes (digits 1-9), where the number of balls in each box represents how many times that digit appears. The number of ways is C(5 +9 -1,5)=C(13,5)=1287. Yep, that matches. So that seems to check out.\n\nAlternatively, considering the problem as a stars and bars where we are distributing 5 identical items (digit positions) into 9 bins (digits 1 through 9). Each bin corresponds to a digit, and the number of items in the bin is how many times that digit appears in the number. Since the digits must be non-decreasing, the order is determined by the digits, so each such distribution corresponds to exactly one valid number.\n\nSo the formula is C(n + k -1, k) where n=9 digits, k=5 positions. So 13 choose 5.\n\n13 choose 5 is calculated as 13! / (5! * 8!) = (13×12×11×10×9)/(5×4×3×2×1) = (13×12×11×10×9)/120.\n\nCalculating that:\n\n13×12 = 156\n\n156×11 = 1716\n\n1716×10 = 17160\n\n17160×9 = 154440\n\nDivide by 120: 154440 ÷ 120 = 154440 ÷ 10 ÷ 12 = 15444 ÷ 12 = 1287.\n\nSo 1287. That seems right.\n\nTherefore, the number of 5-digit positive integers with non-decreasing digits is 1287.\n\n**Final Answer**\nThe number of such 5-digit integers is \\boxed{1287}.\n</think>To determine the number of 5-digit positive integers \\(\\overline{x_1x_2x_3x_4x_5}\\) such that \\(x_1 \\le x_2 \\le x_3 \\le x_4 \\le x_5\\), we can use combinatorial methods.\n\nThis problem is equivalent to counting the number of non-decreasing sequences of length 5 where the first digit \\(x_1\\) is at least 1 (since it is a 5-digit number). This can be modeled using combinations with repetition, where we need to choose 5 digits from the set \\(\\{1, 2, \\ldots, 9\\}\\) with repetition allowed.\n\nThe formula for combinations with repetition is given by:\n\\[\n\\binom{n + k - 1}{k}\n\\]\nwhere \\(n\\) is the number of types of items to choose from (9 digits here) and \\(k\\) is the number of items to choose (5 digits here). Plugging in the values, we get:\n\\[\n\\binom{9 + 5 - 1}{5} = \\binom{13}{5}\n\\]\n\nNow, we calculate \\(\\binom{13}{5}\\):\n\\[\n\\binom{13}{5} = \\frac{13!}{5!(13-5)!} = \\frac{13!}{5! \\cdot 8!} = \\frac{13 \\times 12 \\times 11 \\times 10 \\times 9}{5 \\times 4 \\times 3 \\times 2 \\times 1}\n\\]\n\nBreaking down the calculation:\n\\[\n13 \\times 12 = 156\n\\]\n\\[\n156 \\times 11 = 1716\n\\]\n\\[\n1716 \\times 10 = 17160\n\\]\n\\[\n17160 \\times 9 = 154440\n\\]\n\nNext, we divide by \\(5!\\):\n\\[\n5! = 5 \\times 4 \\times 3 \\times 2 \\times 1 = 120\n\\]\n\nSo,\n\\[\n\\frac{154440}{120} = 1287\n\\]\n\nThus, the number of 5-digit positive integers with non-decreasing digits is \\(\\boxed{1287}\\)."}