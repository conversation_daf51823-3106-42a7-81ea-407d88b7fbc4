{"row_number": 18757, "expected_answer": "$-\\pi \\ln 2$", "problem": "Evaluate the integral $\\int_{0}^{\\pi} \\ln (1 + \\cos x)\\ dx$.", "generated_solution": "<think>\nOkay, so I have this integral to solve: the integral from 0 to π of ln(1 + cos x) dx. Hmm, let me think. I remember that integrals involving logarithmic functions can sometimes be tricky, but maybe there's a substitution or a symmetry I can use here.\n\nFirst off, the limits are from 0 to π, which is half the period of the cosine function. That makes me think about using symmetry properties. Let me recall if there's a substitution that can simplify this. Maybe substituting t = π - x? Let me try that.\n\nLet’s set t = π - x. Then, when x = 0, t = π, and when x = π, t = 0. The differential dx becomes -dt, but since the limits switch places, the integral becomes the integral from π to 0 of ln(1 + cos(π - t)) (-dt), which simplifies to the integral from 0 to π of ln(1 + cos(π - t)) dt. \n\nNow, cos(π - t) is equal to -cos t. So, the integral becomes the integral from 0 to π of ln(1 - cos t) dt. Wait, so that means the original integral I is equal to the integral of ln(1 - cos t) dt from 0 to π. Let me denote the original integral as I. Therefore, I = ∫₀^π ln(1 + cos x) dx = ∫₀^π ln(1 - cos x) dx. Hmm, interesting. So both these integrals are equal. Maybe I can add them together?\n\nAdding them, I get 2I = ∫₀^π [ln(1 + cos x) + ln(1 - cos x)] dx. Which simplifies to ∫₀^π ln[(1 + cos x)(1 - cos x)] dx. The product inside the log is 1 - cos²x, which is sin²x. So, 2I = ∫₀^π ln(sin²x) dx. The logarithm of a square can be written as 2 ln |sin x|. Since we're integrating from 0 to π, sin x is non-negative, so absolute value isn't needed. Therefore, 2I = 2 ∫₀^π ln(sin x) dx. Dividing both sides by 2, we get I = ∫₀^π ln(sin x) dx.\n\nOh, okay, so the original integral reduces to the integral of ln(sin x) from 0 to π. I remember there's a standard result for that integral. Let me recall. I think the integral of ln(sin x) from 0 to π is -π ln 2. Let me check that again. \n\nYes, there's a known integral: ∫₀^π ln(sin x) dx = -π ln 2. So if I = -π ln 2, then that's the answer. Wait, but let me verify why that integral is -π ln 2. How is that derived?\n\nOne way to compute ∫₀^π ln(sin x) dx is to use symmetry. Maybe split the integral into two parts, from 0 to π/2 and π/2 to π. Then, substitute x = π - t in the second integral. Then, the integral from π/2 to π becomes the integral from 0 to π/2 of ln(sin t) dt. So the entire integral is 2 ∫₀^{π/2} ln(sin x) dx.\n\nNow, the integral from 0 to π/2 of ln(sin x) dx is a standard result. I remember that it's equal to -(π/2) ln 2. Therefore, multiplying by 2 gives -π ln 2. So that checks out. Therefore, the original integral I is indeed -π ln 2.\n\nWait, let me confirm that step again. If ∫₀^{π/2} ln(sin x) dx = -(π/2) ln 2, then 2 times that is -π ln 2. So, yes, that would mean that I = -π ln 2. Therefore, the answer should be -π ln 2. But let me make sure I didn't make any mistakes in the substitution steps.\n\nStarting with substitution t = π - x. The original integral is I = ∫₀^π ln(1 + cos x) dx. After substitution, we get I = ∫₀^π ln(1 - cos t) dt. Then, adding I + I = 2I = ∫₀^π ln(1 - cos²t) dt = ∫₀^π ln(sin²t) dt = 2 ∫₀^π ln(sin t) dt. Then, 2I = 2 ∫₀^π ln(sin t) dt => I = ∫₀^π ln(sin t) dt, which is -π ln 2. So, yes, seems correct.\n\nAlternatively, maybe use a Fourier series expansion or some other method? Let me think if there's another way. Sometimes, integrals involving logarithms can be evaluated using series expansions. Let me try expanding ln(1 + cos x) into a Fourier series or a power series.\n\nBut maybe that's more complicated. Alternatively, recall that 1 + cos x = 2 cos²(x/2). Let's see: cos x = 2 cos²(x/2) - 1, so 1 + cos x = 2 cos²(x/2). Therefore, ln(1 + cos x) = ln(2 cos²(x/2)) = ln 2 + 2 ln(cos(x/2)). Therefore, the integral I becomes ∫₀^π [ln 2 + 2 ln(cos(x/2))] dx. That splits into ln 2 * π + 2 ∫₀^π ln(cos(x/2)) dx.\n\nLet me substitute t = x/2 in the integral. Then, when x = 0, t = 0; x = π, t = π/2. dx = 2 dt. Therefore, the integral becomes ln 2 * π + 2 * 2 ∫₀^{π/2} ln(cos t) dt. Which is ln 2 * π + 4 ∫₀^{π/2} ln(cos t) dt. Wait, but we know that ∫₀^{π/2} ln(cos t) dt = - (π/2) ln 2. Let me check that.\n\nYes, the integral from 0 to π/2 of ln(cos t) dt is equal to - (π/2) ln 2. So substituting that in, we get ln 2 * π + 4 * (- (π/2) ln 2) = π ln 2 - 2 π ln 2 = -π ln 2. Which matches the previous result. So that confirms the answer.\n\nAlternatively, another approach: using integration by parts. Let's try that. Let me set u = ln(1 + cos x), dv = dx. Then du = (-sin x)/(1 + cos x) dx, and v = x. So, integration by parts gives uv from 0 to π minus ∫₀^π v du. The uv term is x ln(1 + cos x) evaluated from 0 to π. At x=π, cos π = -1, so ln(1 + cos π) = ln(0), which is -infinity, but multiplied by π. Wait, that's undefined. Similarly, at x=0, cos 0 = 1, so ln(2). So the uv term would be π * ln(0) - 0 * ln(2). But ln(0) is -infinity, which suggests that integration by parts might not be the best approach here because it leads to an indeterminate form. Hmm, maybe not the right path. So, perhaps the earlier methods are better.\n\nSo, given that through substitution we've reduced the original integral to the known integral of ln(sin x) over 0 to π, which is -π ln 2, and confirmed via another substitution and expansion, I can be confident that the answer is -π ln 2.\n\nTherefore, the value of the integral ∫₀^π ln(1 + cos x) dx is -π ln 2.\n\n**Final Answer**\nThe value of the integral is \\boxed{ -\\pi \\ln 2 }.\n</think>To evaluate the integral \\(\\int_{0}^{\\pi} \\ln (1 + \\cos x)\\ dx\\), we start by using the substitution \\(t = \\pi - x\\). This transforms the integral as follows:\n\nWhen \\(x = 0\\), \\(t = \\pi\\), and when \\(x = \\pi\\), \\(t = 0\\). Also, \\(dx = -dt\\). Thus, the integral becomes:\n\n\\[\n\\int_{0}^{\\pi} \\ln(1 + \\cos x)\\ dx = \\int_{\\pi}^{0} \\ln(1 + \\cos(\\pi - t)) (-dt) = \\int_{0}^{\\pi} \\ln(1 - \\cos t)\\ dt\n\\]\n\nLet \\(I = \\int_{0}^{\\pi} \\ln(1 + \\cos x)\\ dx\\). Therefore, we also have:\n\n\\[\nI = \\int_{0}^{\\pi} \\ln(1 - \\cos x)\\ dx\n\\]\n\nAdding these two integrals, we get:\n\n\\[\n2I = \\int_{0}^{\\pi} \\left[ \\ln(1 + \\cos x) + \\ln(1 - \\cos x) \\right] dx\n\\]\n\nUsing the property of logarithms, we can combine the logarithms:\n\n\\[\n2I = \\int_{0}^{\\pi} \\ln \\left[(1 + \\cos x)(1 - \\cos x)\\right] dx = \\int_{0}^{\\pi} \\ln(1 - \\cos^2 x)\\ dx = \\int_{0}^{\\pi} \\ln(\\sin^2 x)\\ dx\n\\]\n\nSince \\(\\ln(\\sin^2 x) = 2 \\ln |\\sin x|\\) and \\(\\sin x \\geq 0\\) for \\(x \\in [0, \\pi]\\), we have:\n\n\\[\n2I = 2 \\int_{0}^{\\pi} \\ln(\\sin x)\\ dx\n\\]\n\nDividing both sides by 2, we find:\n\n\\[\nI = \\int_{0}^{\\pi} \\ln(\\sin x)\\ dx\n\\]\n\nThe integral \\(\\int_{0}^{\\pi} \\ln(\\sin x)\\ dx\\) can be evaluated by splitting it into two parts from \\(0\\) to \\(\\pi/2\\) and from \\(\\pi/2\\) to \\(\\pi\\):\n\n\\[\n\\int_{0}^{\\pi} \\ln(\\sin x)\\ dx = \\int_{0}^{\\pi/2} \\ln(\\sin x)\\ dx + \\int_{\\pi/2}^{\\pi} \\ln(\\sin x)\\ dx\n\\]\n\nFor the second integral, we use the substitution \\(t = \\pi - x\\):\n\n\\[\n\\int_{\\pi/2}^{\\pi} \\ln(\\sin x)\\ dx = \\int_{\\pi/2}^{0} \\ln(\\sin(\\pi - t)) (-dt) = \\int_{0}^{\\pi/2} \\ln(\\sin t)\\ dt\n\\]\n\nThus, we have:\n\n\\[\n\\int_{0}^{\\pi} \\ln(\\sin x)\\ dx = 2 \\int_{0}^{\\pi/2} \\ln(\\sin x)\\ dx\n\\]\n\nThe integral \\(\\int_{0}^{\\pi/2} \\ln(\\sin x)\\ dx\\) is a known result and equals \\(-\\frac{\\pi}{2} \\ln 2\\). Therefore:\n\n\\[\n\\int_{0}^{\\pi} \\ln(\\sin x)\\ dx = 2 \\left(-\\frac{\\pi}{2} \\ln 2\\right) = -\\pi \\ln 2\n\\]\n\nThus, the value of the integral is:\n\n\\[\n\\boxed{-\\pi \\ln 2}\n\\]"}