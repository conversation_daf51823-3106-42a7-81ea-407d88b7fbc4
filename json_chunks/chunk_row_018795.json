{"row_number": 18795, "expected_answer": "10", "problem": "Find the greatest integral value of \\( k \\) such that the polynomial \\( x^4 - 5x^2 - kx - 4 \\) has its lowest integral upper bound of the roots at \\( x = 3 \\).", "generated_solution": "<think>\nOkay, so I need to find the greatest integer value of k such that the polynomial x⁴ - 5x² - kx - 4 has its lowest integral upper bound of the roots at x = 3. Hmm, let me try to understand what the problem is asking here.\n\nFirst, I remember that the Upper Bound Theorem (or Upper and Lower Bounds of Roots) in algebra helps determine the intervals where all the real roots of a polynomial lie. Specifically, if we use the Upper Bound Theorem, we can find a number that is greater than or equal to all the real roots of the polynomial. The problem is saying that the lowest such integer upper bound is x = 3. So, that means 3 is the smallest integer where all real roots of the polynomial are less than or equal to 3. But we need to find the maximum integer k for which this is true.\n\nWait, so if 3 is the lowest integral upper bound, then if we used 2, it wouldn't be an upper bound anymore. Therefore, there must be a root greater than 2. But since 3 is the lowest integer upper bound, the actual upper bound is somewhere between 2 and 3. Wait, maybe I need to recall the Upper Bound Theorem more precisely.\n\nThe Upper Bound Theorem states that if we divide the polynomial by (x - c) using synthetic division and all the numbers in the last row (the coefficients of the quotient polynomial and the remainder) are positive, then c is an upper bound for the real roots of the polynomial. Conversely, if we use the Lower Bound Theorem, we can find a lower bound.\n\nBut here, we need the lowest integral upper bound to be 3. So that means when we test c=3 using synthetic division, the coefficients of the quotient and the remainder should all be non-negative. Additionally, if we test c=2, which is the next lower integer, synthetic division should give at least one negative number in the last row, indicating that 2 is not an upper bound. Therefore, the maximum k would be the largest integer where synthetic division with 3 gives all non-negative numbers, but synthetic division with 2 does not.\n\nSo the plan is: perform synthetic division of the polynomial x⁴ -5x² -k x -4 by (x - 3) and ensure that all coefficients in the quotient and the remainder are non-negative. Then check that for x=2, there's at least one negative coefficient. Then find the maximum k for which this holds.\n\nLet me set up the synthetic division for x=3.\n\nThe polynomial is x⁴ + 0x³ -5x² -k x -4. So the coefficients are [1, 0, -5, -k, -4]. Dividing by (x - 3):\n\nStart synthetic division:\n\n3 | 1   0   -5   -k   -4\n  |     3   9   12   3*(12 -k)\n  -------------------------------\n    1   <USER>   <GROUP>   (12 -k)   (3*(12 -k) -4)\n\nWait, maybe step by step:\n\nFirst, bring down the leading coefficient: 1.\n\nMultiply 1 by 3, add to the next coefficient (0): 0 + 3*1 = 3.\n\nMultiply 3 by 3, add to the next coefficient (-5): -5 + 3*3 = -5 +9 = 4.\n\nMultiply 4 by 3, add to the next coefficient (-k): -k + 4*3 = -k +12.\n\nMultiply (-k +12) by 3, add to the last coefficient (-4): -4 + 3*(-k +12) = -4 -3k +36 = 32 -3k.\n\nSo the result of synthetic division is [1, 3, 4, (12 -k), (32 -3k)].\n\nFor 3 to be an upper bound, all these coefficients must be non-negative. So:\n\n12 - k ≥ 0 ⇒ k ≤12\n\n32 -3k ≥ 0 ⇒ 3k ≤32 ⇒ k ≤32/3 ≈10.666...\n\nTherefore, the more restrictive condition is k ≤10.666..., so the maximum integer k is 10. But we need to check if k=10 satisfies the condition that all coefficients are non-negative.\n\nCheck k=10:\n\nThen the coefficients would be 1, 3, 4, 12 -10=2, and 32 -3*10=2. All non-negative. So k=10 is okay.\n\nBut we also need to check that 2 is not an upper bound. So for k=10, perform synthetic division with x=2.\n\nSo the polynomial is x⁴ +0x³ -5x² -10x -4. Coefficients [1, 0, -5, -10, -4]. Divided by (x -2):\n\n2 | 1   0   -5   -10   -4\n  |     2   4   -2   -24\n  --------------------------\n    1   <USER>   <GROUP>   -12   -28\n\nWait, the coefficients here are 1, 2, -1, -12, and remainder -28. So there are negative coefficients here, which means that 2 is not an upper bound. Therefore, with k=10, 3 is indeed the lowest integral upper bound.\n\nBut wait, the problem is asking for the greatest integral value of k. So maybe I need to check if k=10 is the maximum possible. Let me check k=11.\n\nBut according to the synthetic division with x=3, the last term was 32 -3k. So if k=10, that term is 32 -30=2. If k=11, then 32 -33= -1, which is negative. So 32 -3k ≥0 implies k ≤10.666..., so k=10 is the maximum integer. But wait, the quotient coefficients must all be non-negative. So when k=11, 12 -k =1, which is positive, but the last term is -1, which is negative. Therefore, 3 is not an upper bound for k=11. Thus, the maximum k is 10.\n\nWait, but to confirm, maybe I need to check with k=10, the synthetic division with 3 is all non-negative, which we saw. Then synthetic division with 2 gives negative coefficients, so 2 is not an upper bound, so 3 is indeed the lowest. Hence, the answer is 10.\n\nBut let me check again. Maybe there's another approach. Let's think about upper bounds. If x=3 is the upper bound, then all real roots are less than or equal to 3. Therefore, if we substitute x=3, the polynomial evaluated at 3 should be positive? Wait, no. The Upper Bound Theorem is about synthetic division coefficients.\n\nAlternatively, maybe using the Upper Bound Theorem, which states that if f(x) is a polynomial with real coefficients and a positive leading coefficient, then if you divide f(x) by (x - c) using synthetic division and all the coefficients in the quotient polynomial and the remainder are non-negative, then c is an upper bound for the real roots of f(x).\n\nSo in this case, if we want c=3 to be an upper bound, then the synthetic division must result in all non-negative coefficients. And for 3 to be the lowest such integer, then c=2 must not be an upper bound (i.e., when performing synthetic division with 2, there is at least one negative coefficient).\n\nTherefore, the key point is that for k=10, synthetic division with 3 gives [1,3,4,2,2], all non-negative. And synthetic division with 2 gives [1,2,-1,-12,-28], which has negatives, so 2 is not an upper bound. Hence, 3 is the lowest integral upper bound. Therefore, k=10 is the maximum.\n\nBut maybe I should check for k=10 whether the polynomial actually has roots above 2 but below 3. Because the Upper Bound Theorem tells us that there are no roots greater than 3, but there may be roots between 2 and 3, which would mean that 3 is still the lowest integral upper bound.\n\nBut in this case, as long as 3 is an upper bound and 2 is not, 3 is the minimal integer upper bound. So even if there are roots between 2 and 3, the problem is satisfied. So perhaps the approach is correct.\n\nTherefore, the answer is 10.\n\n**Final Answer**\n\\boxed{10}\n</think>To find the greatest integral value of \\( k \\) such that the polynomial \\( x^4 - 5x^2 - kx - 4 \\) has its lowest integral upper bound of the roots at \\( x = 3 \\), we use the Upper Bound Theorem. This theorem states that if dividing the polynomial by \\( (x - c) \\) using synthetic division results in all non-negative coefficients in the quotient and remainder, then \\( c \\) is an upper bound for the real roots. \n\nFor \\( x = 3 \\) to be the lowest integral upper bound, synthetic division by \\( (x - 3) \\) must yield all non-negative coefficients, and synthetic division by \\( (x - 2) \\) must yield at least one negative coefficient.\n\n### Step 1: Synthetic Division by \\( x - 3 \\)\nThe coefficients of the polynomial \\( x^4 - 5x^2 - kx - 4 \\) are \\( [1, 0, -5, -k, -4] \\).\n\n\\[\n\\begin{array}{r|rrrrr}\n3 & 1 & 0 & -5 & -k & -4 \\\\\n  &   & 3 & 9 & 12 & 3(12 - k) \\\\\n\\hline\n  & 1 & 3 & 4 & 12 - k & 32 - 3k \\\\\n\\end{array}\n\\]\n\nFor all coefficients to be non-negative:\n- \\( 12 - k \\geq 0 \\implies k \\leq 12 \\)\n- \\( 32 - 3k \\geq 0 \\implies k \\leq \\frac{32}{3} \\approx 10.67 \\)\n\nThe more restrictive condition is \\( k \\leq 10 \\).\n\n### Step 2: Check \\( k = 10 \\)\nFor \\( k = 10 \\), the coefficients are \\( [1, 3, 4, 2, 2] \\), which are all non-negative.\n\n### Step 3: Synthetic Division by \\( x - 2 \\) for \\( k = 10 \\)\nThe coefficients of the polynomial \\( x^4 - 5x^2 - 10x - 4 \\) are \\( [1, 0, -5, -10, -4] \\).\n\n\\[\n\\begin{array}{r|rrrrr}\n2 & 1 & 0 & -5 & -10 & -4 \\\\\n  &   & 2 & 4 & -2 & -24 \\\\\n\\hline\n  & 1 & 2 & -1 & -12 & -28 \\\\\n\\end{array}\n\\]\n\nThe coefficients include negative values, confirming that \\( x = 2 \\) is not an upper bound.\n\n### Conclusion\nThe greatest integral value of \\( k \\) such that the polynomial \\( x^4 - 5x^2 - kx - 4 \\) has its lowest integral upper bound of the roots at \\( x = 3 \\) is \\(\\boxed{10}\\)."}