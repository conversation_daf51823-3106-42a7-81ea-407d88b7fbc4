{"row_number": 18790, "expected_answer": "\\(\\frac{5^n - 1}{4}\\)", "problem": "Find the number of zeros at the end of $(5^n)!$.", "generated_solution": "<think>\nOkay, so I need to find the number of zeros at the end of (5^n)! Hmm, trailing zeros in factorials... I remember that the number of trailing zeros is determined by the number of times 10 is a factor in the number, which depends on the number of pairs of 2 and 5 in its prime factorization. Since there are usually more 2s than 5s in a factorial's prime factors, the number of trailing zeros is determined by the number of 5s. Right, so the key is to calculate how many times 5 appears as a factor in (5^n)!.\n\nBut wait, how do you calculate the number of times a prime number divides a factorial? Oh yeah, <PERSON><PERSON>'s formula. The formula says that for a prime p and a positive integer m, the exponent of p in m! is the sum of floor(m/p) + floor(m/p^2) + floor(m/p^3) + ... until the terms become zero. So in this case, for (5^n)! and prime 5, it would be floor(5^n / 5) + floor(5^n / 25) + floor(5^n / 125) + ... and so on.\n\nLet me test this with a smaller exponent to see if it makes sense. Let's say n=1. Then (5^1)! = 5! = 120, which has one trailing zero. Using the formula: floor(5/5) = 1, floor(5/25)=0, so total is 1. That matches. For n=2, 25! should have how many zeros? Let's compute: floor(25/5)=5, floor(25/25)=1, floor(25/125)=0. Total 5+1=6 zeros. Let me check 25!... 25! is a huge number, but I know that each multiple of 5 contributes a zero, but multiples of 25 contribute an extra. So 25/5=5, 25/25=1, so 5+1=6. That seems correct. So the formula works here.\n\nNow, applying this to (5^n)! So we need to compute the sum: sum_{k=1}^∞ floor(5^n / 5^k). Since 5^n divided by 5^k is 5^{n - k}, and since floor(5^n / 5^k) = 5^{n - k} when k ≤ n. When k > n, 5^{n - k} becomes 1/5^{k - n}, which is less than 1, so the floor would be 0. Therefore, the sum is just from k=1 to k=n of 5^{n - k}.\n\nWait, let me think. For each k from 1 to n, floor(5^n /5^k) = floor(5^{n -k}) = 5^{n -k} because 5^{n -k} is an integer. So, the sum becomes 5^{n-1} + 5^{n-2} + ... + 5^0. That's a geometric series. The sum of a geometric series from exponent 0 to m-1 is (5^m - 1)/(5 - 1). Here, m is n, so the sum is (5^n - 1)/4.\n\nWait, let me check that. For example, with n=1: sum is 5^{0} = 1, which is (5^1 -1)/4 = (5 -1)/4=1. Correct. For n=2: sum is 5^{1} +5^{0}=5+1=6. (5^2 -1)/4=25-1=24/4=6. Correct. For n=3: sum should be 25 +5 +1=31. (125 -1)/4=124/4=31. Correct. So yes, the sum is (5^n -1)/4. Therefore, the number of trailing zeros in (5^n)! is (5^n -1)/4.\n\nBut wait, let me make sure there isn't a mistake here. Let's verify with n=3. (5^3)! = 125! How many zeros? The formula gives (125 -1)/4=31. So, according to that, 31 zeros. Let me compute manually. 125/5=25, 125/25=5, 125/125=1, and 125/625=0. So sum is 25+5+1=31. Yes, that's correct. So the formula holds.\n\nTherefore, the general formula for the number of trailing zeros in (5^n)! is (5^n -1)/4.\n\nBut wait, wait, let me confirm with another example. Let's take n=4. Then (5^4)! = 625! The number of zeros should be (625 -1)/4=624/4=156. Let's compute using Legendre's formula. 625/5=125, 625/25=25, 625/125=5, 625/625=1, and beyond that, 3125 is 5^5 which is bigger than 625, so stops here. So sum is 125+25+5+1=156. Correct again. So formula holds.\n\nTherefore, the answer should be (5^n -1)/4. But let me make sure that this is an integer. Since 5^n is congruent to 1 mod 4, because 5 mod 4 is 1, so 5^n mod4=1^n=1. So 5^n -1 is divisible by 4. Therefore, (5^n -1)/4 is an integer. So that works.\n\nTherefore, the number of trailing zeros in (5^n)! is (5^n -1)/4.\n\nBut let me check if there is any alternative way to represent it. Wait, perhaps using the formula for the sum of a geometric series. Since the sum is 5^{n-1} +5^{n-2} + ... +1. The number of terms is n. The sum is (5^n -1)/ (5 -1) = (5^n -1)/4. Yes, which is the same as above.\n\nHence, the conclusion is that the number of trailing zeros in (5^n)! is (5^n -1)/4.\n</think>To find the number of trailing zeros in \\((5^n)!\\), we need to determine the exponent of 5 in the prime factorization of \\((5^n)!\\). This is because the number of trailing zeros is determined by the number of times 10 is a factor in the number, and each 10 is a product of 2 and 5. Since there are usually more factors of 2 than 5, the number of trailing zeros is determined by the number of factors of 5.\n\nWe use Legendre's formula to find the exponent of a prime \\( p \\) in \\( m! \\). Legendre's formula states that the exponent of \\( p \\) in \\( m! \\) is given by:\n\\[\n\\sum_{k=1}^{\\infty} \\left\\lfloor \\frac{m}{p^k} \\right\\rfloor\n\\]\n\nIn this case, \\( m = 5^n \\) and \\( p = 5 \\). Therefore, we need to compute:\n\\[\n\\sum_{k=1}^{\\infty} \\left\\lfloor \\frac{5^n}{5^k} \\right\\rfloor\n\\]\n\nFor \\( k \\leq n \\), \\( \\left\\lfloor \\frac{5^n}{5^k} \\right\\rfloor = 5^{n-k} \\). For \\( k > n \\), \\( \\left\\lfloor \\frac{5^n}{5^k} \\right\\rfloor = 0 \\). Thus, the sum simplifies to:\n\\[\n\\sum_{k=1}^{n} 5^{n-k}\n\\]\n\nThis is a geometric series with the first term \\( 5^{n-1} \\) and common ratio \\( \\frac{1}{5} \\). The sum of the first \\( n \\) terms of a geometric series \\( a + ar + ar^2 + \\cdots + ar^{n-1} \\) is given by:\n\\[\nS_n = a \\frac{1-r^n}{1-r}\n\\]\n\nHere, \\( a = 5^{n-1} \\), \\( r = \\frac{1}{5} \\), and the number of terms is \\( n \\). Therefore, the sum is:\n\\[\n\\sum_{k=1}^{n} 5^{n-k} = 5^{n-1} \\frac{1 - \\left(\\frac{1}{5}\\right)^n}{1 - \\frac{1}{5}} = 5^{n-1} \\frac{1 - \\frac{1}{5^n}}{\\frac{4}{5}} = 5^{n-1} \\cdot \\frac{5}{4} \\left(1 - \\frac{1}{5^n}\\right) = \\frac{5^n - 1}{4}\n\\]\n\nThus, the number of trailing zeros in \\((5^n)!\\) is:\n\\[\n\\boxed{\\frac{5^n - 1}{4}}\n\\]"}