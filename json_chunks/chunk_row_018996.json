{"row_number": 18996, "expected_answer": "\\(\\frac{1}{2}\\)", "problem": "Let \\( n \\) be a positive integer, and let \\( a_1, \\ldots, a_n \\) and \\( b_1, \\ldots, b_n \\) be positive real numbers such that \\( a_1 + \\cdots + a_n = b_1 + \\cdots + b_n = 1 \\). What is the minimum value of the expression \\( \\frac{a_1^2}{a_1 + b_1} + \\cdots + \\frac{a_n^2}{a_n + b_n} \\)?", "generated_solution": "<think>\nOkay, so I need to find the minimum value of the expression \\(\\sum_{i=1}^n \\frac{a_i^2}{a_i + b_i}\\) where all the \\(a_i\\) and \\(b_i\\) are positive real numbers, and the sums of both sequences are 1. Hmm, let's think about this.\n\nFirst, since both \\(a_i\\) and \\(b_i\\) sum to 1, each of them is between 0 and 1. The expression we're trying to minimize is a sum of fractions where each term is \\(a_i^2\\) over the sum of \\(a_i\\) and \\(b_i\\). \n\nI wonder if there's an inequality that could help here. The Cauchy-Schwarz inequality often comes into play with such expressions. Let me recall that Cauchy-Sc<PERSON>rz in the form \\(\\left(\\sum \\frac{x_i^2}{y_i}\\right) \\geq \\frac{(\\sum x_i)^2}{\\sum y_i}\\). Wait, but here we have each term as \\(\\frac{a_i^2}{a_i + b_i}\\), which might fit into that form if we set \\(x_i = a_i\\) and \\(y_i = a_i + b_i\\). Let's try that.\n\nApplying Cauchy-Sc<PERSON>rz, we get:\n\n\\[\n\\sum_{i=1}^n \\frac{a_i^2}{a_i + b_i} \\geq \\frac{\\left(\\sum_{i=1}^n a_i\\right)^2}{\\sum_{i=1}^n (a_i + b_i)}\n\\]\n\nSince \\(\\sum a_i = 1\\) and \\(\\sum (a_i + b_i) = \\sum a_i + \\sum b_i = 1 + 1 = 2\\), substituting these in gives:\n\n\\[\n\\frac{1^2}{2} = \\frac{1}{2}\n\\]\n\nSo the expression is at least \\(\\frac{1}{2}\\). But is this achievable? Let's check if there's a case where equality holds in Cauchy-Schwarz. For equality, we need that \\(\\frac{x_1}{y_1} = \\frac{x_2}{y_2} = \\dots = \\frac{x_n}{y_n}\\). In our case, this would mean that for each \\(i\\), \\(\\frac{a_i}{a_i + b_i}\\) is constant. Let's call that constant \\(k\\).\n\nSo for each \\(i\\), \\(\\frac{a_i}{a_i + b_i} = k\\), which implies \\(a_i = k(a_i + b_i)\\), so \\(a_i = k a_i + k b_i\\). Rearranging, \\(a_i - k a_i = k b_i\\) → \\(a_i(1 - k) = k b_i\\) → \\(\\frac{a_i}{b_i} = \\frac{k}{1 - k}\\).\n\nThis suggests that the ratio \\(a_i/b_i\\) is the same for all \\(i\\). Let's denote \\(r = \\frac{k}{1 - k}\\), so \\(a_i = r b_i\\) for all \\(i\\). Now, since \\(\\sum a_i = 1\\) and \\(\\sum b_i = 1\\), substituting \\(a_i = r b_i\\) gives:\n\n\\[\n\\sum_{i=1}^n r b_i = r \\sum_{i=1}^n b_i = r \\times 1 = 1 \\implies r = 1\n\\]\n\nSo \\(a_i = b_i\\) for all \\(i\\). Therefore, if each \\(a_i = b_i\\), then their sums would both be 1, so each \\(a_i = b_i = \\frac{1}{n}\\) if they are all equal. Wait, but if all \\(a_i = b_i\\), then each term in the sum becomes \\(\\frac{a_i^2}{2a_i} = \\frac{a_i}{2}\\), so the total sum is \\(\\frac{1}{2}\\), which matches the lower bound we found earlier. So equality is achievable when all \\(a_i = b_i\\). Therefore, the minimum value is \\(\\frac{1}{2}\\).\n\nBut wait, let's test with a simple case to verify. Let n = 2, and set \\(a_1 = a_2 = 1/2\\), \\(b_1 = b_2 = 1/2\\). Then each term is \\(\\frac{(1/2)^2}{(1/2 + 1/2)} = \\frac{1/4}{1} = 1/4\\), so the total is 1/2. If I try another distribution, say \\(a_1 = 1\\), \\(a_2 = 0\\), but \\(b_1 = 0\\), \\(b_2 = 1\\). However, the problem states that all \\(a_i\\) and \\(b_i\\) are positive real numbers, so we can't have zeros. So maybe another example where they are not equal but positive.\n\nLet me take n=2, \\(a_1 = 0.6\\), \\(a_2 = 0.4\\), and \\(b_1 = 0.4\\), \\(b_2 = 0.6\\). Then each term:\n\nFirst term: \\(\\frac{0.6^2}{0.6 + 0.4} = \\frac{0.36}{1} = 0.36\\)\n\nSecond term: \\(\\frac{0.4^2}{0.4 + 0.6} = \\frac{0.16}{1} = 0.16\\)\n\nTotal sum: 0.36 + 0.16 = 0.52, which is more than 0.5. So indeed, the minimum is achieved when all \\(a_i = b_i\\). Another example, maybe with n=3. Let all \\(a_i = b_i = 1/3\\). Then each term is \\(\\frac{(1/3)^2}{2/3} = \\frac{1/9}{2/3} = 1/6\\). Summing three terms gives 3*(1/6) = 1/2. So that's the same.\n\nAlternatively, suppose n=1. Then a1 =1, b1=1. The expression is 1^2/(1+1) = 1/2. So even for n=1, it's 1/2. Wait, that's interesting. So regardless of n, the minimum is 1/2. So it seems that the minimum is 1/2, achieved when each a_i = b_i. Therefore, the answer should be \\boxed{\\dfrac{1}{2}}.\n\nBut just to be thorough, let me consider if there's a case where the Cauchy-Schwarz gives 1/2 but there's another method that gives a lower bound. But since we already have an example where it's achievable, and the inequality gives 1/2, which is tight, then that's the minimum. So I think that's the solution.\n</think>To find the minimum value of the expression \\(\\sum_{i=1}^n \\frac{a_i^2}{a_i + b_i}\\) where \\(a_1 + \\cdots + a_n = b_1 + \\cdots + b_n = 1\\) and \\(a_i, b_i > 0\\), we proceed as follows:\n\n1. **Application of Cauchy-Schwarz Inequality:**\n   We apply the Cauchy-Schwarz inequality in the form:\n   \\[\n   \\left(\\sum_{i=1}^n \\frac{a_i^2}{a_i + b_i}\\right) \\left(\\sum_{i=1}^n (a_i + b_i)\\right) \\geq \\left(\\sum_{i=1}^n a_i\\right)^2.\n   \\]\n   Given that \\(\\sum_{i=1}^n a_i = 1\\) and \\(\\sum_{i=1}^n b_i = 1\\), it follows that \\(\\sum_{i=1}^n (a_i + b_i) = 2\\). Thus, the inequality becomes:\n   \\[\n   \\left(\\sum_{i=1}^n \\frac{a_i^2}{a_i + b_i}\\right) \\cdot 2 \\geq 1^2.\n   \\]\n   Simplifying, we get:\n   \\[\n   \\sum_{i=1}^n \\frac{a_i^2}{a_i + b_i} \\geq \\frac{1}{2}.\n   \\]\n\n2. **Achieving the Lower Bound:**\n   To check if this lower bound can be achieved, we consider the case when \\(a_i = b_i\\) for all \\(i\\). In this case:\n   \\[\n   \\frac{a_i^2}{a_i + b_i} = \\frac{a_i^2}{2a_i} = \\frac{a_i}{2}.\n   \\]\n   Summing these terms, we get:\n   \\[\n   \\sum_{i=1}^n \\frac{a_i^2}{a_i + b_i} = \\sum_{i=1}^n \\frac{a_i}{2} = \\frac{1}{2} \\sum_{i=1}^n a_i = \\frac{1}{2} \\cdot 1 = \\frac{1}{2}.\n   \\]\n   Hence, the minimum value \\(\\frac{1}{2}\\) is indeed achievable.\n\nTherefore, the minimum value of the expression \\(\\sum_{i=1}^n \\frac{a_i^2}{a_i + b_i}\\) is \\(\\boxed{\\frac{1}{2}}\\)."}