{"row_number": 18460, "expected_answer": "\\(\\text{B}(b+1, a+1)\\) or \\(\\frac{\\Gamma(b+1)\\Gamma(a+1)}{\\Gamma(a+b+2)}\\)", "problem": "Evaluate the integral $\\int_0^1 (1-x)^a x^b \\, dx$ where $a$ and $b$ are positive real numbers.", "generated_solution": "<think>\nOkay, so I need to evaluate the integral from 0 to 1 of (1 - x)^a times x^b dx, where a and b are positive real numbers. Hmm, let's see. I remember that integrals of this form might relate to the Beta function or something like that. Wait, the Beta function is usually written as B(p, q) = integral from 0 to 1 of t^{p - 1}(1 - t)^{q - 1} dt, right? So if I compare that to the given integral, the integrand is (1 - x)^a x^b. \n\nLet me check the exponents. In the Beta function, it's t^{p - 1}(1 - t)^{q - 1}, but in our problem, it's x^b (1 - x)^a. So maybe if I set p - 1 = b and q - 1 = a, that would mean p = b + 1 and q = a + 1. Then the Beta function B(p, q) would be the integral from 0 to 1 of x^{b} (1 - x)^{a} dx. Which is exactly the integral given here. So then the integral should be equal to B(b + 1, a + 1). \n\nBut I also recall that the Beta function is related to the Gamma function. The formula is B(p, q) = Γ(p)Γ(q) / Γ(p + q). So substituting p and q as before, B(b + 1, a + 1) = Γ(b + 1)Γ(a + 1) / Γ(a + b + 2). \n\nWait, but maybe I should double-check the parameters. Let me make sure I didn't mix up p and q. In the Beta function definition, the exponents are p-1 on t and q-1 on (1 - t). So if our integrand is x^b (1 - x)^a, then p - 1 = b implies p = b + 1, and q - 1 = a implies q = a + 1. So yes, that's correct. Therefore, the integral is Γ(b + 1)Γ(a + 1) divided by Γ(a + b + 2).\n\nBut then, the problem states that a and b are positive real numbers. The Gamma function generalizes the factorial, right? So for positive integers n, Γ(n) = (n - 1)!. But here, a and b could be any positive real numbers, not necessarily integers. But regardless, the Beta function in terms of Gamma functions holds for all complex numbers except the poles, so it's valid here.\n\nAlternatively, I might have encountered this integral in the context of the Beta distribution in probability theory. The integral of the Beta distribution from 0 to 1 is 1, but that requires the normalizing constant, which is 1/B(a + 1, b + 1). Wait, but maybe that's not directly helpful here. Let me stick with the Beta and Gamma function relation.\n\nSo the answer should be Γ(a + 1)Γ(b + 1)/Γ(a + b + 2). But maybe there's a way to express this in terms of factorials if a and b were integers, but the problem says they are positive real numbers. Therefore, the answer must be in terms of Gamma functions. Alternatively, since Γ(n + 1) = n! for integers n, but for real numbers, it's defined as Γ(z) = ∫₀^∞ t^{z - 1} e^{-t} dt.\n\nAlternatively, maybe there's another substitution that can be done here. Let me think. Suppose I let t = 1 - x. Then when x = 0, t = 1; when x = 1, t = 0. So the integral becomes ∫ from 1 to 0 of t^a (1 - t)^b (-dt). The negative sign reverses the limits, so it's ∫ from 0 to 1 of t^a (1 - t)^b dt, which is the same as the original integral but swapping a and b. So that suggests that the integral is symmetric in a and b. Which makes sense because the Beta function is symmetric, B(p, q) = B(q, p). So indeed, the result should have Γ(a + 1)Γ(b + 1) in the numerator, which is symmetric in a and b.\n\nTherefore, the final answer is Γ(a + 1)Γ(b + 1)/Γ(a + b + 2). But maybe this can also be written using factorials if we consider that Γ(n) = (n - 1)! for positive integers. But since a and b are real numbers, we can’t use factorial notation here. So the answer must stay in terms of Gamma functions.\n\nAlternatively, another way to approach the integral is via substitution. Let me try substitution. Let’s see, perhaps substitution u = x/(1 - x) or something similar. But that might complicate things. Alternatively, integrating by parts? Let me try integrating by parts once.\n\nLet’s set u = x^b and dv = (1 - x)^a dx. Then du = b x^{b - 1} dx, and v = ∫ (1 - x)^a dx. The integral of (1 - x)^a dx is -(1 - x)^{a + 1}/(a + 1). So then integration by parts gives uv evaluated from 0 to 1 minus ∫ v du. Let's compute that.\n\nuv from 0 to 1: At x=1, x^b is 1, and (1 - x)^{a + 1} is 0, so that term is 0. At x=0, x^b is 0, so the entire uv term is 0. Then we have minus ∫ from 0 to 1 of v du, which is - ∫ (- (1 - x)^{a + 1}/(a + 1)) * b x^{b - 1} dx. The two negatives cancel, so it becomes (b/(a + 1)) ∫ (1 - x)^{a + 1} x^{b - 1} dx.\n\nSo the original integral, let's call it I(a, b), is equal to (b/(a + 1)) * I(a + 1, b - 1). Wait, but b is a positive real number. If we continue integrating by parts multiple times, perhaps we can find a recursion.\n\nAlternatively, let's suppose that we do this repeatedly. Let's say we perform integration by parts k times. But maybe there's a pattern here. Let me try one more step.\n\nSuppose we have I(a, b) = (b/(a + 1)) * I(a + 1, b - 1). If we then integrate I(a + 1, b - 1) by parts again, setting u = x^{b - 1} and dv = (1 - x)^{a + 1} dx, then du = (b - 1)x^{b - 2} dx and v = - (1 - x)^{a + 2}/(a + 2). Then, following the same logic, the uv term would again be zero, and the integral becomes ( (b - 1)/(a + 2) ) * I(a + 2, b - 2).\n\nContinuing this process, each time we integrate by parts, we decrease the exponent of x by 1 and increase the exponent of (1 - x) by 1, while multiplying by b/(a + 1), then (b - 1)/(a + 2), and so on. If we keep doing this until the exponent on x becomes zero, which would take b steps (assuming b is an integer, but since it's a real number, maybe not...). Hmm, maybe this approach only works if b is a positive integer. Since the problem states that b is a positive real number, not necessarily integer, this method might not lead to a direct solution. So perhaps the Beta function approach is better here.\n\nAlternatively, recall that the integral from 0 to 1 of x^{b}(1 - x)^a dx is the same as the Beta function B(b + 1, a + 1) = Γ(b + 1)Γ(a + 1)/Γ(a + b + 2). Therefore, the answer is the ratio of Gamma functions as before.\n\nAnother way to think about it is using the relationship between Beta and Gamma functions. Since I know that Γ(p)Γ(q) = Γ(p + q) B(p, q), but perhaps more precisely, B(p, q) = Γ(p)Γ(q)/Γ(p + q). So substituting p = b + 1 and q = a + 1, then yes, that gives the Beta function in terms of Gamma functions. Therefore, the integral is Γ(b + 1)Γ(a + 1)/Γ(a + b + 2).\n\nTherefore, the final answer is Γ(a + 1)Γ(b + 1) divided by Γ(a + b + 2). To confirm, I can check a simple case where a and b are integers. For example, let a = 0, b = 0. Then the integral is ∫0^1 (1 - x)^0 x^0 dx = ∫0^1 1 dx = 1. Plugging into the formula: Γ(1)Γ(1)/Γ(2). Since Γ(1) = 1, and Γ(2) = 1! = 1, so 1*1/1 = 1. Correct.\n\nAnother test case: Let a = 1, b = 1. Then the integral is ∫0^1 (1 - x)x dx = ∫0^1 x - x^2 dx = [x²/2 - x³/3] from 0 to1 = (1/2 - 1/3) - 0 = 1/6. The formula gives Γ(2)Γ(2)/Γ(4). Γ(2) = 1! = 1, Γ(4) = 3! = 6. So 1*1/6 = 1/6. Correct.\n\nAnother example: Let a = 2, b = 3. Integral is ∫0^1 (1 - x)^2 x^3 dx. Let's compute it directly. Expand (1 - x)^2 = 1 - 2x + x². Multiply by x³: x³ - 2x⁴ + x⁵. Integrate term by term: [x⁴/4 - 2x⁵/5 + x⁶/6] from 0 to1 = 1/4 - 2/5 + 1/6. Let's compute that: convert to common denominator 60. 15/60 - 24/60 + 10/60 = (15 -24 +10)/60 = 1/60. Now using the formula: Γ(3 +1)Γ(2 +1)/Γ(3 +2 +2). Wait, Γ(4)Γ(3)/Γ(5). Γ(4) = 3! = 6, Γ(3) = 2! = 2, Γ(5) = 4! =24. So (6*2)/24 = 12/24 = 1/2. Wait, but the actual integral was 1/60. Wait, that's a discrepancy. Wait, wait, maybe I made a mistake in the parameters.\n\nWait, in the problem, a and b are parameters. In my example, a =2 and b=3, so according to the formula, Γ(a +1)=Γ(3)=2!, Γ(b +1)=Γ(4)=6, and Γ(a + b + 2)=Γ(2 +3 +2)=Γ(7)=6! =720. Then the formula would be (2! * 6!)/7! = (2*720)/5040 = 1440/5040 = 12/42 = 2/7. Wait, but that's not matching the direct computation of 1/60. So this indicates an error in my test case.\n\nWait, I must have miscalculated something. Wait, the formula is Γ(a +1)Γ(b +1)/Γ(a + b + 2). For a =2, b=3, this is Γ(3)Γ(4)/Γ(6). Γ(3) =2!, Γ(4)=3!, Γ(6)=5!.\n\nSo 2! * 3! /5! = (2 *6)/120 =12/120=1/10. But when I computed the integral directly, I got 1/60. That's a problem. So that must mean that either my calculation of the integral is wrong, or my formula is wrong.\n\nWait let me check the integral again. If a=2, b=3. So the integrand is (1 -x)^2 x^3. Let's compute ∫0^1 (1 -x)^2 x^3 dx. Expanding (1 -x)^2 gives 1 -2x +x². Multiply by x^3: x^3 -2x^4 +x^5. The integral is [x^4/4 - 2x^5/5 +x^6/6] from 0 to1. So substituting 1: 1/4 - 2/5 +1/6. Let's compute this:\n\nConvert to denominators of 60. 1/4 =15/60, 2/5=24/60, 1/6=10/60. So 15/60 -24/60 +10/60 = (15 -24 +10)/60 =1/60. But according to the formula, it should be Γ(3)Γ(4)/Γ(6) = 2*6 /120 =12/120=1/10. But 1/60 is not 1/10. That’s a problem. So where is the mistake?\n\nWait, no, perhaps I messed up the formula. Wait, the original integral is B(b +1, a +1) = Γ(b+1)Γ(a+1)/Γ(a +b +2). Wait, no, Beta function parameters are (p, q) where the exponents are p-1 and q-1 on x and 1 -x. So in the integral ∫x^b (1 -x)^a dx, that would correspond to p = b +1, q = a +1, so the Beta function is B(b +1, a +1) = Γ(b+1)Γ(a+1)/Γ(a +b +2). So in my test case, a=2, b=3, so Γ(3+1)Γ(2+1)/Γ(3+2+2)=Γ(4)Γ(3)/Γ(7)=6*2/720=12/720=1/60. Ah! Wait, I miscalculated Γ(a +b +2). For a=2 and b=3, a + b +2=7. Γ(7)=6!=720. So indeed, Γ(4)=6, Γ(3)=2, so 6*2=12. 12/720=1/60. Which matches the direct computation. So earlier when I thought the formula gives 1/10, that was a mistake in the denominator. I must have confused the denominator as Γ(a +1 + b +1) which is Γ(a +b +2). So the formula does work here. So in the example, the formula gives 1/60, which matches the manual computation. So the formula is correct. \n\nTherefore, the conclusion is that the integral equals Γ(a +1)Γ(b +1)/Γ(a +b +2). So that's the answer. Hence, the answer is \\boxed{\\dfrac{\\Gamma(a+1)\\Gamma(b+1)}{\\Gamma(a + b + 2)}}.\n</think>To evaluate the integral \\(\\int_0^1 (1 - x)^a x^b \\, dx\\) where \\(a\\) and \\(b\\) are positive real numbers, we recognize that it is a form of the Beta function. The Beta function \\(B(p, q)\\) is defined as:\n\n\\[\nB(p, q) = \\int_0^1 t^{p-1} (1 - t)^{q-1} \\, dt\n\\]\n\nBy comparing the given integral \\(\\int_0^1 (1 - x)^a x^b \\, dx\\) with the Beta function, we identify the parameters \\(p\\) and \\(q\\) as follows:\n- The exponent on \\(x\\) is \\(b\\), so \\(p - 1 = b\\) which implies \\(p = b + 1\\).\n- The exponent on \\(1 - x\\) is \\(a\\), so \\(q - 1 = a\\) which implies \\(q = a + 1\\).\n\nThus, the integral can be written in terms of the Beta function as:\n\n\\[\n\\int_0^1 (1 - x)^a x^b \\, dx = B(b + 1, a + 1)\n\\]\n\nThe Beta function \\(B(p, q)\\) is related to the Gamma function \\(\\Gamma(z)\\) by the formula:\n\n\\[\nB(p, q) = \\frac{\\Gamma(p) \\Gamma(q)}{\\Gamma(p + q)}\n\\]\n\nSubstituting \\(p = b + 1\\) and \\(q = a + 1\\) into this formula, we get:\n\n\\[\nB(b + 1, a + 1) = \\frac{\\Gamma(b + 1) \\Gamma(a + 1)}{\\Gamma((b + 1) + (a + 1))} = \\frac{\\Gamma(b + 1) \\Gamma(a + 1)}{\\Gamma(a + b + 2)}\n\\]\n\nTherefore, the evaluated integral is:\n\n\\[\n\\boxed{\\dfrac{\\Gamma(a+1)\\Gamma(b+1)}{\\Gamma(a + b + 2)}}\n\\]"}