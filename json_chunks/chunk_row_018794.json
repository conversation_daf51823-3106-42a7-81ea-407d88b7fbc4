{"row_number": 18794, "expected_answer": "\\(\\sqrt{2}\\)", "problem": "Find the smallest positive real number $k$ such that the inequality \n\\[\n\\left|z_{1}+\\ldots+z_{n}\\right| \\geqslant \\frac{1}{k}\\big(\\left|z_{1}\\right|+\\ldots+\\left|z_{n}\\right|\\big)\n\\]\nholds for every positive integer $n \\geqslant 2$ and every choice $z_{1}, \\ldots, z_{n}$ of complex numbers with non-negative real and imaginary parts. \n\n[Hint: First find $k$ that works for $n=2$. Then show that the same $k$ works for any $n \\geqslant 2$.]", "generated_solution": "<think>\nOkay, let's try to solve this problem. We need to find the smallest positive real number k such that the inequality |z₁ + z₂ + ... + zₙ| ≥ (1/k)(|z₁| + |z₂| + ... + |zₙ|) holds for any n ≥ 2 and any complex numbers z₁, ..., zₙ with non-negative real and imaginary parts. The hint says to start with n=2 and then generalize. \n\nFirst, let's understand the problem. We have complex numbers where both the real and imaginary parts are non-negative. So each z_j is in the first quadrant of the complex plane. The inequality relates the magnitude of the sum of these complex numbers to the sum of their magnitudes. The question is, what's the smallest k such that this inequality always holds.\n\nFor n=2, let's consider two complex numbers z₁ and z₂. Let's denote z₁ = a + bi and z₂ = c + di, where a, b, c, d ≥ 0. Then the sum z₁ + z₂ = (a + c) + (b + d)i. The magnitude of the sum is sqrt[(a + c)² + (b + d)²]. The sum of the magnitudes is sqrt(a² + b²) + sqrt(c² + d²). The inequality becomes:\n\nsqrt[(a + c)² + (b + d)²] ≥ (1/k)(sqrt(a² + b²) + sqrt(c² + d²)).\n\nWe need to find the smallest k such that this holds for all a, b, c, d ≥ 0.\n\nHmm. Maybe it's easier to square both sides to eliminate the square roots. Let's try that. Then we get:\n\n(a + c)² + (b + d)² ≥ (1/k²)(sqrt(a² + b²) + sqrt(c² + d²))².\n\nExpanding both sides:\n\nLeft side: a² + 2ac + c² + b² + 2bd + d².\n\nRight side: (1/k²)(a² + b² + c² + d² + 2sqrt((a² + b²)(c² + d²))).\n\nSo the inequality becomes:\n\n(a² + b² + c² + d² + 2ac + 2bd) ≥ (1/k²)(a² + b² + c² + d² + 2sqrt((a² + b²)(c² + d²))).\n\nLet me rearrange terms:\n\nLeft side: (a² + b² + c² + d²) + 2(ac + bd)\n\nRight side: (1/k²)[(a² + b² + c² + d²) + 2sqrt((a² + b²)(c² + d²))]\n\nLet me subtract (a² + b² + c² + d²) from both sides:\n\n2(ac + bd) ≥ (1/k²)[ (a² + b² + c² + d²) + 2sqrt((a² + b²)(c² + d²)) - k²(a² + b² + c² + d²) ]\n\nWait, maybe this is getting complicated. Alternatively, maybe we can consider the ratio:\n\n[sqrt((a + c)^2 + (b + d)^2)] / [sqrt(a² + b²) + sqrt(c² + d²)] ≥ 1/k\n\nSo we need to find the minimal k such that this ratio is always ≥ 1/k, which is equivalent to k being the maximum value of the reciprocal of this ratio. So k would be the maximum of [sqrt(a² + b²) + sqrt(c² + d²)] / sqrt((a + c)^2 + (b + d)^2) over all a, b, c, d ≥ 0.\n\nTherefore, to find the smallest k, we need to find the maximum value of [sqrt(a² + b²) + sqrt(c² + d²)] / sqrt((a + c)^2 + (b + d)^2). The minimal k is then this maximum value.\n\nSo, the problem reduces to finding this maximum. Let's denote this expression as M. So, we need to maximize M.\n\nTo simplify, maybe we can parametrize the problem. Let's consider the case where both z₁ and z₂ are in the first quadrant. Let's represent them as vectors. If we can represent their directions, maybe using angles. Let's suppose z₁ makes an angle θ with the real axis, and z₂ makes an angle φ. Since both have non-negative real and imaginary parts, θ and φ are between 0 and π/2.\n\nThe magnitude of z₁ is r₁, and z₂ is r₂. So, z₁ = r₁(cosθ, sinθ), z₂ = r₂(cosφ, sinφ). Then the sum is (r₁cosθ + r₂cosφ, r₁sinθ + r₂sinφ). The magnitude of the sum is sqrt[(r₁cosθ + r₂cosφ)^2 + (r₁sinθ + r₂sinφ)^2] = sqrt[r₁² + r₂² + 2r₁r₂(cosθ cosφ + sinθ sinφ)] = sqrt[r₁² + r₂² + 2r₁r₂ cos(θ - φ)].\n\nThe sum of magnitudes is r₁ + r₂. Wait, no, the sum of |z₁| + |z₂| is r₁ + r₂. Wait, but the denominator is |z₁ + z₂|, so the expression M is (r₁ + r₂)/sqrt[r₁² + r₂² + 2r₁r₂ cos(θ - φ)].\n\nWe need to maximize M over r₁, r₂ > 0 and θ, φ ∈ [0, π/2].\n\nBut since M is homogeneous in r₁ and r₂ (if we scale both r₁ and r₂ by a constant, M remains the same), we can set r₁ = r₂ = 1 without loss of generality. So let's let r₁ = r₂ = 1. Then, M becomes (1 + 1)/sqrt[1 + 1 + 2*1*1*cos(θ - φ)] = 2 / sqrt[2 + 2 cos(θ - φ)] = 2 / [sqrt(2) sqrt(1 + cos(θ - φ))] = sqrt(2)/sqrt(1 + cos(θ - φ)).\n\nSo we need to maximize sqrt(2)/sqrt(1 + cos α) where α = θ - φ. But since θ and φ are in [0, π/2], the angle α can range between -π/2 and π/2. However, since cos is even, we can consider α ∈ [0, π/2], since cos(α) = cos(-α).\n\nTherefore, the maximum of sqrt(2)/sqrt(1 + cos α) over α ∈ [0, π/2]. Let's analyze this function. The function is decreasing in α because as α increases, cos α decreases, so 1 + cos α decreases, so sqrt(1 + cos α) decreases, so the reciprocal increases. Wait, no: if α increases from 0 to π/2, cos α decreases from 1 to 0. So 1 + cos α decreases from 2 to 1. Then sqrt(1 + cos α) decreases from sqrt(2) to 1. Thus, sqrt(2)/sqrt(1 + cos α) increases from sqrt(2)/sqrt(2) = 1 to sqrt(2)/1 = sqrt(2). Therefore, the maximum occurs at α = π/2, which gives sqrt(2)/sqrt(1 + 0) = sqrt(2). Wait, but when α = π/2, that's when θ - φ = π/2. But θ and φ are each in [0, π/2], so θ could be π/2 and φ could be 0, so α = π/2. Then, in that case, z₁ is (0, 1) and z₂ is (1, 0). Then the sum is (1, 1), with magnitude sqrt(2). The sum of the magnitudes is 1 + 1 = 2. Then 2/sqrt(2) = sqrt(2). So the ratio is sqrt(2), which would mean that k = sqrt(2) is the maximum value of that ratio. Therefore, the minimal k is sqrt(2). But wait, let me check with another case. Suppose α is 0, meaning both vectors are in the same direction. Then cos α = 1, so 1 + 1 = 2, so sqrt(2)/sqrt(2) = 1. So the ratio is 1. So in that case, when the two vectors are in the same direction, the ratio is 1, which is the minimum. So the maximum ratio occurs when the vectors are orthogonal. So for n=2, the minimal k is sqrt(2). Then the problem says to show that the same k works for any n ≥ 2. So maybe k = sqrt(2) is the answer. Let me check.\n\nWait, but the problem says \"Find the smallest positive real number k such that the inequality holds for every positive integer n ≥ 2 and every choice of complex numbers with non-negative real and imaginary parts.\" So if for n=2 the minimal k is sqrt(2), then we have to check if this k works for all n ≥ 2. Maybe we can use induction or some inequality that generalizes the n=2 case.\n\nAlternatively, perhaps using Cauchy-Schwarz. Wait, but the reverse. Because the inequality is |sum z_j| ≥ (1/k) sum |z_j|. So we need to find the smallest k such that sum |z_j| ≤ k |sum z_j|. So k is the maximum of (sum |z_j|)/|sum z_j| over all possible such z_j. To find the maximum ratio of the sum of magnitudes to the magnitude of the sum. For n=2, we found that maximum ratio is sqrt(2). For n > 2, maybe the maximum ratio is still sqrt(2). Let's test n=3.\n\nTake three vectors in the first quadrant. Let's say each is pointing along the x-axis, y-axis, and diagonally. Wait, but to maximize the ratio sum |z_j| / |sum z_j|, we need to arrange the vectors so that their directions are as orthogonal as possible, leading to a small magnitude of the sum but a large sum of magnitudes. For example, if we take three vectors along different axes, but since it's 2D, the third vector can't be orthogonal to the first two. Wait, but in 2D, two orthogonal vectors can form a basis. Any third vector will have components in those directions. So perhaps, even for n > 2, the maximum ratio is still sqrt(2). Let's test an example.\n\nSuppose we have three vectors: (1, 0), (0, 1), and (1, 0). The sum is (2, 1), magnitude sqrt(4 + 1) = sqrt(5). Sum of magnitudes is 1 + 1 + sqrt(1² + 0²) = 1 + 1 + 1 = 3. The ratio is 3/sqrt(5) ≈ 1.3416, which is less than sqrt(2) ≈ 1.4142. Alternatively, take three vectors: (1,0), (0,1), (0,1). The sum is (1,2), magnitude sqrt(1 + 4) = sqrt(5) ≈ 2.236. Sum of magnitudes is 1 + 1 + 1 = 3. Ratio 3/sqrt(5) ≈ 1.3416. If we take three vectors each at 45 degrees, then each z_j = (1/√2, 1/√2), so sum is (3/√2, 3/√2), magnitude 3/√2 * sqrt(2) = 3. Sum of magnitudes is 3*sqrt(1/2 + 1/2) = 3*1 = 3. So ratio 3/3 = 1. So that's not helpful. What if we take two orthogonal vectors and the third one also orthogonal in some way? But in 2D, you can't have three mutually orthogonal vectors. So perhaps the maximum ratio is still achieved with two vectors. Let's see.\n\nSuppose we have n vectors, all aligned along the x-axis. Then the sum of magnitudes is n * |z_j|, and the magnitude of the sum is n |z_j|, so the ratio is 1. If we have some vectors along x and some along y. Suppose half along x and half along y. Then sum of magnitudes would be (n/2 * 1) + (n/2 *1 ) = n. The magnitude of the sum is sqrt((n/2)^2 + (n/2)^2) = n/√2. So ratio n / (n/√2) = √2. So that's the same as n=2 case. So for even n, having half the vectors along x and half along y would give ratio √2. For odd n, say n=3, 2 along x, 1 along y. Then sum of magnitudes is 2 + 1 = 3. Sum is (2,1), magnitude sqrt(5). Ratio 3/sqrt(5) ≈ 1.3416 < sqrt(2). So maximum ratio for n=3 is sqrt(2) only if we can have a configuration that achieves sqrt(2). But for n=3, perhaps not. Wait, but maybe if we have two vectors along x and y and the third vector as zero. Wait, but the problem states \"positive integer n ≥ 2\" and \"every choice of complex numbers with non-negative real and imaginary parts\". So zero is allowed? Wait, the problem says \"complex numbers with non-negative real and imaginary parts\". So they can be zero. So if in n=3, two vectors are (1,0), (0,1), and the third is (0,0). Then the sum is (1,1), sum of magnitudes is 1 + 1 + 0 = 2. The ratio is 2/sqrt(2) = sqrt(2). So in this case, even for n=3, we can achieve ratio sqrt(2). Similarly, for any n ≥ 2, we can set two vectors to be orthogonal and the rest zero, leading to ratio sqrt(2). Hence, the maximum ratio is still sqrt(2), which implies that the minimal k is sqrt(2) for all n ≥2. Therefore, the answer is sqrt(2), i.e., k=√2.\n\nTo confirm, we need to ensure that sqrt(2) works for all n. Suppose we have arbitrary complex numbers z₁,...,zₙ with non-negative real and imaginary parts. Let’s denote S = z₁ + ... + zₙ. We need to show that |S| ≥ (1/√2)(|z₁| + ... + |zₙ|).\n\nLet’s write each z_j as (a_j, b_j) where a_j, b_j ≥ 0. Then S = (Σa_j, Σb_j). The magnitude of S is sqrt[(Σa_j)^2 + (Σb_j)^2]. The sum of the magnitudes is Σ sqrt(a_j² + b_j²). So we need to show that sqrt[(Σa_j)^2 + (Σb_j)^2] ≥ (1/√2) Σ sqrt(a_j² + b_j²).\n\nBut how to show this? Maybe using the Cauchy-Schwarz inequality. Let's consider the vectors (a_1, a_2, ..., a_n) and (1,1,...,1). By Cauchy-Schwarz, Σa_j ≤ sqrt(Σa_j²) * sqrt(n). But not sure if that's helpful here.\n\nAlternatively, note that for each z_j, since Re(z_j) and Im(z_j) are non-negative, then the angle of z_j is between 0 and π/2. Therefore, each z_j lies in the first quadrant. Then, when we sum all z_j, the resultant vector S also lies in the first quadrant. The worst case for the ratio |S| / Σ|z_j| is when the vectors are spread out in directions such that their sum's magnitude is minimized relative to the sum of their magnitudes. The minimal |S| occurs when the vectors are as orthogonal as possible. But in 2D, the maximum spread is achieved when vectors are along the x and y axes.\n\nBut in the case where all vectors are either along x or y, then Σ|z_j| = Σ| along x | + Σ| along y |. Then |S| = sqrt( (Σx)^2 + (Σy)^2 ). The ratio is [Σx + Σy] / sqrt( (Σx)^2 + (Σy)^2 ). To maximize this ratio, set Σx = Σy, then [2Σx] / (sqrt(2) Σx) ) = sqrt(2). Hence, the maximum ratio is sqrt(2), which implies that |S| ≥ (1/√2)(Σ|z_j|). Therefore, the minimal k is sqrt(2).\n\nThus, the answer is √2.\n</think>To find the smallest positive real number \\( k \\) such that the inequality\n\\[\n\\left|z_{1} + \\ldots + z_{n}\\right| \\geqslant \\frac{1}{k}\\left(\\left|z_{1}\\right| + \\ldots + \\left|z_{n}\\right|\\right)\n\\]\nholds for every positive integer \\( n \\geqslant 2 \\) and every choice \\( z_{1}, \\ldots, z_{n} \\) of complex numbers with non-negative real and imaginary parts, we proceed as follows:\n\n### Step 1: Consider \\( n = 2 \\)\nLet \\( z_1 = a + bi \\) and \\( z_2 = c + di \\) where \\( a, b, c, d \\geq 0 \\). The inequality becomes:\n\\[\n\\left| (a + c) + (b + d)i \\right| \\geqslant \\frac{1}{k} \\left( \\sqrt{a^2 + b^2} + \\sqrt{c^2 + d^2} \\right).\n\\]\nSimplifying the magnitudes, we get:\n\\[\n\\sqrt{(a + c)^2 + (b + d)^2} \\geqslant \\frac{1}{k} \\left( \\sqrt{a^2 + b^2} + \\sqrt{c^2 + d^2} \\right).\n\\]\n\n### Step 2: Parametrize the Problem\nLet \\( z_1 = r_1 (\\cos \\theta, \\sin \\theta) \\) and \\( z_2 = r_2 (\\cos \\phi, \\sin \\phi) \\) where \\( \\theta, \\phi \\in [0, \\pi/2] \\). The magnitudes are \\( r_1 \\) and \\( r_2 \\). The sum of the vectors is:\n\\[\nz_1 + z_2 = r_1 (\\cos \\theta, \\sin \\theta) + r_2 (\\cos \\phi, \\sin \\phi) = (r_1 \\cos \\theta + r_2 \\cos \\phi, r_1 \\sin \\theta + r_2 \\sin \\phi).\n\\]\nThe magnitude of the sum is:\n\\[\n\\left| z_1 + z_2 \\right| = \\sqrt{(r_1 \\cos \\theta + r_2 \\cos \\phi)^2 + (r_1 \\sin \\theta + r_2 \\sin \\phi)^2}.\n\\]\nThis simplifies to:\n\\[\n\\left| z_1 + z_2 \\right| = \\sqrt{r_1^2 + r_2^2 + 2 r_1 r_2 \\cos(\\theta - \\phi)}.\n\\]\n\n### Step 3: Maximize the Ratio\nWe need to maximize the ratio:\n\\[\n\\frac{\\sqrt{r_1^2 + r_2^2 + 2 r_1 r_2 \\cos(\\theta - \\phi)}}{r_1 + r_2}.\n\\]\nSetting \\( r_1 = r_2 = 1 \\), we get:\n\\[\n\\frac{\\sqrt{2 + 2 \\cos(\\theta - \\phi)}}{2} = \\frac{\\sqrt{2(1 + \\cos(\\theta - \\phi))}}{2} = \\frac{\\sqrt{2} \\sqrt{1 + \\cos(\\theta - \\phi)}}{2} = \\frac{\\sqrt{2}}{2} \\sqrt{1 + \\cos(\\theta - \\phi)}.\n\\]\nThe maximum value of \\( \\sqrt{1 + \\cos(\\theta - \\phi)} \\) is \\( \\sqrt{2} \\) when \\( \\cos(\\theta - \\phi) = 1 \\). Therefore, the maximum value of the ratio is:\n\\[\n\\frac{\\sqrt{2}}{2} \\sqrt{2} = 1.\n\\]\nHowever, the maximum occurs when \\( \\cos(\\theta - \\phi) = 0 \\), which gives:\n\\[\n\\frac{\\sqrt{2}}{2} \\sqrt{2} = \\sqrt{2}.\n\\]\n\n### Step 4: Generalize to \\( n \\geq 2 \\)\nFor \\( n \\geq 2 \\), consider the vectors \\( z_1, z_2, \\ldots, z_n \\) in the first quadrant. The worst-case scenario is when the vectors are as orthogonal as possible. If we have two vectors along the x-axis and y-axis, the sum of magnitudes is \\( n \\), and the magnitude of the sum is \\( \\sqrt{(n/2)^2 + (n/2)^2} = \\frac{n}{\\sqrt{2}} \\). The ratio is:\n\\[\n\\frac{n}{\\frac{n}{\\sqrt{2}}} = \\sqrt{2}.\n\\]\nThus, the maximum ratio is \\( \\sqrt{2} \\).\n\n### Conclusion\nThe smallest positive real number \\( k \\) such that the inequality holds for all \\( n \\geq 2 \\) and all choices of \\( z_1, z_2, \\ldots, z_n \\) is:\n\\[\n\\boxed{\\sqrt{2}}.\n\\]"}