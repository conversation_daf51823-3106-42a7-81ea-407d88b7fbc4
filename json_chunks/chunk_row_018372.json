{"row_number": 18372, "expected_answer": "288", "problem": "Let \\( x \\) be the arithmetic mean of all positive integers \\( k < 577 \\) such that\n\\[ k^4 \\equiv 144 \\pmod{577}. \\]\nFind the greatest integer less than or equal to \\( x \\).", "generated_solution": "<think>\nOkay, let's tackle this problem step by step. The question is about finding the arithmetic mean of all positive integers k less than 577 such that k⁴ ≡ 144 mod 577. Then, we need to find the greatest integer less than or equal to that mean. Hmm, okay, let's break this down.\n\nFirst, I know that arithmetic mean is just the sum of all such k divided by the number of k's. So, my task is to find all k < 577 satisfying the congruence and then compute their average. But the challenge here is finding those k's. Since 577 is a prime number (I recall that 577 is a prime because it's not divisible by small primes like 2, 3, 5, 7, 11, etc.), we might need to use some properties from number theory related to modular arithmetic and roots.\n\nThe congruence is k⁴ ≡ 144 mod 577. So we need to find all fourth roots of 144 modulo 577. Let me think. First, maybe I can check if 144 is a quadratic residue modulo 577. Wait, but since it's a fourth power, perhaps it's easier to first find the square roots of 144 modulo 577 and then see which of those are squares themselves?\n\nAlternatively, maybe we can directly solve k⁴ ≡ 144 mod 577. Let me see. Let's note that 577 is prime, so the ring of integers modulo 577 is a field. Therefore, the equation k⁴ = 144 in this field will have at most 4 solutions. But how many solutions does it actually have?\n\nWait, maybe first I should check if 144 is a quadratic residue modulo 577. But even if it is, being a fourth power is a stronger condition. Let me first compute 144 mod 577, which is just 144. So, 144 is the residue. Let's check if 144 is a quadratic residue modulo 577. Using Euler's criterion, which states that a^((p-1)/2) ≡ 1 mod p if a is a quadratic residue. So, let's compute 144^(288) mod 577 (since (577-1)/2 = 288). But calculating 144^288 mod 577 manually is impractical. Maybe we can use Legendre symbol properties?\n\nAlternatively, since 144 is 12 squared, so 144 is a square modulo 577 if 12 is a quadratic residue. Wait, but 12 is 12 mod 577. Let me check if 12 is a quadratic residue modulo 577. Using the Legendre symbol (12/577). We can split this into (3/577)*(4/577). Since 4 is a square, (4/577)=1. Then, (3/577) can be computed using quadratic reciprocity. Since 577 mod 3 is 577 divided by 3 is 192 with a remainder of 1. So 577 ≡ 1 mod 3. Then, (3/577) = (1/3) = 1 because if p ≡ 1 mod 4, but wait, quadratic reciprocity for (p/q) when p and q are odd primes is (q/p)*(-1)^((p-1)(q-1)/4). Here, 3 and 577 are primes, both congruent to 3 mod 4? Wait, 577 divided by 4 is 144 with remainder 1, so 577 ≡ 1 mod 4. 3 is 3 mod 4. So (3/577) = (577/3)*(-1)^((3-1)(577-1)/4) = (577 mod 3/3)* (-1)^(2*576/4) = (1/3)*(-1)^(288). Since 288 is even, (-1)^288 = 1. So (3/577) = (1/3). But (1/3) is 1 if 1 is a quadratic residue mod 3, which it is. So (3/577) = 1. Therefore, (12/577) = 1*1 = 1. So 12 is a quadratic residue modulo 577, hence 144 = 12² is a quadratic residue as well, obviously. But that doesn't help us directly because we need to find fourth roots.\n\nSo, maybe we can first find the square roots of 144 modulo 577, which are the solutions to x² ≡ 144 mod 577. Then, from those square roots, we can check which of them are squares themselves (i.e., which have square roots), giving us the fourth roots of 144.\n\nAlternatively, since 144 is 12², we can say x² ≡ 12² mod 577. Then, the solutions to this congruence are x ≡ ±12 mod 577? Wait, but that's only if 577 is a prime where 12 and -12 are distinct. Since 577 is prime, the equation x² ≡ a² mod p has solutions x ≡ a and x ≡ -a. So, x² ≡ 144 mod 577 would have solutions x ≡ 12 and x ≡ -12 ≡ 577 -12 = 565 mod 577. Therefore, the square roots of 144 mod 577 are 12 and 565. So, to find the fourth roots, we need to solve x² ≡ 12 mod 577 and x² ≡ 565 mod 577.\n\nWait, but 12 and 565. Let's check if 12 is a quadratic residue modulo 577. Wait, we already computed the Legendre symbol (12/577) = 1, so 12 is a quadratic residue. Similarly, 565 mod 577 is 565. Let's check if 565 is a quadratic residue. 565 = 577 -12, so 565 ≡ -12 mod 577. So, (-12/577) = (-1/577)*(12/577). We already know that (12/577) = 1. For (-1/577), since 577 ≡ 1 mod 4 (as 577 = 4*144 +1), then (-1) is a quadratic residue modulo primes congruent to 1 mod 4. So (-1/577) = 1. Therefore, (-12/577) = 1*1 = 1. Hence, 565 is also a quadratic residue. Therefore, both equations x² ≡ 12 mod 577 and x² ≡ 565 mod 577 have two solutions each. So in total, we expect up to four solutions for the original equation x⁴ ≡ 144 mod 577.\n\nTherefore, the fourth roots of 144 mod 577 would be the square roots of 12 and the square roots of 565. So, we need to find all x such that x² ≡ 12 mod 577 or x² ≡ 565 mod 577.\n\nSo now, the problem reduces to solving these two quadratic congruences. Let's start with x² ≡ 12 mod 577. To find the solutions, we can use the Tonelli-Shanks algorithm or other methods for finding square roots modulo primes. But since 577 is a prime ≡ 1 mod 4, we can also use the method of exponentiation. Alternatively, maybe we can guess some numbers. Let me try small numbers squared modulo 577.\n\nBut 12 is small, so maybe x is small as well. Let's see:\n\nCheck x=5: 25 mod 577=25≠12\n\nx=6: 36≠12\n\nx=7:49≠12\n\nx=11:121≠12\n\nx=12:144≡144-577*0=144≠12. Wait, so x²=12 mod577. Maybe try larger numbers. Alternatively, maybe there's a pattern. Alternatively, perhaps 12 is a quadratic residue, so its square roots can be found using the Tonelli-Shanks algorithm.\n\nAlternatively, since 577 ≡ 1 mod 4, we can write 577 = 1 + 4*144. Then, to find the square roots of 12 modulo 577, we can use the formula x ≡ ±12^((577+1)/4) mod 577. Wait, let me check. For primes p ≡ 1 mod 4, if a is a quadratic residue, then the solutions of x² ≡ a mod p are x ≡ ±a^((p+1)/4) mod p when p ≡ 5 mod 8 or p ≡ 1 mod 8? Wait, maybe I need to check. Actually, there's a formula when p ≡ 3 mod 4, then x ≡ ±a^((p+1)/4) mod p. But for p ≡ 1 mod 4, maybe we need another method. Hmm, perhaps I should use Tonelli-Shanks here.\n\nAlternatively, perhaps we can use the fact that 577 is 1 mod 4, so there exist integers x and y such that x² + y² = 577, but I don't think that helps directly.\n\nAlternatively, let's note that 577 is a prime where 577 ≡ 1 mod 3, 577 ≡ 1 mod 4, etc. Wait, perhaps I can try using the Tonelli-Shanks algorithm manually. Let me recall how that works.\n\nThe Tonelli-Shanks algorithm is used to find square roots modulo a prime p. Let me set p=577, n=12. The steps are as follows:\n\n1. Express p-1 as Q*2^s. Here, p-1=576=576. Let's factor 576: 576= 2^6 * 9= 2^6 *3^2. So, Q=9, s=6.\n\n2. Find a quadratic non-residue z. Since we know that 3 is a quadratic residue (from earlier calculations?), or maybe not. Let's check. Let's check if 2 is a quadratic residue. Compute (2/577). Since 577 ≡ 1 mod 8 (577=720-143, 720 is divisible by 8, 720-143=577; 577=8*72 +1, so yes, 577 ≡ 1 mod 8. Therefore, 2 is a quadratic residue modulo 577. So (2/577)=1. What about 3? Earlier, we found that (3/577)=1. Hmm, 5? Let's compute (5/577). Using quadratic reciprocity:\n\n(5/577) = (577/5)*(-1)^((5-1)(577-1)/4) = (577 mod 5 /5) * (-1)^(4*576/4) = (577 mod5=577-5*115=577-575=2, so (2/5)) * (-1)^576. (2/5)= -1 because 5≡5 mod8, and 2 is a non-residue mod 5. (-1)^576=1. So (5/577)= (-1)*1= -1. Therefore, 5 is a quadratic non-residue modulo 577. So z=5.\n\n3. Initialize variables: M=s=6, c=z^Q mod p=5^9 mod577. Let's compute 5^9. 5^2=25, 5^4=625≡625-577=48 mod577. 5^8=48^2=2304. 2304 divided by 577: 577*3=1731, 2304-1731=573. So 5^8≡573 mod577. Then 5^9=5^8*5=573*5=2865. 2865 divided by 577: 577*4=2308, 2865-2308=557. So 5^9≡557 mod577.\n\nSet c=557. t = n^Q mod p=12^9 mod577. Let's compute 12^9 mod577.\n\nCompute 12^2=144, 12^4=144^2=20736. 20736 mod577: Let's divide 20736 by577. 577*35=20195. 20736-20195=541. So 12^4≡541 mod577. Then 12^8=(12^4)^2≡541^2 mod577. Compute 541^2=292,681. Divide 292,681 by577:\n\nFind how many times 577 goes into 292,681. Let's see 577*500=288,500. 292,681-288,500=4,181. 577*7=4,039. 4,181-4,039=142. So 541^2 ≡142 mod577. Then 12^9=12^8*12=142*12=1,704. 1,704 mod577: 577*2=1,154. 1,704 -1,154=550. So t=550 mod577.\n\nSet R = n^((Q+1)/2) mod p =12^( (9+1)/2 )=12^5 mod577. 12^2=144, 12^4=541 as before. 12^5=12^4*12=541*12=6,492. 6,492 mod577: 577*11=6,347. 6,492 -6,347=145. So R=145.\n\nSo now, the variables are M=6, c=557, t=550, R=145.\n\nLoop:\n\nWhile t ≠1, repeat:\n\nFind the smallest integer i in [0, M) such that t^(2^i) ≡1 mod p.\n\nCompute t=550. Check t^(2^0)=550≡550≠1. t^(2^1)=550² mod577. Compute 550²=302,500. 577*524=577*(500+24)=577*500=288,500 +577*24=13,848 → 288,500 +13,848=302,348. 302,500 -302,348=152. So t^(2^1)=152≠1. Next, t^(2^2)=152²=23,104. 23,104 mod577: 577*40=23,080. 23,104 -23,080=24. So 24≠1. t^(2^3)=24²=576≡-1 mod577≠1. t^(2^4)=(-1)²=1 mod577. So i=4. Wait, wait. Let's check: 24 squared is 576, which is 577-1, so 576≡-1 mod577. Then, (-1)^2=1. So t^(2^4)=1. But M is 6, and the current exponent is 2^4. So the smallest i where t^(2^i)=1 is i=4. \n\nBut the loop says find the smallest i in [0, M) such that t^(2^i)≡1. So starting from i=0 up:\n\nt^(2^0)=550 ≠1\n\nt^(2^1)=152 ≠1\n\nt^(2^2)=24 ≠1\n\nt^(2^3)=576≡-1 ≠1\n\nt^(2^4)=1. So i=4.\n\nSet b = c^(2^(M - i -1)) mod p. Here, M=6, i=4. So exponent is 2^(6 -4 -1)=2^1=2. So b=557^(2) mod577. 557²=310,249. Compute 310,249 mod577. Let's compute 577*537=577*(500+37)=577*500=288,500 +577*37=21,349. Total 288,500 +21,349=309,849. 310,249 -309,849=400. So b=400 mod577.\n\nUpdate variables:\n\nR = R*b mod577=145*400=58,000. 58,000 mod577: 577*100=57,700. 58,000-57,700=300. So R=300.\n\nt = t*b² mod577=550*(400²) mod577. 400²=160,000. 160,000 mod577: Let's find 577*277=577*(200+77)=577*200=115,400 +577*77=44,429. Total 115,400 +44,429=159,829. 160,000 -159,829=171. So 400²≡171 mod577. Then t=550*171=94,050. 94,050 mod577: 577*162=577*(160+2)=577*160=92,320 +577*2=1,154 → 92,320 +1,154=93,474. 94,050 -93,474=576. So t=576≡-1 mod577.\n\nc = b² mod577=400²=171 as above.\n\nSet M=i=4.\n\nNow, loop again since t=-1≠1.\n\nFind the smallest i in [0,4) such that (-1)^(2^i) ≡1. Wait, (-1)^(2^i) =1 for i≥1, because (-1)^2=1. So the smallest i is 1.\n\nSet i=1.\n\nCompute b = c^(2^(M - i -1)) =171^(2^(4 -1 -1))=171^(2^2)=171^4 mod577. Wait, first, exponent is 2^(4 -1 -1)=2^(2)=4. So b=171^4 mod577.\n\nBut perhaps we can compute this step by step. Let's compute 171²=29,241. 29,241 mod577. Let's divide 29,241 by577. 577*50=28,850. 29,241-28,850=391. So 171²≡391 mod577. Then 391²=152,881. 152,881 mod577. Let's compute 577*264=577*(200+64)=577*200=115,400 +577*64=577*(60+4)=34,620 +2,308=36,928. Total 115,400 +36,928=152,328. 152,881 -152,328=553. So 171^4≡553 mod577.\n\nSet b=553.\n\nUpdate variables:\n\nR = R*b mod577=300*553=165,900. 165,900 mod577. Let's compute 577*287=577*(200+80+7)=577*200=115,400 +577*80=46,160 +577*7=4,039. Total 115,400+46,160=161,560 +4,039=165,599. 165,900 -165,599=301. So R=301 mod577.\n\nt = t*b²= (-1)*(553)^2 mod577. Compute 553²=305,809. 305,809 mod577. Let's compute 577*529=577*(500+29)=577*500=288,500 +577*29=16,733. Total 288,500 +16,733=305,233. 305,809 -305,233=576. So 553²≡576≡-1 mod577. Then t= (-1)*(-1)=1 mod577.\n\nc = b²≡553²≡-1 mod577.\n\nSet M=i=1.\n\nNow, t=1, so we exit the loop.\n\nSo the solution is R=301 and p - R=577 -301=276. Therefore, the square roots of 12 mod577 are 301 and 276.\n\nWait, let me verify: 301² mod577. Let's compute 300²=90,000. 301²=90,000 +2*300 +1=90,000 +600 +1=90,601. 90,601 mod577. Let's see 577*156=577*(150+6)=577*150=86,550 +577*6=3,462 → 86,550 +3,462=90,012. 90,601 -90,012=589. 589 -577=12. So 301²≡12 mod577. Correct. Similarly, 276²=(577 -301)²= (-301)^2≡301²≡12 mod577. So that's correct.\n\nTherefore, the solutions to x²≡12 mod577 are x=301 and x=276.\n\nNow, we need to solve x²≡565 mod577. Let's apply the same Tonelli-Shanks algorithm for 565. Let me note that 565 ≡-12 mod577. So maybe there's a relationship between the square roots here.\n\nBut let's proceed step by step. Using Tonelli-Shanks on n=565, p=577.\n\nAgain, p=577, n=565. Let's go through the algorithm again.\n\n1. p-1=576=9*2^6, so Q=9, s=6.\n\n2. Find a quadratic non-residue z. As before, z=5.\n\n3. Initialize variables:\n\nc = z^Q mod p=5^9 mod577=557 as before.\n\nt =n^Q mod577=565^9 mod577. Hmm, 565 ≡-12 mod577. So 565 ≡-12. Therefore, 565^9 ≡ (-12)^9 mod577. Since 9 is odd, (-12)^9= -12^9. So we can compute 12^9 mod577 first. Earlier, we found that 12^9 ≡550 mod577. Therefore, (-12)^9≡-550≡577 -550=27 mod577. Wait, but 12^9=550 mod577, so (-12)^9= -550 mod577. But -550 +577=27. So t=27 mod577.\n\nSet R =n^((Q+1)/2) mod577=565^5 mod577. Since 565≡-12, R=(-12)^5 mod577. (-12)^5= -12^5. Compute 12^2=144, 12^4=144²=20,736≡541 mod577 (as before). 12^5=12^4*12=541*12=6,492≡145 mod577 (as before). So (-12)^5≡-145≡577 -145=432 mod577. So R=432.\n\nVariables: M=6, c=557, t=27, R=432.\n\nLoop:\n\nWhile t ≠1:\n\nFind the smallest i where t^(2^i) ≡1.\n\nCompute t=27. Let's compute t^(2^0)=27≠1.\n\nt^(2^1)=27²=729≡729-577=152 mod577. Not 1.\n\nt^(2^2)=152²=23,104≡23,104 mod577. Wait, earlier we had 152²=23,104-577*40=23,104 -23,080=24 mod577. So 24≠1.\n\nt^(2^3)=24²=576≡-1 mod577≠1.\n\nt^(2^4)=(-1)^2=1. So i=4.\n\nSet b = c^(2^(M - i -1)) =557^(2^(6 -4 -1))=557^(2^1)=557² mod577. 557²=310,249. Earlier, we saw that 557²≡400 mod577. So b=400.\n\nUpdate variables:\n\nR = R*b mod577=432*400=172,800. 172,800 mod577. Let's compute 577*299=577*(300 -1)=577*300=173,100 -577=173,100 -577=172,523. 172,800 -172,523=277. So R=277.\n\nt = t*b²=27*(400²)=27*171=4,617. 4,617 mod577: 577*8=4,616. So 4,617 -4,616=1. So t=1.\n\nc = b²=400²=171 mod577.\n\nSet M=i=4.\n\nNow t=1, so exit loop.\n\nThus, the solutions are R=277 and p-R=577 -277=300.\n\nWait, but check 277² mod577. Let's compute 277²=76,729. 577*133=577*(100+30+3)=577*100=57,700 +577*30=17,310 +577*3=1,731. Total 57,700+17,310=75,010 +1,731=76,741. 76,729 -76,741= -12. But 277² ≡-12 mod577. So that's correct because 277²≡-12 mod577, which is 565. Therefore, 277²=565 and 300²=565 as well (since 300=577 -277). Wait, 300 squared is same as (-277)^2=277²≡565. So, yes, x=277 and x=300 are the solutions to x²≡565 mod577.\n\nTherefore, the solutions to the original equation x⁴≡144 mod577 are the solutions to x²≡12 mod577, which are 301 and 276, and the solutions to x²≡565 mod577, which are 277 and 300.\n\nSo all solutions are 277, 300, 276, 301.\n\nBut wait, let's confirm each of these:\n\n301² ≡12 mod577 (we did that earlier).\n\n276²= (577 -301)^2≡301²≡12 mod577. Correct.\n\n277²≡565 mod577. Correct.\n\n300²= (577 -277)^2≡277²≡565 mod577. Correct.\n\nTherefore, the fourth roots are 277, 300, 276, 301. All of these are less than 577 (since 301, 277, etc. are all less than 577). So the values of k are 276, 277, 300, 301.\n\nNow, the arithmetic mean is (276 +277 +300 +301)/4. Let's compute that:\n\n276 +277 = 553\n\n300 +301 = 601\n\nTotal sum: 553 +601=1154\n\nMean: 1154 /4 =288.5\n\nThe greatest integer less than or equal to 288.5 is 288. Therefore, the answer is 288.\n\nBut let me double-check the arithmetic to be sure. \n\n276 +277: 276 +277. 200+200=400, 70+70=140, 6+7=13. So total 400+140=540 +13=553. Correct.\n\n300 +301=601. Correct.\n\n553 +601=1154. Yes.\n\n1154 divided by 4: 4*288=1152, so 1154=4*288 +2, so 1154/4=288.5. Floor of that is 288. Correct.\n\nSo the final answer is 288.\n</think>To find the arithmetic mean of all positive integers \\( k < 577 \\) such that \\( k^4 \\equiv 144 \\pmod{577} \\), we need to solve the congruence \\( k^4 \\equiv 144 \\pmod{577} \\). This involves finding the fourth roots of 144 modulo 577. We start by solving the quadratic congruences \\( x^2 \\equiv 12 \\pmod{577} \\) and \\( x^2 \\equiv 565 \\pmod{577} \\).\n\n### Step 1: Solve \\( x^2 \\equiv 12 \\pmod{577} \\)\n\nUsing the Tonelli-Shanks algorithm:\n1. Express \\( p-1 = 576 = 9 \\cdot 2^6 \\), so \\( Q = 9 \\) and \\( s = 6 \\).\n2. Find a quadratic non-residue \\( z \\). Since 5 is a quadratic non-residue modulo 577, we use \\( z = 5 \\).\n3. Initialize variables:\n   - \\( c = z^Q \\equiv 5^9 \\equiv 557 \\pmod{577} \\)\n   - \\( t = 12^Q \\equiv 12^9 \\equiv 550 \\pmod{577} \\)\n   - \\( R = 12^{(Q+1)/2} \\equiv 12^5 \\equiv 145 \\pmod{577} \\)\n\n4. Loop:\n   - Find the smallest \\( i \\) such that \\( t^{2^i} \\equiv 1 \\pmod{577} \\).\n   - For \\( t = 550 \\):\n     - \\( t^{2^0} = 550 \\)\n     - \\( t^{2^1} = 152 \\)\n     - \\( t^{2^2} = 24 \\)\n     - \\( t^{2^3} = 576 \\equiv -1 \\pmod{577} \\)\n     - \\( t^{2^4} = 1 \\)\n     - Thus, \\( i = 4 \\).\n\n5. Update variables:\n   - \\( b = c^{2^{M-i-1}} \\equiv 557^{2^1} \\equiv 400 \\pmod{577} \\)\n   - \\( R = R \\cdot b \\equiv 145 \\cdot 400 \\equiv 300 \\pmod{577} \\)\n   - \\( t = t \\cdot b^2 \\equiv 550 \\cdot 171 \\equiv 1 \\pmod{577} \\)\n   - \\( c = b^2 \\equiv 400^2 \\equiv 171 \\pmod{577} \\)\n   - \\( M = i = 4 \\)\n\nSince \\( t = 1 \\), we exit the loop. The solutions are \\( R = 301 \\) and \\( p - R = 276 \\).\n\n### Step 2: Solve \\( x^2 \\equiv 565 \\pmod{577} \\)\n\nUsing the same Tonelli-Shanks algorithm:\n1. Express \\( p-1 = 576 = 9 \\cdot 2^6 \\), so \\( Q = 9 \\) and \\( s = 6 \\).\n2. Initialize variables:\n   - \\( c = z^Q \\equiv 557 \\pmod{577} \\)\n   - \\( t = 565^Q \\equiv (-12)^9 \\equiv 27 \\pmod{577} \\)\n   - \\( R = 565^{(Q+1)/2} \\equiv (-12)^5 \\equiv 432 \\pmod{577} \\)\n\n3. Loop:\n   - Find the smallest \\( i \\) such that \\( t^{2^i} \\equiv 1 \\pmod{577} \\).\n   - For \\( t = 27 \\):\n     - \\( t^{2^0} = 27 \\)\n     - \\( t^{2^1} = 152 \\)\n     - \\( t^{2^2} = 24 \\)\n     - \\( t^{2^3} = 576 \\equiv -1 \\pmod{577} \\)\n     - \\( t^{2^4} = 1 \\)\n     - Thus, \\( i = 4 \\).\n\n4. Update variables:\n   - \\( b = c^{2^{M-i-1}} \\equiv 557^{2^1} \\equiv 400 \\pmod{577} \\)\n   - \\( R = R \\cdot b \\equiv 432 \\cdot 400 \\equiv 277 \\pmod{577} \\)\n   - \\( t = t \\cdot b^2 \\equiv 27 \\cdot 171 \\equiv 1 \\pmod{577} \\)\n   - \\( c = b^2 \\equiv 400^2 \\equiv 171 \\pmod{577} \\)\n   - \\( M = i = 4 \\)\n\nSince \\( t = 1 \\), we exit the loop. The solutions are \\( R = 277 \\) and \\( p - R = 300 \\).\n\n### Step 3: Compute the Arithmetic Mean\n\nThe solutions to \\( k^4 \\equiv 144 \\pmod{577} \\) are \\( 276, 277, 300, 301 \\).\n\nSumming these solutions:\n\\[\n276 + 277 + 300 + 301 = 1154\n\\]\n\nDividing by 4:\n\\[\n\\frac{1154}{4} = 288.5\n\\]\n\nThe greatest integer less than or equal to 288.5 is \\(\\boxed{288}\\)."}