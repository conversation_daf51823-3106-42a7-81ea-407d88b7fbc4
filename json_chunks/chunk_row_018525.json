{"row_number": 18525, "expected_answer": "0", "problem": "Calculate \\( \\lim_{n\\to\\infty} \\int_0^{1} x^n \\cdot \\cos x \\, \\mathrm{dx} \\).", "generated_solution": "<think>\nOkay, so I need to find the limit as n approaches infinity of the integral from 0 to 1 of x^n times cos(x) dx. Hmm, let's think about this step by step. \n\nFirst, I remember that when dealing with integrals involving x^n and some function, especially as n becomes very large, the behavior of x^n can dominate. Because x is between 0 and 1, raising it to the power of n will make it approach 0 as n increases, except at x=1 where x^n is always 1. So maybe the integral is concentrated near x=1 when n is large. But how does that affect the integral?\n\nWait, maybe I should split the integral into two parts: one very close to 1 and the rest. Like, for any small epsilon > 0, split the integral from 0 to 1-epsilon and from 1-epsilon to 1. Then as n goes to infinity, the integral from 0 to 1-epsilon would go to 0 because x^n becomes negligible there, right? And the integral from 1-epsilon to 1 might approach the value of cos(1) times the integral of x^n over that interval. But since epsilon is small, maybe cos(x) is approximately cos(1) there. Then as epsilon approaches 0, the integral near 1 might approach cos(1) times the integral of x^n from 0 to 1, which would be cos(1)/(n+1). But wait, if we're taking the limit as n approaches infinity, then that term would go to 0 as well. Hmm, but that can't be right. Wait, maybe I need to use the Dominated Convergence Theorem or something?\n\nAlternatively, maybe just evaluate the integral directly for finite n and see what happens as n approaches infinity. Let's try integrating by parts. Let me set u = cos(x) and dv = x^n dx. Then du = -sin(x) dx and v = x^{n+1}/(n+1). So integrating by parts gives:\n\nuv from 0 to 1 minus integral from 0 to 1 of v du. That would be [cos(1)*1^{n+1}/(n+1) - cos(0)*0^{n+1}/(n+1)] minus integral from 0 to 1 of x^{n+1}/(n+1)*(-sin(x)) dx. Simplifying that, cos(1)/(n+1) - 0 + (1/(n+1)) integral from 0 to 1 x^{n+1} sin(x) dx.\n\nSo the original integral is equal to cos(1)/(n+1) + [1/(n+1)] integral x^{n+1} sin(x) dx from 0 to 1.\n\nNow, the question is, what happens to this expression as n approaches infinity. The first term is cos(1)/(n+1), which clearly approaches 0 as n becomes large. The second term is 1/(n+1) multiplied by the integral of x^{n+1} sin(x) dx. Let's look at that integral. Since |x^{n+1} sin(x)| ≤ x^{n+1} because |sin(x)| ≤ 1. Therefore, the integral from 0 to 1 of x^{n+1} dx is 1/(n+2). So the integral is bounded by 1/(n+2). Therefore, the second term is bounded by 1/(n+1) * 1/(n+2) ≈ 1/n², which also goes to 0 as n approaches infinity. So both terms go to 0, so the limit should be 0.\n\nWait, but maybe there's a more straightforward way. Let's think about the function x^n cos(x). For each x in [0,1), x^n tends to 0 as n→∞. At x=1, it's cos(1). So pointwise, the function converges to 0 almost everywhere on [0,1) and cos(1) at x=1. But since a single point doesn't contribute to the integral, maybe the integral converges to 0. \n\nBut we need to be careful here. Even if a function converges pointwise, the integral might not converge to the integral of the limit unless we have uniform convergence or apply a theorem like Dominated Convergence Theorem. Let's check if the Dominated Convergence Theorem applies here. \n\nThe DCT requires that |x^n cos(x)| ≤ g(x) for some integrable function g(x) on [0,1]. Since |cos(x)| ≤ 1, then |x^n cos(x)| ≤ x^n. But on [0,1], x^n is dominated by 1, which is integrable on [0,1]. Therefore, by DCT, we can interchange the limit and the integral:\n\nlim_{n→∞} ∫₀¹ x^n cos(x) dx = ∫₀¹ lim_{n→∞} x^n cos(x) dx.\n\nBut lim_{n→∞} x^n is 0 for x in [0,1) and 1 at x=1. So the integrand becomes 0 except at x=1. Since the integral over a single point is 0, the entire integral is 0. Therefore, the limit is 0.\n\nTherefore, the answer should be 0. \n\nAlternatively, maybe even without DCT, since for each x in [0,1), x^n tends to 0, and for x=1, x^n=1, but in the integral, the contribution from x=1 is negligible. So even if we bound the integral, since the maximum value of cos(x) is 1, the integral is bounded by ∫₀¹ x^n dx = 1/(n+1), which approaches 0 as n→∞. Therefore, by the squeeze theorem, the limit is 0.\n\nYes, that seems correct. So the answer is 0.\n</think>To calculate \\( \\lim_{n\\to\\infty} \\int_0^{1} x^n \\cdot \\cos x \\, \\mathrm{dx} \\), we can use the Dominated Convergence Theorem (DCT).\n\nFirst, observe the behavior of the function \\( x^n \\cos x \\) as \\( n \\to \\infty \\):\n\n1. For \\( x \\in [0, 1) \\), \\( x^n \\to 0 \\) as \\( n \\to \\infty \\).\n2. At \\( x = 1 \\), \\( x^n = 1 \\), so \\( x^n \\cos x = \\cos 1 \\).\n\nThus, the pointwise limit of \\( x^n \\cos x \\) as \\( n \\to \\infty \\) is:\n\\[ \\lim_{n \\to \\infty} x^n \\cos x = \\begin{cases} \n0 & \\text{if } x \\in [0, 1) \\\\\n\\cos 1 & \\text{if } x = 1 \n\\end{cases} \\]\n\nNext, we need to apply the Dominated Convergence Theorem. The DCT requires that there exists an integrable function \\( g(x) \\) such that \\( |x^n \\cos x| \\leq g(x) \\) for all \\( n \\) and \\( x \\in [0, 1] \\). Since \\( |\\cos x| \\leq 1 \\), we have:\n\\[ |x^n \\cos x| \\leq x^n \\leq 1 \\]\nThe function \\( g(x) = 1 \\) is integrable on \\([0, 1]\\).\n\nBy the Dominated Convergence Theorem, we can interchange the limit and the integral:\n\\[ \\lim_{n \\to \\infty} \\int_0^1 x^n \\cos x \\, \\mathrm{dx} = \\int_0^1 \\lim_{n \\to \\infty} x^n \\cos x \\, \\mathrm{dx} \\]\n\nThe integrand \\( \\lim_{n \\to \\infty} x^n \\cos x \\) is 0 almost everywhere on \\([0, 1]\\), except at \\( x = 1 \\), where it is \\(\\cos 1\\). However, the integral of a function that is zero everywhere except at a single point is zero:\n\\[ \\int_0^1 \\lim_{n \\to \\infty} x^n \\cos x \\, \\mathrm{dx} = \\int_0^1 0 \\, \\mathrm{dx} = 0 \\]\n\nThus, the limit is:\n\\[ \\lim_{n \\to \\infty} \\int_0^1 x^n \\cos x \\, \\mathrm{dx} = 0 \\]\n\nTherefore, the final answer is:\n\\[\n\\boxed{0}\n\\]"}