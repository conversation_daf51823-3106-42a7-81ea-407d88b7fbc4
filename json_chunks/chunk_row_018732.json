{"row_number": 18732, "expected_answer": "4", "problem": "Find the positive integers $n$ not divisible by $3$, for which $2^{n^2-10}+2133$ is a perfect cube.", "generated_solution": "<think>\nOkay, so I need to find positive integers n that aren't divisible by 3, such that when I take 2 raised to the power of (n² - 10) and add 2133 to it, the result is a perfect cube. Hmm, let me start by understanding what's being asked here.\n\nFirst, let me note that n must be a positive integer, and 3 doesn't divide n. So n can be 1, 2, 4, 5, 7, 8, etc. But obviously, the problem is likely to have a small solution, so maybe I don't need to check too large numbers. Let's see.\n\nThe expression given is 2^{n² - 10} + 2133, and this should be equal to some cube, say k³, where k is a positive integer. So, 2^{n² - 10} = k³ - 2133. Therefore, k³ must be greater than 2133, so k must be at least the cube root of 2133. Let me calculate that. The cube root of 2133 is approximately 12.88, so k needs to be at least 13.\n\nSo possible k values start at 13. Let's check if k³ - 2133 is a power of 2. If I can find such k, then I can set 2^{n² - 10} equal to that and solve for n. Also, since 2^{n² - 10} must be a positive integer, n² - 10 must be a non-negative integer. Thus, n² ≥ 10, which implies n ≥ 4 (since 3²=9 is less than 10). Wait, 4² is 16, which is 16 -10 = 6, so exponents start at 6. So n must be at least 4. But the problem says n is a positive integer not divisible by 3, so n can be 4,5,7,8, etc.\n\nWait, but 2^{n² -10} must be a positive integer, so n² -10 must be non-negative. So n² ≥10, so n ≥ 4, since 3²=9 which is less than 10, and 4²=16. So n starts at 4, and must not be divisible by 3.\n\nSo possible n values: 4,5,7,8,10,11,13, etc. But let's see if we can limit the possible k's.\n\nLet me list k starting at 13 and compute k³ - 2133, check if the result is a power of 2.\n\nStarting with k=13: 13³ = 2197. 2197 - 2133 = 64. 64 is 2^6. So that's a power of 2. So 64=2^6. Therefore, 2^{n² -10}=64=2^6. Therefore, n² -10=6, so n²=16, so n=4. But 4 is not divisible by 3. So 4 is a solution. Let me check that. 2^{4²-10} +2133=2^{16-10}+2133=2^6 +2133=64+2133=2197=13³. Correct. So n=4 is a solution.\n\nNext, check k=14: 14³=2744. 2744 -2133=611. 611 is not a power of 2. Since 512 is 2^9=512, and 1024 is 2^10=1024. 611 is between 512 and 1024, but not a power of 2. So no.\n\nk=15: 15³=3375. 3375 -2133=1242. 1242 is not a power of 2. 1024 and 2048, so no.\n\nk=16: 16³=4096. 4096 -2133=1963. Not a power of 2.\n\nk=17: 17³=4913. 4913-2133=2780. Not a power of 2.\n\nk=12: Wait, earlier I thought k starts at 13 because cube root of 2133 is approx 12.88, so k=12 would be 12³=1728, which is less than 2133, so 1728-2133 would be negative, but 2^{n²-10} must be positive, so k³ must be greater than 2133. Hence, k=12 is invalid. So starting at k=13 is correct.\n\nk=18: 18³=5832. 5832 -2133=3699. Not a power of 2.\n\nk=19: 19³=6859. 6859-2133=4726. Not a power of 2.\n\nk=20: 8000 -2133=5867. Not a power of 2.\n\nHmm, as k increases, the difference k³ -2133 grows rapidly. Powers of 2 also grow, but exponentially, but cube grows faster. However, maybe there's another value where k³ -2133 is a power of 2. Let me check a few more k's.\n\nk=11: Wait, even though 11³=1331 which is less than 2133, but perhaps check to be thorough. 1331 -2133 is negative, so invalid.\n\nk=21: 21³=9261. 9261 -2133=7128. Not a power of 2.\n\nk=22: 10648 -2133=8515. Not a power of 2.\n\nk=23: 12167 -2133=10034. Not a power of 2.\n\nk=24: 13824 -2133=11691. Not a power of 2.\n\nk=25: 15625 -2133=13492. Not a power of 2.\n\nk=26: 17576 -2133=15443. Not a power of 2.\n\nk=27: 19683 -2133=17550. Not a power of 2.\n\nk=28: 21952 -2133=19819. Not a power of 2.\n\nWait, 2^14=16384, 2^15=32768. So between k=25 (13492) and k=30 (27000-2133=24867), so maybe somewhere in between. But none of the differences so far are powers of 2. Let's check for k=32: 32³=32768. 32768 -2133=30635. Which is less than 32768, but not a power of 2. 2^15=32768, so 30635 is less than that. Not a power.\n\nAlternatively, maybe there's another solution. Wait, but the equation is 2^{n² -10} =k³ -2133. The left-hand side is a power of 2, so the right-hand side must be a power of 2 as well. Therefore, k³ must be 2133 plus a power of 2. So we need to find k such that k³ is 2133 plus 2^m, where m is n² -10.\n\nGiven that 2133 is a specific number, perhaps we can factor it to see if there's something we can use. Let's factor 2133. 2133 divided by 3 is 711. Then 711 divided by 3 is 237. Then 237 divided by 3 is 79. So 2133=3³ × 79. Hmm, 3³ is 27, 27×79=2133.\n\nSo 2133=3³×79. So if I can write k³ as 3³×79 + 2^m. Maybe there's a way to factor this, but I'm not sure. Alternatively, since k³ - 2^m =2133, perhaps we can look for k and m such that this holds.\n\nBut maybe it's better to stick to the initial approach where we check for k starting at 13 and see if k³ -2133 is a power of 2. We already found k=13 gives 64=2^6, which gives n=4. Let's check higher k's for possible other powers of 2.\n\nWait, another thought: If 2^{n² -10} +2133=k³, then modulo 3. Since n is not divisible by 3, n can be 1 or 2 mod 3. Let me check what 2^{n² -10} is modulo 3.\n\nSince 2 mod 3 is 2. 2^1=2 mod3, 2^2=4=1 mod3, 2^3=2 mod3, etc., so the cycle is 2,1,2,1,... every exponent. So the exponent is n² -10. Let's compute n² -10 modulo 2 (since the cycle length is 2 for modulus 3). So n² mod 3. Since n is not divisible by 3, n can be 1 or 2 mod3.\n\nIf n≡1 mod3, then n²≡1²=1 mod3.\n\nIf n≡2 mod3, then n²≡4≡1 mod3.\n\nTherefore, regardless of n not divisible by3, n²≡1 mod3. Therefore, n² -10 ≡1 -10 ≡-9≡0 mod3. Therefore, n² -10 ≡0 mod3. Therefore, exponent is 0 mod2? Wait, the exponent is n² -10. Let me see: since n² -10 is 0 mod3, but how does that relate to the cycle of 2 modulo3. The cycle is 2,1,2,1,... every even exponent: if exponent is even, 2^even ≡1 mod3; exponent odd, 2^odd ≡2 mod3.\n\nBut since exponent is n² -10 ≡0 mod3. Wait, exponent is n² -10. Let's check modulus 2 for the exponent, since the cycle is of length 2. Wait, n² -10 modulo 2: n² is even or odd. Since n is an integer, if n is even, n² is even; if n is odd, n² is odd. So n² -10 modulo2: if n even, then 0 -0=0 mod2. If n odd, 1 -0=1 mod2. So exponent is even or odd depending on n's parity. However, 2^{exponent} mod3 cycles every 2. Let's compute 2^{exponent} mod3.\n\nIf exponent is even, 2^{exponent} ≡1 mod3. If exponent is odd, 2^{exponent} ≡2 mod3.\n\nNow, the left-hand side is 2^{exponent} +2133. Let's compute this modulo3. 2133 is divisible by3 (since 2133=3³×79), so 2133≡0 mod3. Therefore, 2^{exponent} +2133 ≡2^{exponent} mod3. So the entire expression is ≡2^{exponent} mod3. However, the right-hand side is k³. Let's compute k³ mod3.\n\nPossible cubes modulo3: 0³=0, 1³=1, 2³=8≡2 mod3. So k³ can be 0,1,2 mod3. But since k³=2^{exponent} +2133≡2^{exponent} mod3.\n\nThus, possible cases:\n\nIf exponent is even: 2^{exponent} ≡1 mod3. Therefore, k³≡1 mod3. Which implies k≡1 mod3.\n\nIf exponent is odd: 2^{exponent}≡2 mod3. Therefore, k³≡2 mod3. Which implies k≡2 mod3.\n\nSo depending on the parity of the exponent (n² -10), which is equivalent to n's parity (since n² -10 mod2 is 0 if n even, 1 if n odd). So if n is even, exponent is even (since n even implies n² even, even minus 10 is even). If n is odd, exponent is odd. Wait, n even: n² even, 10 even. Even - even = even. So exponent is even. If n odd: n² odd, 10 even. Odd - even = odd. So exponent is odd. Therefore, when n is even, exponent is even, so 2^{even} ≡1 mod3. Therefore, k³≡1 mod3, so k≡1 mod3. When n is odd, exponent is odd, so 2^{odd}≡2 mod3, so k³≡2 mod3, so k≡2 mod3.\n\nTherefore, this gives us additional constraints on k. For example, in our first solution, n=4 (even), so k should be ≡1 mod3. Indeed, k=13≡1 mod3 (13/3=4*3 +1, yes). So that checks out.\n\nBut how can this help? Maybe it can help narrow down the k's we need to check. So, for each n even (not divisible by3), we need k≡1 mod3. For n odd (also not divisible by3), we need k≡2 mod3.\n\nBut perhaps since we already found n=4 as a solution, and tried k=13,14,15,16,... up to 28 and found no other k's where k³ -2133 is a power of2, maybe there's another solution at a higher k. However, since 2^{n² -10} grows exponentially, but k³ grows cubically, the gap between consecutive powers of2 increases, while the gap between k³ terms also increases. So maybe there's only a small solution.\n\nAlternatively, maybe n=5. Let's check n=5. Then n²-10=25-10=15. So 2^15=32768. Then 32768 +2133=34901. Is 34901 a cube? Let's compute cube of 32: 32³=32768, 33³=35937. So 34901 is between 32³ and 33³. Not a cube. So not a cube.\n\nn=5 is not a solution. Next, n=7. n² -10=49-10=39. 2^39 is a huge number. 2^10=1024, 2^20=1,048,576, 2^30≈1 billion, 2^40≈1 trillion. So 2^39≈549,755,813,500. Adding 2133 gives roughly 549,755,815,633. Now, the cube root of that number is roughly (549,755,815,633)^(1/3). Let's see, 1000³=1,000,000,000, so 549,755,815,633 is about 549.756 billion. So cube root would be approximately 8,193 (since 8192³= 2^39=549,755,813,500). Wait, 8192 is 2^13. So 8192³=2^39. So 2^39 +2133=8192³ +2133. So that would be equal to k³=8192³ +2133. But the difference between consecutive cubes (k+1)³ -k³=3k²+3k+1. So unless 2133 is equal to that expression for some k, but 2133 is much larger than that for k=8192. Wait, this is not feasible. 8192³ is a huge number, adding 2133 would not make it a perfect cube because the next cube after 8192³ is 8193³, which is 8193³ -8192³=3*8192² +3*8192 +1, which is way larger than 2133. Therefore, 8192³ +2133 cannot be a perfect cube. So n=7 is not a solution.\n\nSimilarly, n=8. n=8 is even, not divisible by3. n² -10=64-10=54. 2^54 is a huge number. 2^10=1024, 2^20≈million, 2^30≈billion, 2^40≈trillion, 2^50≈quadrillion, so 2^54≈18,014,398,509,481,984. Adding 2133 gives a number which is still around 1.8e16. The cube root of that is approximately (2^54)^(1/3)=2^(54/3)=2^18=262,144. Therefore, k≈262,144. Then k³=2^54. But k=2^18. Therefore, k³=2^54. So 2^54 +2133. This is not a perfect cube. Because 2^54 is already a cube (since 54 is a multiple of3: 54=3*18), so 2^54=(2^18)^3. Therefore, 2^54 +2133=(2^18)^3 +2133, which is not a cube, as adding 2133 would not make it the next cube. The next cube after (2^18) is (2^18 +1)^3=(262,144 +1)^3=262,145³. The difference between them is 3*(262,144)^2 +3*262,144 +1, which is way larger than 2133. Therefore, this can't be a cube. So n=8 is invalid.\n\nSo, going further, the exponent n² -10 increases as n increases, making 2^{n² -10} extremely large, and the difference between consecutive cubes around that number is much larger than 2133, making it impossible for 2^{n² -10} +2133 to be a cube for larger n.\n\nTherefore, the only solution is n=4.\n\nWait, but let's check n=2. Wait, n must be at least 4 because n² -10 must be non-negative. For n=4, n²=16-10=6, exponent 6. For n=3, but n=3 is divisible by3, which is excluded. So n=4 is the smallest possible. So, I think the only solution is n=4. Let me verify once again.\n\nWait, what about n=1? n=1 is not divisible by3, but n² -10=1 -10=-9, which would give 2^{-9} +2133. But 2^{-9} is 1/512, which is not an integer, so that's invalid. Similarly, n=2: 4 -10=-6, 2^{-6}=1/64, not integer. n=3 is excluded. So the first possible n is 4. Then 4 gives us a valid solution, and higher n's don't. Therefore, the only solution is n=4.\n\n**Final Answer**\nThe positive integer \\( n \\) is \\boxed{4}.\n</think>To find the positive integers \\( n \\) not divisible by 3 for which \\( 2^{n^2 - 10} + 2133 \\) is a perfect cube, we start by setting the expression equal to \\( k^3 \\), where \\( k \\) is a positive integer. This gives us the equation:\n\n\\[\n2^{n^2 - 10} + 2133 = k^3\n\\]\n\nRearranging, we get:\n\n\\[\n2^{n^2 - 10} = k^3 - 2133\n\\]\n\nFor \\( 2^{n^2 - 10} \\) to be an integer, \\( n^2 - 10 \\) must be non-negative, implying \\( n^2 \\geq 10 \\). Therefore, \\( n \\geq 4 \\) since \\( n \\) is a positive integer not divisible by 3. The possible values for \\( n \\) start from 4, 5, 7, 8, etc.\n\nNext, we need \\( k^3 - 2133 \\) to be a power of 2. The cube root of 2133 is approximately 12.88, so \\( k \\) must be at least 13. We check values of \\( k \\) starting from 13:\n\n- For \\( k = 13 \\):\n  \\[\n  13^3 = 2197 \\quad \\text{and} \\quad 2197 - 2133 = 64 = 2^6\n  \\]\n  This gives \\( 2^{n^2 - 10} = 2^6 \\), so \\( n^2 - 10 = 6 \\). Solving for \\( n \\):\n  \\[\n  n^2 = 16 \\quad \\Rightarrow \\quad n = 4\n  \\]\n  Since 4 is not divisible by 3, this is a valid solution.\n\nWe now check if there are any other possible values of \\( k \\) for which \\( k^3 - 2133 \\) is a power of 2. For \\( k \\geq 14 \\), the differences \\( k^3 - 2133 \\) do not yield powers of 2. For example:\n\n- For \\( k = 14 \\):\n  \\[\n  14^3 = 2744 \\quad \\text{and} \\quad 2744 - 2133 = 611 \\quad \\text{(not a power of 2)}\n  \\]\n\n- For \\( k = 15 \\):\n  \\[\n  15^3 = 3375 \\quad \\text{and} \\quad 3375 - 2133 = 1242 \\quad \\text{(not a power of 2)}\n  \\]\n\nAs \\( k \\) increases, \\( k^3 - 2133 \\) grows rapidly, and the gaps between consecutive cubes become much larger than 2133, making it impossible for \\( 2^{n^2 - 10} + 2133 \\) to be a perfect cube for larger \\( n \\).\n\nFinally, we verify that \\( n = 4 \\) is the only solution. For \\( n \\geq 5 \\), \\( 2^{n^2 - 10} \\) becomes extremely large, and the difference between consecutive cubes around that number is much larger than 2133, making it impossible for \\( 2^{n^2 - 10} + 2133 \\) to be a cube.\n\nThus, the only solution is \\( n = 4 \\).\n\n\\[\n\\boxed{4}\n\\]"}