#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试<think>标签保留功能
"""

import json
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from json_FY import VLLMTranslator

def test_think_preservation():
    """测试<think>标签保留功能"""

    # 创建测试数据
    test_data = {
        "row_number": 1,
        "expected_answer": "14",
        "problem": "Given $\\sqrt{x^2+165}-\\sqrt{x^2-52}=7$ and $x$ is positive, find all possible values of $x$.",
        "generated_solution": "<think>\nOkay, let's see. I need to solve the equation √(x² + 165) - √(x² - 52) = 7, and find all positive values of x. Hmm, radicals can be tricky, but maybe if I can eliminate the square roots by squaring both sides. Let me try that.\n</think>To solve the equation \\(\\sqrt{x^2 + 165} - \\sqrt{x^2 - 52} = 7\\) for positive \\(x\\), we proceed as follows:\n\n1. Start with the given equation:\n   \\[\n   \\sqrt{x^2 + 165} - \\sqrt{x^2 - 52} = 7\n   \\]\n\n2. Isolate one of the square roots by moving \\(\\sqrt{x^2 - 52}\\) to the right side:\n   \\[\n   \\sqrt{x^2 + 165} = 7 + \\sqrt{x^2 - 52}\n   \\]\n\nThus, the only positive solution is:\n\\[\n\\boxed{14}\n\\]"
    }

    print("测试数据:")
    print(json.dumps(test_data, ensure_ascii=False, indent=2))
    print("\n" + "="*60)

    # 初始化翻译器
    translator = VLLMTranslator(
        model_name="/home/<USER>/Model/LLM/Qwen/Qwen3-8B-AWQ",
        max_concurrent=1
    )

    # 检查VLLM连接
    print("🔍 正在检查VLLM服务状态...")
    if not translator.check_vllm_connection():
        print("❌ 无法连接到VLLM服务，请确保服务正在运行")
        return False
    else:
        print("✅ VLLM服务连接正常")

    # 测试单个字段翻译
    print("\n测试单个字段翻译:")
    print("-" * 40)

    # 测试problem字段（应该移除<think>标签）
    print("1. 测试problem字段（应该移除<think>标签）:")
    problem_result = translator.translate_text(test_data["problem"], "problem")
    print(f"原文: {test_data['problem'][:100]}...")
    print(f"译文: {problem_result[:100]}...")
    print(f"包含<think>: {'<think>' in problem_result}")

    # 测试generated_solution字段（应该保留<think>标签）
    print("\n2. 测试generated_solution字段（应该保留<think>标签）:")
    solution_result = translator.translate_text(test_data["generated_solution"], "solution")
    print(f"原文: {test_data['generated_solution'][:100]}...")
    print(f"译文: {solution_result[:100]}...")
    print(f"包含<think>: {'<think>' in solution_result}")

    # 测试JSON翻译
    print("\n测试JSON翻译:")
    print("-" * 40)

    fields_to_translate = ["problem", "generated_solution", "expected_answer"]

    # 构建翻译提示
    fields_info = []
    for field in fields_to_translate:
        if field in test_data and test_data[field] and str(test_data[field]).strip():
            field_type = translator._get_field_type(field) if hasattr(translator, '_get_field_type') else "general"
            fields_info.append(f'"{field}": 需要翻译为中文（类型：{field_type}）')

    prompt = f"""请将以下JSON对象中的指定字段翻译成中文，并返回完整的JSON格式。

原始JSON：
{json.dumps(test_data, ensure_ascii=False, indent=2)}

翻译要求：
{chr(10).join(fields_info)}

翻译规则：
1. 保持所有数学公式、符号、数字完全不变
2. 直接将指定字段的内容翻译为中文，覆盖原字段内容
3. 保持其他字段不变
4. 返回完整的JSON对象，字段名保持不变，只翻译字段内容
5. 不要添加任何解释、说明、思考过程或其他文本
6. 不要使用代码块格式，直接返回纯JSON
7. 重要：字段值必须是纯文本字符串，不要在字段值中嵌套JSON对象
8. 确保返回的是有效的JSON格式
9. 特别重要：如果字段内容包含<think>...</think>标签，请保留这些标签并翻译标签内的内容

请直接返回翻译后的完整JSON对象："""

    print("发送翻译请求...")
    translated_json = translator.translate_json_item(prompt)

    if translated_json:
        print("\n翻译结果:")
        print(json.dumps(translated_json, ensure_ascii=False, indent=2))

        # 检查<think>标签是否被保留
        if "generated_solution" in translated_json:
            solution_content = translated_json["generated_solution"]
            has_think_start = "<think>" in solution_content
            has_think_end = "</think>" in solution_content
            print(f"\ngenerated_solution字段包含<think>标签: {has_think_start}")
            print(f"generated_solution字段包含</think>标签: {has_think_end}")

            if has_think_start and has_think_end:
                print("✅ <think>标签结构完整")
                # 显示<think>标签内的内容
                import re
                think_match = re.search(r'<think>(.*?)</think>', solution_content, re.DOTALL)
                if think_match:
                    think_content = think_match.group(1).strip()
                    print(f"<think>标签内容: {think_content[:100]}...")
            elif has_think_start or has_think_end:
                print("⚠️ <think>标签结构不完整")
                if has_think_start and not has_think_end:
                    print("  - 有<think>但缺少</think>")
                elif not has_think_start and has_think_end:
                    print("  - 有</think>但缺少<think>")
            else:
                print("❌ <think>标签未被保留")

        return True
    else:
        print("❌ JSON翻译失败")
        return False

if __name__ == "__main__":
    test_think_preservation()
