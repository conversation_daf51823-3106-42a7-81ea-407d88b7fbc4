#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parquet文件转JSON格式脚本
支持读取parquet文件并转换为JSON格式，可以指定行数进行测试
"""

import pandas as pd
import json
import os
import argparse
import logging
import shutil
from typing import Dict, Any, Optional, List
from pathlib import Path
from tqdm import tqdm

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ParquetToJsonConverter:
    """Parquet到JSON转换器"""

    def __init__(self, input_file: str, output_file: Optional[str] = None):
        self.input_file = input_file
        self.output_file = output_file or self._generate_output_filename()
        self.df = None
        self.chunks_dir = "json_chunks"

    def _generate_output_filename(self) -> str:
        """生成输出文件名"""
        input_path = Path(self.input_file)
        output_path = input_path.parent / f"{input_path.stem}.json"
        return str(output_path)

    def load_parquet(self, nrows: Optional[int] = None) -> bool:
        """
        加载parquet文件

        Args:
            nrows: 读取的行数，None表示读取全部

        Returns:
            是否加载成功
        """
        try:
            logger.info(f"正在加载文件: {self.input_file}")

            if nrows is not None:
                logger.info(f"限制读取行数: {nrows}")
                # 先读取全部数据然后取前n行（因为pyarrow不直接支持nrows参数）
                self.df = pd.read_parquet(self.input_file)
                if len(self.df) > nrows:
                    self.df = self.df.head(nrows)
                    logger.info(f"已截取前 {nrows} 行数据")
            else:
                self.df = pd.read_parquet(self.input_file)

            logger.info(f"文件加载成功!")
            logger.info(f"数据形状: {self.df.shape}")
            logger.info(f"列名: {list(self.df.columns)}")

            return True

        except Exception as e:
            logger.error(f"加载文件失败: {e}")
            return False

    def preview_data(self, num_rows: int = 5) -> None:
        """预览数据"""
        if self.df is None:
            logger.error("请先加载数据")
            return

        logger.info(f"\n数据预览 (前{num_rows}行):")
        logger.info("=" * 80)

        for i in range(min(num_rows, len(self.df))):
            logger.info(f"\n第 {i+1} 行:")
            for col in self.df.columns:
                value = self.df.iloc[i][col]
                # 截断长文本以便显示
                if isinstance(value, str) and len(value) > 100:
                    display_value = value[:100] + "..."
                else:
                    display_value = value
                logger.info(f"  {col}: {display_value}")

    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息"""
        if self.df is None:
            return {}

        info = {
            "total_rows": len(self.df),
            "total_columns": len(self.df.columns),
            "columns": list(self.df.columns),
            "dtypes": {col: str(dtype) for col, dtype in self.df.dtypes.items()},
            "memory_usage": f"{self.df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB",
            "null_counts": self.df.isnull().sum().to_dict()
        }

        return info

    def convert_to_json(self, format_type: str = "records", ensure_ascii: bool = False,
                       indent: Optional[int] = 2) -> bool:
        """
        转换为JSON格式

        Args:
            format_type: JSON格式类型
                - "records": [{"col1": val1, "col2": val2}, ...]
                - "index": {"0": {"col1": val1, "col2": val2}, ...}
                - "values": [[val1, val2], [val3, val4], ...]
                - "split": {"index": [...], "columns": [...], "data": [[...]]}
            ensure_ascii: 是否确保ASCII编码
            indent: JSON缩进，None表示紧凑格式

        Returns:
            是否转换成功
        """
        if self.df is None:
            logger.error("请先加载数据")
            return False

        try:
            logger.info(f"开始转换为JSON格式 (格式: {format_type})")

            # 处理NaN值
            df_clean = self.df.fillna("")  # 将NaN替换为空字符串

            # 转换为JSON
            if format_type == "records":
                json_data = df_clean.to_dict(orient="records")
            elif format_type == "index":
                json_data = df_clean.to_dict(orient="index")
            elif format_type == "values":
                json_data = {
                    "columns": list(df_clean.columns),
                    "data": df_clean.values.tolist()
                }
            elif format_type == "split":
                json_data = df_clean.to_dict(orient="split")
            else:
                logger.error(f"不支持的格式类型: {format_type}")
                return False

            # 保存JSON文件
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=ensure_ascii, indent=indent)

            # 获取文件大小
            file_size = os.path.getsize(self.output_file) / 1024 / 1024

            logger.info(f"转换完成!")
            logger.info(f"输出文件: {self.output_file}")
            logger.info(f"文件大小: {file_size:.2f} MB")

            return True

        except Exception as e:
            logger.error(f"转换失败: {e}")
            return False

    def convert_simplified_format(self, split_files: bool = False) -> bool:
        """
        转换为简化格式，只保留关键字段：行号、expected_answer、problem、generated_solution

        Args:
            split_files: 是否按row_number切分为多个文件

        Returns:
            是否转换成功
        """
        if self.df is None:
            logger.error("请先加载数据")
            return False

        try:
            logger.info("开始转换为简化格式")

            json_data = []
            for idx, row in self.df.iterrows():
                try:
                    simplified_item = {
                        "row_number": idx + 1,  # 行号从1开始
                        "expected_answer": row.get("expected_answer", ""),
                        "problem": row.get("problem", ""),
                        "generated_solution": row.get("generated_solution", "")
                    }
                    json_data.append(simplified_item)
                except Exception as e:
                    logger.warning(f"格式化第{idx+1}行失败: {e}")
                    continue

            # 保存JSON文件
            output_file = self.output_file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)

            file_size = os.path.getsize(output_file) / 1024 / 1024

            logger.info(f"简化格式转换完成!")
            logger.info(f"输出文件: {output_file}")
            logger.info(f"文件大小: {file_size:.2f} MB")
            logger.info(f"转换条目数: {len(json_data)}")

            # 如果需要切分文件
            if split_files:
                logger.info("开始按row_number切分JSON文件...")
                chunk_files = self.split_json_by_row_number(json_data)
                if chunk_files:
                    logger.info(f"切分完成，生成 {len(chunk_files)} 个文件")
                    logger.info(f"切分文件目录: {self.chunks_dir}")

                    # 询问是否保留原始合并文件
                    user_choice = input("是否保留原始合并的JSON文件？(y/n): ")
                    if user_choice.lower() != 'y':
                        os.remove(output_file)
                        logger.info("原始合并文件已删除")
                else:
                    logger.error("切分失败")
                    return False

            return True

        except Exception as e:
            logger.error(f"简化格式转换失败: {e}")
            return False

    def split_json_by_row_number(self, json_data: List[Dict]) -> List[str]:
        """
        根据row_number将JSON数据分割成多个文件

        Args:
            json_data: JSON数据列表

        Returns:
            生成的文件路径列表
        """
        try:
            # 确保块文件目录存在
            os.makedirs(self.chunks_dir, exist_ok=True)

            chunk_files = []
            total_items = len(json_data)

            logger.info(f"按row_number分块配置:")
            logger.info(f"- 总条目数: {total_items}")
            logger.info(f"- 分块方式: 每个条目一个文件")
            logger.info(f"- 将生成: {total_items} 个文件")

            with tqdm(total=total_items, desc="✂️ 按row_number分割", unit="文件", colour='orange') as pbar:
                for i, item in enumerate(json_data):
                    # 获取row_number作为文件标识
                    row_number = item.get('row_number', i + 1)

                    # 生成块文件名（每个文件包含一个条目）
                    chunk_filename = f"chunk_row_{row_number:06d}.json"
                    chunk_path = os.path.join(self.chunks_dir, chunk_filename)

                    # 保存单个条目到文件（注意：保存为单个对象，不是数组）
                    with open(chunk_path, 'w', encoding='utf-8') as f:
                        json.dump(item, f, ensure_ascii=False, indent=2)

                    chunk_files.append(chunk_path)

                    pbar.update(1)
                    pbar.set_postfix({
                        "row_number": row_number,
                        "已生成": f"{i+1}/{total_items}"
                    })

            logger.info(f"按row_number分块完成，生成 {len(chunk_files)} 个文件")
            return chunk_files

        except Exception as e:
            logger.error(f"分块失败: {e}")
            return []

    def cleanup_chunks(self):
        """清理临时块文件"""
        if os.path.exists(self.chunks_dir):
            shutil.rmtree(self.chunks_dir)
            logger.info("临时块文件已清理")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Parquet文件转JSON格式工具")
    parser.add_argument("input_file", help="输入的parquet文件路径")
    parser.add_argument("-o", "--output", help="输出的JSON文件路径")
    parser.add_argument("-n", "--nrows", type=int, default=0, help="读取的行数 (默认: 0表示全部)")
    parser.add_argument("-f", "--format", choices=["records", "index", "values", "split", "simplified"],
                       default="simplified", help="JSON格式类型 (默认: simplified - 只保留关键字段)")
    parser.add_argument("--no-indent", action="store_true", help="不使用缩进 (紧凑格式)")
    parser.add_argument("--ascii", action="store_true", help="使用ASCII编码")
    parser.add_argument("--preview", action="store_true", help="只预览数据，不转换")
    parser.add_argument("--split", action="store_true", help="按row_number切分JSON为多个文件")

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input_file):
        logger.error(f"输入文件不存在: {args.input_file}")
        return

    # 创建转换器
    converter = ParquetToJsonConverter(args.input_file, args.output)

    # 确定读取行数
    nrows = None if args.nrows == 0 else args.nrows

    # 加载数据
    if not converter.load_parquet(nrows):
        return

    # 显示数据信息
    info = converter.get_data_info()
    logger.info(f"\n数据信息:")
    logger.info(f"  总行数: {info['total_rows']}")
    logger.info(f"  总列数: {info['total_columns']}")
    logger.info(f"  内存使用: {info['memory_usage']}")
    logger.info(f"  列名: {info['columns']}")

    # 预览数据
    converter.preview_data(3)

    # 如果只是预览，则退出
    if args.preview:
        logger.info("预览完成，退出")
        return

    # 转换为JSON
    indent = None if args.no_indent else 2

    if args.format == "simplified":
        success = converter.convert_simplified_format(split_files=args.split)
    else:
        if args.split:
            logger.warning("--split 参数只在 simplified 格式下有效，将被忽略")
        success = converter.convert_to_json(
            format_type=args.format,
            ensure_ascii=args.ascii,
            indent=indent
        )

    if success:
        logger.info("✅ 转换成功完成!")

        # 如果使用了切分功能，询问是否清理临时文件
        if args.format == "simplified" and args.split:
            user_choice = input("是否清理临时块文件？(y/n): ")
            if user_choice.lower() == 'y':
                converter.cleanup_chunks()
    else:
        logger.error("❌ 转换失败")

if __name__ == "__main__":
    main()
