{"row_number": 2723, "expected_answer": "\\( r^2 \\cos 2\\theta = a^2 \\)", "problem": "Find the polar equation of the curve $H$ with Cartesian equation $x^{2} - y^{2} = a^{2}$.", "generated_solution": "<think>\nOkay, let's see. I need to find the polar equation for the hyperbola given by the Cartesian equation x² - y² = a². Hmm, right, converting Cartesian to polar. I remember that in polar coordinates, x = r cos θ and y = r sin θ. So maybe I can substitute those into the equation.\n\nSo starting with x² - y² = a². Replace x with r cos θ and y with r sin θ. That should give me (r cos θ)² - (r sin θ)² = a². Let me compute that. \n\nExpanding the squares: r² cos² θ - r² sin² θ = a². Factor out the r²: r² (cos² θ - sin² θ) = a². Oh, wait, I remember that cos² θ - sin² θ is equal to cos 2θ. So that simplifies the equation to r² cos 2θ = a². \n\nSo then, solving for r², I get r² = a² / cos 2θ. But to write it in polar form, usually we have r as a function of θ. So taking the square root of both sides, r = ± a / sqrt(cos 2θ). Hmm, but in polar equations, sometimes they prefer just the positive r, or maybe express it using the absolute value? Wait, but in polar coordinates, r can be negative, so maybe the ± is acceptable. However, typically, polar equations are written as r = ... without the ±, because if you allow θ to vary, negative r can be represented by adding π to the angle. So perhaps the equation is r² = a² / cos 2θ, or maybe just r = a / sqrt(cos 2θ). But let me check.\n\nWait, the original Cartesian equation is a hyperbola. Hyperbola in polar coordinates usually has a form involving 1/(1 ± e cos θ) or something like that, but maybe this is a different case. Wait, maybe I need to double-check my steps.\n\nOriginal equation: x² - y² = a². Substitute x = r cos θ, y = r sin θ. Then:\n\n(r cos θ)^2 - (r sin θ)^2 = a²\n\nr² (cos² θ - sin² θ) = a²\n\nWhich is r² cos 2θ = a². So r² = a² / cos 2θ. Then, solving for r, we get r = ± a / sqrt(cos 2θ). But in polar coordinates, r can be negative, so allowing for both positive and negative r. But sometimes, the equation is written as r^2 = a^2 sec 2θ, since sec is 1/cos. So maybe the answer is r² = a² sec 2θ. But sometimes people write it as r = a sqrt(sec 2θ), but that's equivalent. Wait, but sqrt(sec 2θ) is 1/sqrt(cos 2θ). So maybe both forms are acceptable. \n\nBut let me check the standard form for hyperbola in polar coordinates. The standard hyperbola in polar coordinates is usually given in terms of a focus at the origin, but in this case, the hyperbola is centered at the origin, symmetric about both axes. The Cartesian equation is a rectangular hyperbola, right? Because x² - y² = a² is a hyperbola with asymptotes y = x and y = -x. So its branches are along the x and y axes? Wait, no. Wait, hyperbola x² - y² = a² opens along the x-axis. The asymptotes are y = ±x. So the transverse axis is the x-axis.\n\nBut when converting to polar coordinates, maybe we need to express it in terms of r and θ. Let's see, so if I proceed with the equation r² = a² / cos 2θ, that might be acceptable. Alternatively, if we take the square root, r = a / sqrt(cos 2θ). But for θ where cos 2θ is negative, the denominator becomes imaginary, which isn't possible. Wait, so that would mean that the hyperbola exists only where cos 2θ is positive. Because if cos 2θ is positive, then 2θ is in the first or fourth quadrant, so θ is between -π/4 and π/4, or between 3π/4 and 5π/4, etc. That makes sense for a hyperbola opening along the x-axis. Because in polar coordinates, when θ is 0, r is a / sqrt(1), so r = a, which is the vertex of the hyperbola at (a, 0). Similarly, θ = π, then cos 2π = 1, so r = a, but since θ = π, the point is (-a, 0), which is the other vertex. So that seems to check out.\n\nWait, but for the hyperbola x² - y² = a², the vertices are at (±a, 0). So in polar coordinates, when θ = 0, r = a / sqrt(cos 0) = a / 1 = a, so the point (a, 0). When θ = π, cos 2π = 1, so r = a / 1 = a, but angle π gives (-a, 0). That's correct. For angles in between, like θ = π/4, then cos(2*(π/4)) = cos(π/2) = 0, so r approaches infinity. Which makes sense because the asymptotes are at θ = π/4 and θ = 3π/4, etc. So as θ approaches those angles, the denominator approaches zero, so r approaches infinity. That aligns with the asymptotes.\n\nTherefore, the polar equation should be r² = a² sec 2θ, or r = ± a / sqrt(cos 2θ). But I need to confirm if the problem expects the equation in a particular form. The problem says \"Find the polar equation of the curve H...\" So maybe either form is acceptable, but often polar equations are written as r in terms of θ without the ±, since negative r can be achieved by adding π to θ. However, in this case, the hyperbola has two separate branches. The right branch corresponds to positive r with θ near 0 or π, and the left branch corresponds to negative r with θ near π/2 or 3π/2? Wait, no. Let me think.\n\nIf we use r = a / sqrt(cos 2θ), then for θ between -π/4 and π/4, cos 2θ is positive, so r is positive, which gives the right branch. Then, when θ is between π/4 and 3π/4, cos 2θ is negative, so sqrt(cos 2θ) would be imaginary, which isn't allowed. Then, from θ = 3π/4 to 5π/4, cos 2θ is positive again (since 2θ would be from 3π/2 to 5π/2, which is equivalent to angles where cosine is positive), so r would be real again. But θ in 3π/4 to 5π/4 would be pointing to the left and down/up, but with r = a / sqrt(cos 2θ), let's see at θ = π, 2θ = 2π, cos 2θ = 1, so r = a, which is (-a, 0) in Cartesian. So that's the left vertex. So actually, the left branch is covered by angles between π/2 and 3π/2, but cos 2θ must be positive. Wait, at θ = π/2, 2θ = π, cos π = -1, so denominator is sqrt(-1), which is imaginary. So maybe it's split into regions.\n\nWait, perhaps the domain of θ where cos 2θ is positive is when 2θ is in (-π/2, π/2) modulo 2π. So θ is in (-π/4, π/4) modulo π. So θ in (-π/4, π/4), (3π/4, 5π/4), etc. So those intervals correspond to the right and left branches. So the equation r = a / sqrt(cos 2θ) would cover both branches as θ varies in those intervals. However, if we allow negative r, then for θ in (π/4, 3π/4), where cos 2θ is negative, then r would be imaginary if we take the positive square root, but if we allow negative r, maybe we can represent those regions?\n\nWait, no. If we have r² = a² / cos 2θ, then for cos 2θ negative, r² must be negative, which is impossible. So actually, the polar equation r² = a² / cos 2θ implies that cos 2θ must be positive, hence the domain restrictions. So the hyperbola in polar coordinates only exists where cos 2θ is positive, which are the angular regions we discussed earlier, giving the two branches. Therefore, the polar equation is r² = a² sec 2θ. So to write it in a standard form, that's acceptable. Alternatively, solving for r, you get r = ±a / sqrt(cos 2θ), but as r can be positive or negative, but with θ restricted to intervals where cos 2θ is positive.\n\nAlternatively, to write it in terms of r^2, which is more straightforward. Let me check some references. For example, standard conversions: the hyperbola x² - y² = a² in polar coordinates. Let me recall that maybe there's a standard form. From references, I remember that for rectangular hyperbola, polar equations can be written as r² = a² / cos 2θ or similar. So perhaps that is the standard answer. So the answer is r² = a² sec 2θ. So converting secant to 1/cos, so r² = a² / cos 2θ. Therefore, writing that as the polar equation. So boxed, it would be r^2 = \\boxed{\\frac{a^2}{\\cos 2\\theta}}.\n\nBut let me confirm once more. Let's take a point on the hyperbola. For example, when y = 0, x = ±a. In polar coordinates, that's (r=a, θ=0) and (r=a, θ=π). Plugging into the equation r² cos 2θ = a². At θ=0: a² * cos 0 = a² *1 = a², which equals a². Correct. At θ=π: r=a, cos 2π=1, so a² *1 =a². Correct. Another point: when x=0, the hyperbola doesn't have points, but the asymptotes at y=±x. Let's pick a point not on the axes. For example, take θ=π/6. Then cos 2θ = cos π/3 = 0.5. So r² = a² / 0.5 = 2a², so r = a√2. Converting back to Cartesian: x = r cos θ = a√2 cos(π/6) = a√2*(√3/2) = a*(√6)/2. y = a√2 sin(π/6) = a√2*(1/2) = a*(√2)/2. Then x² - y² = (a²*(6/4)) - (a²*(2/4)) = (6a² - 2a²)/4 = 4a²/4 = a². Which matches the original equation. So that works. \n\nTherefore, the polar equation is r² = a² / cos 2θ. So the answer should be r² = a² sec 2θ or r² = a² / cos 2θ. Both are correct, but since the problem says \"polar equation\", either is acceptable. But since in the conversion we derived r² = a² / cos 2θ, I think that's the answer they are looking for. So boxed, it would be r^2 = \\boxed{\\frac{a^2}{\\cos 2\\theta}}.\n</think>To find the polar equation of the curve \\( H \\) given by the Cartesian equation \\( x^2 - y^2 = a^2 \\), we start by expressing \\( x \\) and \\( y \\) in terms of polar coordinates. Recall that in polar coordinates, \\( x = r \\cos \\theta \\) and \\( y = r \\sin \\theta \\).\n\nSubstitute these into the Cartesian equation:\n\n\\[\n(r \\cos \\theta)^2 - (r \\sin \\theta)^2 = a^2\n\\]\n\nSimplify the left-hand side:\n\n\\[\nr^2 \\cos^2 \\theta - r^2 \\sin^2 \\theta = a^2\n\\]\n\nFactor out \\( r^2 \\):\n\n\\[\nr^2 (\\cos^2 \\theta - \\sin^2 \\theta) = a^2\n\\]\n\nUsing the trigonometric identity \\( \\cos^2 \\theta - \\sin^2 \\theta = \\cos 2\\theta \\), we get:\n\n\\[\nr^2 \\cos 2\\theta = a^2\n\\]\n\nSolve for \\( r^2 \\):\n\n\\[\nr^2 = \\frac{a^2}{\\cos 2\\theta}\n\\]\n\nThus, the polar equation of the hyperbola is:\n\n\\[\n\\boxed{r^2 = \\frac{a^2}{\\cos 2\\theta}}\n\\]"}