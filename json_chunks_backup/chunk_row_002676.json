{"row_number": 2676, "expected_answer": "89", "problem": "Find the last two digits of \\(87^{494}\\).", "generated_solution": "<think>\nOkay, so I need to find the last two digits of 87 raised to the 494th power. Hmm, that's 87^494. I remember that to find the last two digits of a number, we need to compute the number modulo 100. So, essentially, I need to calculate 87^494 mod 100. \n\nFirst, maybe I can simplify 87 mod 100. Well, 87 is already less than 100, so 87 mod 100 is just 87. But maybe there's a way to break this down using <PERSON><PERSON><PERSON>'s theorem or something related to <PERSON><PERSON><PERSON>'s totient function? Let me recall. <PERSON><PERSON><PERSON>'s theorem states that if two numbers are coprime, then a^φ(n) ≡ 1 mod n, where φ(n) is <PERSON><PERSON><PERSON>'s totient function. \n\nSo, first, I need to check if 87 and 100 are coprime. The prime factors of 100 are 2 and 5. The prime factors of 87 are 3 and 29. Since they don't share any common factors other than 1, they are coprime. That means <PERSON><PERSON><PERSON>'s theorem applies here. Therefore, φ(100) is needed. \n\nCalculating φ(100): since 100 = 2^2 * 5^2, the totient function is φ(100) = 100*(1 - 1/2)*(1 - 1/5) = 100*(1/2)*(4/5) = 100*(2/5) = 40. Wait, let me verify that again. The formula for φ(n) when n is p^k * q^m is n*(1 - 1/p)*(1 - 1/q). So, yes, 100 = 2^2 * 5^2. So φ(100) = 100*(1 - 1/2)*(1 - 1/5) = 100*(1/2)*(4/5) = 100*(2/5) = 40. That's correct. So φ(100) is 40.\n\nTherefore, according to Euler's theorem, 87^40 ≡ 1 mod 100. So 87^40 ≡ 1 mod 100. Then, since 87 and 100 are coprime, any exponent can be broken down in terms of multiples of 40. So 87^494 can be written as 87^(40*12 + 14), because 40*12 is 480, and 494 - 480 is 14. So that's 87^494 = (87^40)^12 * 87^14. Then, modulo 100, this becomes (1)^12 * 87^14 mod 100 = 87^14 mod 100.\n\nSo now the problem reduces to computing 87^14 mod 100. Hmm. Maybe I can compute this using exponentiation by squaring? Let's see. Let's break down 14 into binary? 14 in binary is 8 + 4 + 2, which is 1110. So we can compute 87^2, then square that to get 87^4, then square again for 87^8, and then multiply by 87^4 and 87^2. Wait, maybe step by step:\n\nFirst, compute 87^2 mod 100. 87^2 is 7569. 7569 mod 100 is 69. So 87^2 ≡ 69 mod 100.\n\nNext, compute 87^4 = (87^2)^2. So 69^2 = 4761. 4761 mod 100 is 61. So 87^4 ≡ 61 mod 100.\n\nThen, 87^8 = (87^4)^2. 61^2 = 3721. 3721 mod 100 is 21. So 87^8 ≡ 21 mod 100.\n\nNow, 87^14 is 87^(8 + 4 + 2) = 87^8 * 87^4 * 87^2. So multiplying the previous results:\n\nFirst, 21 * 61. Let's compute that. 21*60 = 1260, 21*1 = 21, so total 1281. 1281 mod 100 is 81. Then, multiply that by 69 (which is 87^2). 81*69. Let's compute that. 80*69 = 5520, 1*69 = 69, so total 5520 + 69 = 5589. 5589 mod 100 is 89. So 87^14 mod 100 is 89. Therefore, 87^494 mod 100 is also 89. \n\nWait, let me verify this again step by step to ensure no calculation error. Because these modulus operations can get tricky.\n\nStarting over:\n\nCompute 87^1 mod 100 = 87.\n\n87^2 = 87*87 = 7569 → 69 mod 100.\n\n87^4 = (87^2)^2 = 69^2 = 4761 → 61 mod 100.\n\n87^8 = (87^4)^2 = 61^2 = 3721 → 21 mod 100.\n\nSo 87^14 = 87^(8+4+2) = 87^8 * 87^4 * 87^2.\n\nCalculating each term:\n\n87^8 mod 100 = 21\n\n87^4 mod 100 = 61\n\n87^2 mod 100 = 69\n\nMultiply them together:\n\nFirst, 21 * 61 = (20 +1)(60 +1) = 20*60 +20*1 +1*60 +1*1 = 1200 + 20 + 60 +1 = 1281 → 81 mod 100.\n\nThen, 81 * 69 = (80 +1)(70 -1) = 80*70 -80*1 +1*70 -1*1 = 5600 -80 +70 -1 = 5600 -11 = 5589 → 89 mod 100.\n\nSo yes, 87^14 mod 100 is 89. Then, since 87^494 ≡ 87^14 mod 100, the last two digits are 89. Hmm. Wait, let me check if there's another way to compute this.\n\nAlternatively, using Chinese Remainder Theorem. Since 100 = 4 * 25, and 4 and 25 are coprime. Then, we can compute 87^494 mod 4 and mod 25, then combine the results.\n\nLet's try this method to cross-verify.\n\nFirst, compute 87^494 mod 4. 87 mod 4 is 3 (since 4*21=84, 87-84=3). So 3^494 mod 4. Since 3 mod 4 is -1, so (-1)^494 = 1. Therefore, 87^494 ≡ 1 mod 4.\n\nNext, compute 87^494 mod 25. Let's compute this. Since 87 mod 25 is 87 - 25*3 = 87 -75 = 12. So 87 ≡ 12 mod 25. Therefore, 12^494 mod 25. Now, φ(25) is 20, since φ(5^2) = 5^2 -5^1 = 25 -5 = 20. So by Euler's theorem, 12^20 ≡ 1 mod 25. Therefore, 12^494 = 12^(20*24 + 14) = (12^20)^24 * 12^14 ≡ 1^24 * 12^14 ≡ 12^14 mod 25.\n\nSo need to compute 12^14 mod 25.\n\nCompute 12^2 = 144 mod 25. 144 - 5*25=144-125=19. So 12^2 ≡19 mod25.\n\n12^4 = (12^2)^2 =19^2=361. 361 mod25: 25*14=350, 361-350=11. So 12^4 ≡11 mod25.\n\n12^8 = (12^4)^2 =11^2=121 mod25=21. 25*4=100, 121-100=21. So 12^8 ≡21 mod25.\n\n12^14 = 12^(8 + 4 + 2) = 12^8 *12^4 *12^2. So compute 21 *11 *19 mod25.\n\nFirst, 21*11 = 231. 231 mod25: 25*9=225, 231-225=6. So 21*11 ≡6 mod25.\n\nThen, 6 *19 =114. 114 mod25: 25*4=100, 114-100=14. So 12^14 ≡14 mod25.\n\nTherefore, 87^494 ≡12^494≡14 mod25.\n\nSo we have:\n\n87^494 ≡1 mod4\n\n87^494≡14 mod25\n\nWe need to find a number x such that x ≡1 mod4 and x≡14 mod25.\n\nLet’s solve these congruences. Let x =25k +14. Substitute into x ≡1 mod4:\n\n25k +14 ≡1 mod4.\n\n25 mod4 is 1, so 1*k + 14 mod4 ≡k + 2 mod4 ≡1 mod4. Therefore, k +2 ≡1 mod4 →k ≡-1 mod4→k≡3 mod4.\n\nThus, k=4m +3 for some integer m.\n\nSo x=25*(4m +3)+14=100m +75 +14=100m +89.\n\nTherefore, the smallest positive solution is 89 mod100. So the last two digits are 89. That matches the earlier result. So that's good, cross-verified. So both methods give 89. So the answer should be 89. Let me just check again with another approach.\n\nAlternatively, maybe using the Carmichael function. For modulus 100, the Carmichael λ(100) is lcm(λ(4), λ(25)). λ(4)=2, λ(25)=20. So λ(100)=lcm(2,20)=20. Therefore, since 87 and 100 are coprime, 87^20 ≡1 mod100. Therefore, 87^494 =87^(20*24 +14)= (87^20)^24 *87^14 ≡1^24 *87^14 ≡87^14 mod100. Which is the same as before, leading to 89.\n\nTherefore, the answer is 89. So the last two digits of 87^494 are 89. Seems solid with multiple methods confirming.\n</think>To find the last two digits of \\(87^{494}\\), we need to compute \\(87^{494} \\mod 100\\). We will use Euler's theorem and the Chinese Remainder Theorem (CRT) to simplify the calculation.\n\n### Step 1: Euler's Theorem\nEuler's theorem states that if \\(a\\) and \\(n\\) are coprime, then \\(a^{\\phi(n)} \\equiv 1 \\mod n\\), where \\(\\phi(n)\\) is Euler's totient function.\n\nFirst, we calculate \\(\\phi(100)\\):\n\\[\n\\phi(100) = \\phi(2^2 \\times 5^2) = 100 \\left(1 - \\frac{1}{2}\\right) \\left(1 - \\frac{1}{5}\\right) = 100 \\times \\frac{1}{2} \\times \\frac{4}{5} = 40\n\\]\n\nSince 87 and 100 are coprime, Euler's theorem applies:\n\\[\n87^{40} \\equiv 1 \\mod 100\n\\]\n\n### Step 2: Simplify the Exponent\nWe need to express \\(87^{494} \\mod 100\\) in terms of the exponent 40:\n\\[\n494 = 40 \\times 12 + 14\n\\]\nThus,\n\\[\n87^{494} = 87^{40 \\times 12 + 14} = (87^{40})^{12} \\times 87^{14} \\equiv 1^{12} \\times 87^{14} \\equiv 87^{14} \\mod 100\n\\]\n\n### Step 3: Compute \\(87^{14} \\mod 100\\) Using Exponentiation by Squaring\nWe will compute \\(87^{14} \\mod 100\\) using repeated squaring:\n\n1. Compute \\(87^2 \\mod 100\\):\n\\[\n87^2 = 7569 \\equiv 69 \\mod 100\n\\]\n\n2. Compute \\(87^4 \\mod 100\\):\n\\[\n87^4 = (87^2)^2 = 69^2 = 4761 \\equiv 61 \\mod 100\n\\]\n\n3. Compute \\(87^8 \\mod 100\\):\n\\[\n87^8 = (87^4)^2 = 61^2 = 3721 \\equiv 21 \\mod 100\n\\]\n\n4. Combine the results for \\(87^{14}\\):\n\\[\n87^{14} = 87^{8+4+2} = 87^8 \\times 87^4 \\times 87^2\n\\]\n\\[\n87^{14} \\equiv 21 \\times 61 \\times 69 \\mod 100\n\\]\n\nFirst, compute \\(21 \\times 61 \\mod 100\\):\n\\[\n21 \\times 61 = 1281 \\equiv 81 \\mod 100\n\\]\n\nNext, compute \\(81 \\times 69 \\mod 100\\):\n\\[\n81 \\times 69 = 5589 \\equiv 89 \\mod 100\n\\]\n\nThus,\n\\[\n87^{14} \\equiv 89 \\mod 100\n\\]\n\n### Step 4: Verification Using Chinese Remainder Theorem (CRT)\nTo verify, we compute \\(87^{494} \\mod 4\\) and \\(\\mod 25\\), and then combine the results.\n\n1. Compute \\(87^{494} \\mod 4\\):\n\\[\n87 \\equiv 3 \\mod 4\n\\]\n\\[\n3^{494} \\equiv (-1)^{494} \\equiv 1 \\mod 4\n\\]\n\n2. Compute \\(87^{494} \\mod 25\\):\n\\[\n87 \\equiv 12 \\mod 25\n\\]\n\\[\n12^{494} \\mod 25\n\\]\nUsing Euler's theorem, \\(\\phi(25) = 20\\), so:\n\\[\n12^{20} \\equiv 1 \\mod 25\n\\]\n\\[\n494 = 20 \\times 24 + 14\n\\]\n\\[\n12^{494} = 12^{20 \\times 24 + 14} = (12^{20})^{24} \\times 12^{14} \\equiv 1^{24} \\times 12^{14} \\equiv 12^{14} \\mod 25\n\\]\n\nCompute \\(12^{14} \\mod 25\\) using repeated squaring:\n\\[\n12^2 = 144 \\equiv 19 \\mod 25\n\\]\n\\[\n12^4 = 19^2 = 361 \\equiv 11 \\mod 25\n\\]\n\\[\n12^8 = 11^2 = 121 \\equiv 21 \\mod 25\n\\]\n\\[\n12^{14} = 12^{8+4+2} = 12^8 \\times 12^4 \\times 12^2\n\\]\n\\[\n12^{14} \\equiv 21 \\times 11 \\times 19 \\mod 25\n\\]\n\\[\n21 \\times 11 = 231 \\equiv 6 \\mod 25\n\\]\n\\[\n6 \\times 19 = 114 \\equiv 14 \\mod 25\n\\]\n\nThus,\n\\[\n12^{494} \\equiv 14 \\mod 25\n\\]\n\n### Step 5: Combine Results Using CRT\nWe need \\(x\\) such that:\n\\[\nx \\equiv 1 \\mod 4\n\\]\n\\[\nx \\equiv 14 \\mod 25\n\\]\n\nLet \\(x = 25k + 14\\). Then:\n\\[\n25k + 14 \\equiv 1 \\mod 4\n\\]\n\\[\n25 \\equiv 1 \\mod 4 \\implies 1k + 14 \\equiv 1 \\mod 4\n\\]\n\\[\nk + 2 \\equiv 1 \\mod 4 \\implies k \\equiv -1 \\equiv 3 \\mod 4\n\\]\n\nThus, \\(k = 4m + 3\\) for some integer \\(m\\). Therefore:\n\\[\nx = 25(4m + 3) + 14 = 100m + 75 + 14 = 100m + 89\n\\]\n\nThe smallest positive solution is:\n\\[\nx \\equiv 89 \\mod 100\n\\]\n\nThus, the last two digits of \\(87^{494}\\) are:\n\\[\n\\boxed{89}\n\\]"}