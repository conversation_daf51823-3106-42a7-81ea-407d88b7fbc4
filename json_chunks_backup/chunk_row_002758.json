{"row_number": 2758, "expected_answer": "2", "problem": "Find the degree of the minimal polynomial of a square matrix of rank $1$.", "generated_solution": "<think>\nOkay, so I need to find the degree of the minimal polynomial of a square matrix of rank 1. Hmm, let's start by recalling some concepts. The minimal polynomial of a matrix is the smallest degree monic polynomial (leading coefficient 1) such that when you substitute the matrix into the polynomial, you get the zero matrix. The degree of this polynomial is what we're after.\n\nSince the matrix has rank 1, that tells me something about its structure. A rank 1 matrix can be written as the outer product of two vectors, right? So if it's an n×n matrix, it's like multiplying a column vector by a row vector. Let's denote this matrix as A = uv^T, where u and v are vectors in n-dimensional space. \n\nNow, let's think about the properties of such a matrix. The rank is 1, so the image of the matrix is a one-dimensional subspace. That might mean that there's not a lot of complexity in terms of linear transformations here. Maybe the minimal polynomial is of a low degree. \n\nWhat's the characteristic polynomial of a rank 1 matrix? The characteristic polynomial is given by det(A - λI). For a rank 1 matrix, the trace is the sum of the diagonal elements, and since rank is 1, the trace is equal to the only non-zero eigenvalue (if the matrix is not nilpotent). Wait, but if the matrix is idempotent or nilpotent, the minimal polynomial would be different. Hmm, maybe I need to consider different cases.\n\nFirst, let's consider if the matrix is idempotent. If A² = A, then the minimal polynomial would be x² - x = x(x - 1), which is degree 2. But not all rank 1 matrices are idempotent. For example, if A = uv^T and v^Tu = 1, then A² = uv^Tuv^T = u(v^Tu)v^T = u(1)v^T = A. So in that case, A is idempotent. But if v^Tu is not 1, then A² = (v^Tu)A, which would make A² = cA where c = v^Tu. Then, the minimal polynomial would be x² - c x = x(x - c), which is still degree 2 unless c is 0.\n\nWait, but if c is 0, then A² = 0, so the minimal polynomial is x², which is degree 2 as well. Wait, hold on. If c = 0, then A is a rank 1 nilpotent matrix. So in that case, A² = 0. Then the minimal polynomial is x². So regardless of whether the trace is zero or not, the minimal polynomial seems to be degree 2. Let me verify that.\n\nSuppose A is a rank 1 matrix where trace(A) ≠ 0. Then, A is diagonalizable? Wait, no. A rank 1 matrix that is diagonalizable would have one eigenvalue (say λ) with multiplicity 1 and 0 with multiplicity n-1. So in that case, the minimal polynomial would be x(x - λ). But if λ is non-zero, then the minimal polynomial is degree 2. However, if the matrix is not diagonalizable, which might be the case if it's a Jordan block, but rank 1 matrices can't be Jordan blocks unless it's a single Jordan block of size 1, which is just a scalar. Wait, no. A Jordan block of size 2 has rank 2 if it's a Jordan block for a non-zero eigenvalue. Hmm, perhaps I'm getting confused here.\n\nWait, actually, for a Jordan block of size n with eigenvalue λ, the rank is n - the number of Jordan blocks for the eigenvalue 0. Wait, maybe that's not helpful here. Let's go back. If A is a rank 1 matrix, then its Jordan canonical form must also have rank 1. So the Jordan form can't have more than one Jordan block, because that would require at least rank 1 per block. So if A is rank 1, its Jordan form is a single Jordan block with eigenvalue λ, but then the rank of a Jordan block of size n with eigenvalue λ is n if λ ≠ 0, but that's not possible here because we need rank 1. So actually, the Jordan form must be a single Jordan block of size 1 (i.e., a 1x1 matrix with entry λ) and the rest of the diagonal entries are 0. Wait, but that would mean the matrix is diagonal, with one eigenvalue λ and the rest zeros. So in that case, the minimal polynomial would be x(x - λ), which is degree 2. But if λ is zero, then the minimal polynomial is x.\n\nWait, but if A is the zero matrix, then it's rank 0. But in our case, the rank is 1, so λ can't be zero. Wait, no. Wait, a diagonal matrix with one λ ≠ 0 and the rest zeros is rank 1. So its minimal polynomial is x(x - λ). So degree 2. If instead, A is a Jordan block of size 2 with eigenvalue 0, but then its rank would be 1 (since it has ones on the superdiagonal), but actually, a Jordan block of size 2 with eigenvalue 0 is [[0,1],[0,0]], which has rank 1. So in this case, the minimal polynomial is x². So here, the minimal polynomial is degree 2. So regardless of whether the matrix is diagonalizable or not, as long as it's rank 1, the minimal polynomial is of degree at most 2. So maybe the answer is 2?\n\nWait, let me test with some examples. Let's take a diagonal matrix with one 1 and the rest zeros. Then, the minimal polynomial is x(x - 1), which is degree 2. If I take the matrix [[0,1],[0,0]], then the minimal polynomial is x². Both are degree 2. If I take a matrix like [[a, b],[0, 0]] where a ≠ 0, then the minimal polynomial would be x(x - a), which is also degree 2. So in all these cases, the minimal polynomial is degree 2. Therefore, the minimal polynomial of a rank 1 matrix is degree at most 2. But is there a case when the minimal polynomial is degree 1?\n\nIf the minimal polynomial is degree 1, then it would have to be x, meaning the matrix is the zero matrix. But the zero matrix has rank 0, which is not our case. So since the matrix is rank 1, it's non-zero, so the minimal polynomial can't be degree 1. Therefore, the minimal polynomial must be degree 2. So the answer should be 2. Therefore, the degree is 2. Let me just double-check.\n\nSuppose A is a rank 1 matrix. Then, A can be written as uv^T where u and v are non-zero vectors. Let's compute A²: A² = uv^Tuv^T = u(v^Tu)v^T = (v^Tu)uv^T = (v^Tu)A. So A² = cA where c = v^Tu. Therefore, the minimal polynomial must divide x² - c x. So either x² - c x is the minimal polynomial or a factor of it. If c ≠ 0, then the minimal polynomial is x(x - c), which has degree 2. If c = 0, then A² = 0, so the minimal polynomial is x², which is degree 2. Therefore, regardless of c, the minimal polynomial is degree 2. Therefore, the minimal polynomial of a rank 1 matrix has degree 2.\n\nYes, that makes sense. So the answer is 2.\n</think>To find the degree of the minimal polynomial of a square matrix \\( A \\) of rank 1, we can follow these steps:\n\n1. **Matrix Representation**: Since \\( A \\) has rank 1, it can be expressed as the outer product of two non-zero vectors \\( u \\) and \\( v \\):\n   \\[\n   A = uv^T\n   \\]\n\n2. **Square of the Matrix**: Compute \\( A^2 \\):\n   \\[\n   A^2 = (uv^T)(uv^T) = u(v^Tu)v^T = (v^Tu)uv^T = (v^Tu)A\n   \\]\n   Let \\( c = v^Tu \\). Then,\n   \\[\n   A^2 = cA\n   \\]\n\n3. **Minimal Polynomial**: The minimal polynomial \\( m(x) \\) of \\( A \\) is the smallest degree monic polynomial such that \\( m(A) = 0 \\). From \\( A^2 = cA \\), we have:\n   \\[\n   A^2 - cA = 0\n   \\]\n   This implies that the minimal polynomial \\( m(x) \\) must divide \\( x^2 - cx \\).\n\n4. **Possible Minimal Polynomials**:\n   - If \\( c \\neq 0 \\), the minimal polynomial is \\( x(x - c) \\), which is of degree 2.\n   - If \\( c = 0 \\), the minimal polynomial is \\( x^2 \\), which is also of degree 2.\n\n5. **Non-zero Matrix**: Since \\( A \\) is a rank 1 matrix, it is non-zero. Therefore, the minimal polynomial cannot be of degree 1 (which would imply \\( A = 0 \\)).\n\nThus, the minimal polynomial of a rank 1 matrix \\( A \\) is always of degree 2.\n\n\\[\n\\boxed{2}\n\\]"}