{"row_number": 2714, "expected_answer": "\\(\\frac{(t-10)^{12}}{12} + \\frac{10(t-10)^{11}}{11} + C\\)", "problem": "Evaluate the indefinite integral \\(\\int t(t-10)^{10} \\, dt\\).", "generated_solution": "<think>\nOkay, so I need to evaluate the integral of t times (t - 10) to the 10th power with respect to t. Hmm, let's see. The integral is ∫t(t - 10)^10 dt. Alright, how should I approach this? Maybe substitution? Integration by parts? Let me think.\n\nFirst, substitution comes to mind because there's a composite function here, (t - 10)^10. Let me try letting u be the inside function. Let u = t - 10. Then, du/dt = 1, so du = dt. That simplifies things because dt is just du. But then, what about the t in front? If u = t - 10, then t = u + 10. Oh, right! So I can express t in terms of u. That might work.\n\nSo substituting, the integral becomes ∫(u + 10) * u^10 du. Because t is u + 10, and (t - 10)^10 is u^10, and dt is du. So expanding that, the integral is ∫(u + 10)u^10 du. Let me check: (u + 10) multiplied by u^10 would be u^11 + 10u^10. So now the integral simplifies to ∫u^11 du + 10∫u^10 du. That seems manageable.\n\nIntegrating term by term: The integral of u^11 is (u^12)/12, and the integral of 10u^10 is 10*(u^11)/11. So putting those together, we get (u^12)/12 + (10u^11)/11 + C, where C is the constant of integration. Then, substitute back to replace u with t - 10. So substituting back, we have [(t - 10)^12]/12 + [10(t - 10)^11]/11 + C. \n\nWait, but I need to check if that's correct. Maybe I should verify by differentiating. Let's take the derivative of [(t - 10)^12]/12. The derivative of that is (12(t - 10)^11)/12 = (t - 10)^11. Then the derivative of [10(t - 10)^11]/11 is 10*(11(t - 10)^10)/11 = 10(t - 10)^10. So adding those two derivatives gives (t - 10)^11 + 10(t - 10)^10. \n\nBut the original integrand was t(t - 10)^10. Let me see: expanding (t - 10)^10 multiplied by t. Let's factor out (t - 10)^10. So we have t(t - 10)^10 = [(t - 10) + 10](t - 10)^10. Which is (t - 10)^11 + 10(t - 10)^10. That's exactly what we got from the derivative! So the differentiation checks out. Therefore, the integral is correct.\n\nAlternatively, maybe I could have used integration by parts. Let me try that method as well to confirm. Let u = t, dv = (t - 10)^10 dt. Then du = dt, and v would be the integral of (t - 10)^10 dt, which is [(t - 10)^11]/11. So integration by parts formula is uv - ∫v du. So that would be t*( (t - 10)^11 / 11 ) - ∫( (t - 10)^11 / 11 ) dt. Then compute the integral: The first term is t(t - 10)^11 /11. The integral part is (1/11)∫(t - 10)^11 dt, which is (1/11)*( (t - 10)^12 /12 ) + C. So putting it all together: (t(t - 10)^11)/11 - ( (t - 10)^12 )/(11*12) + C. Simplify: Let's factor out (t - 10)^11 / 11 from both terms. That gives [ (t - 10)^11 /11 ] * [ t - (t -10)/12 ].\n\nWait, let me compute the expression step by step. First term is (t*(t -10)^11)/11. Second term is - ( (t -10)^12 )/(132 ). Let's factor out (t -10)^11 from both terms. So, (t -10)^11 [ t/11 - (t -10)/132 ]. Let's get common denominators. The first term has denominator 11, which is 132/12. So t/11 is 12t/132. The second term is -(t -10)/132. So combining them: [12t - (t -10)] / 132. Simplify numerator: 12t - t +10 = 11t +10. Wait, that doesn't seem right. Wait, 12t - t is 11t, and then minus (-10) is +10? Wait, no. The second term is -(t -10) which is -t +10. So 12t - t +10 is 11t +10. Hmm. Wait, that would make the whole expression (11t +10)/132. Therefore, the expression becomes (t -10)^11*(11t +10)/132 + C. Let's see, 132 is 11*12. So simplifying, (t -10)^11*(11t +10)/(11*12) = ( (11t +10)(t -10)^11 ) /132 + C. \n\nWait, but from substitution method, we had (t -10)^12 /12 + 10(t -10)^11 /11. Let's see if these are equivalent. Let me expand both expressions. Let's take substitution answer: (t -10)^12 /12 + 10(t -10)^11 /11. Let's factor out (t -10)^11. Then, it becomes (t -10)^11 [ (t -10)/12 + 10/11 ]. Let's compute the terms inside the brackets: (t -10)/12 +10/11. To combine, common denominator is 132. So (11(t -10) + 120)/132. Which is (11t -110 +120)/132 = (11t +10)/132. So the substitution answer is (t -10)^11*(11t +10)/132 + C. Which is the same as the integration by parts result. So both methods give the same answer, just written differently. So that's good, they are equivalent. So either form is correct. But perhaps the substitution method's answer can be written as [(t -10)^11 (11(t -10) + 120)] /132, but I think either form is acceptable. But let's check if the original substitution answer can be simplified to match.\n\nOriginal substitution answer was (t -10)^12 /12 + 10(t -10)^11 /11. Let's combine these terms over a common denominator. The denominators are 12 and 11, so common denominator is 132. So convert each term: (t -10)^12 *11 /132 + 10(t -10)^11 *12 /132. Then factor out (t -10)^11 /132: [11(t -10) + 120] (t -10)^11 /132. Which is the same as (11t -110 +120)(t -10)^11 /132 = (11t +10)(t -10)^11 /132 + C. So same as integration by parts. Therefore, either form is correct, but perhaps the problem expects one form or the other. Let's see which is simpler. The original answer via substitution is (t -10)^12 /12 + (10/11)(t -10)^11 + C. The combined form is (11t +10)(t -10)^11 /132 + C. Both are acceptable, but maybe the problem expects the expanded form. Let me check with substitution's answer: (t -10)^12 /12 + 10(t -10)^11 /11 + C. Let's see if that's the standard answer. Alternatively, the problem might prefer the factored form. But the question says \"Evaluate the indefinite integral\" and doesn't specify the form, so either is acceptable, but perhaps the first form is more straightforward from substitution. However, let me check the derivative again to confirm.\n\nTaking derivative of (t -10)^12 /12: derivative is 12(t -10)^11 /12 = (t -10)^11. Derivative of 10(t -10)^11 /11 is 10*11(t -10)^10 /11 = 10(t -10)^10. Sum is (t -10)^11 + 10(t -10)^10. Which factors as (t -10)^10 [ (t -10) +10 ] = t(t -10)^10. Which matches the original integrand. So the answer is correct. \n\nAlternatively, the combined form: (11t +10)(t -10)^11 /132. Let's take the derivative. Using product rule: derivative of (11t +10)(t -10)^11. Let me denote f(t) = 11t +10, g(t) = (t -10)^11. The derivative is f'(t)g(t) + f(t)g'(t). f'(t) = 11. g'(t) = 11(t -10)^10. So derivative is 11*(t -10)^11 + (11t +10)*11(t -10)^10. Factor out 11(t -10)^10: 11(t -10)^10 [ (t -10) + (11t +10)/1 ] Wait, no. Let's compute term by term. \n\nWait, first term: 11*(t -10)^11. Second term: (11t +10)*11*(t -10)^10. So factor 11*(t -10)^10: [ (t -10) + (11t +10) ] *11*(t -10)^10. Inside the bracket: t -10 +11t +10 = 12t. So the derivative is 11*12t*(t -10)^10 = 132t(t -10)^10. Then divide by 132, as the original expression is divided by 132. So 132t(t -10)^10 /132 = t(t -10)^10, which matches the integrand. So that derivative is correct. Therefore, both forms are correct. So the answer can be presented either way.\n\nBut perhaps the problem expects the answer in expanded form rather than factored. Let me check what's more standard. Typically, when integrating, we might leave it in the expanded form unless specified otherwise. So, the substitution method gives (t -10)^12 /12 + (10/11)(t -10)^11 + C, which is two terms. Alternatively, the combined form is (11t +10)(t -10)^11 /132 + C. Both are correct, but the first form might be considered more expanded. However, in some textbooks, they might combine the terms. Let me see, but I think that the problem might accept either. However, let's see if there's another approach. Wait, could we do a substitution differently?\n\nAlternatively, perhaps expanding (t -10)^10, but that would involve binomial expansion, which would be tedious as it's to the 10th power. So that's probably not efficient. So substitution or integration by parts are the way to go. Since both methods work, and the answer can be expressed in two equivalent forms, either is acceptable. However, maybe the problem expects one of them. Let me check if perhaps the first method's answer is more straightforward. Let's see, the standard approach for ∫t(t - a)^n dt is substitution u = t - a, so t = u +a, leading to ∫(u +a)u^n du = ∫u^{n+1} +a u^n du, which integrates to u^{n+2}/(n+2) + a u^{n+1}/(n+1) + C, then substitute back. So yes, that's a standard method. So in this case, a=10, n=10, so u^{12}/12 + 10 u^{11}/11 + C. Therefore, substituting back gives the answer. So yes, that's correct. Therefore, the answer should be (t -10)^12 /12 + 10(t -10)^11 /11 + C.\n\nTherefore, after all that thinking and checking, the final answer is that expression. So I'll present that as the solution.\n</think>To evaluate the indefinite integral \\(\\int t(t-10)^{10} \\, dt\\), we use the substitution method.\n\n1. **Substitution**:\n   Let \\( u = t - 10 \\). Then, \\( du = dt \\) and \\( t = u + 10 \\).\n\n2. **Substitute into the integral**:\n   \\[\n   \\int t(t-10)^{10} \\, dt = \\int (u + 10)u^{10} \\, du\n   \\]\n\n3. **Expand the integrand**:\n   \\[\n   \\int (u + 10)u^{10} \\, du = \\int \\left(u^{11} + 10u^{10}\\right) \\, du\n   \\]\n\n4. **Integrate term by term**:\n   \\[\n   \\int u^{11} \\, du + 10 \\int u^{10} \\, du\n   \\]\n\n   Each term can be integrated separately:\n   \\[\n   \\int u^{11} \\, du = \\frac{u^{12}}{12}\n   \\]\n   \\[\n   10 \\int u^{10} \\, du = 10 \\cdot \\frac{u^{11}}{11} = \\frac{10u^{11}}{11}\n   \\]\n\n5. **Combine the results**:\n   \\[\n   \\frac{u^{12}}{12} + \\frac{10u^{11}}{11} + C\n   \\]\n\n6. **Substitute back \\( u = t - 10 \\)**:\n   \\[\n   \\frac{(t-10)^{12}}{12} + \\frac{10(t-10)^{11}}{11} + C\n   \\]\n\nThus, the final answer is:\n\\[\n\\boxed{\\frac{(t-10)^{12}}{12} + \\frac{10(t-10)^{11}}{11} + C}\n\\]"}