{"row_number": 2725, "expected_answer": "\\(\\frac{16}{9}\\)", "problem": "Find the sum of the infinite geometric sequence where the \\( n \\)-th term is defined by \\( a_n = \\frac{n}{4^{n-1}} \\).", "generated_solution": "<think>\nOkay, so I need to find the sum of an infinite geometric sequence where the nth term is given by a_n = n / 4^{n-1}. Hmm, let me think. First, I remember that a geometric series is one where each term is a constant multiple of the previous term. But wait, in this case, the nth term involves n in the numerator. That might complicate things because usually, geometric sequences have terms like ar^{n-1}, where a is the first term and r is the common ratio. Here, though, each term has an n multiplied by (1/4)^{n-1}. So maybe this isn't a standard geometric series, but perhaps a variation of it. \n\nLet me write out the first few terms to see the pattern. For n=1: a_1 = 1 / 4^{0} = 1. For n=2: a_2 = 2 / 4^{1} = 2/4 = 1/2. For n=3: a_3 = 3 / 4^{2} = 3/16. Then n=4: 4/64 = 1/16, and so on. So the terms are 1, 1/2, 3/16, 1/16, etc. Wait, each term isn't just multiplied by a constant ratio. Let me check the ratios between successive terms. From a_1 to a_2: (1/2)/1 = 1/2. From a_2 to a_3: (3/16)/(1/2) = (3/16)*(2/1) = 3/8. Then from a_3 to a_4: (1/16)/(3/16) = 1/3. The ratios are changing, so it's not a geometric series in the traditional sense. Therefore, the formula for the sum of a geometric series, S = a / (1 - r), might not apply directly here. \n\nHmm, so maybe this is an arithmetic-geometric series? Because the numerator is arithmetic (n) and the denominator is geometric (4^{n-1}). I think arithmetic-geometric series have the form n * r^{n}, or something like that. I remember that there's a formula for the sum of such a series. Let me recall. \n\nIf we have a sum S = Σ_{n=1}^∞ n * r^{n-1}, then this sum can be computed using differentiation. Wait, right! Because the standard geometric series sum is Σ_{n=0}^∞ r^n = 1/(1 - r) for |r| < 1. If we take the derivative of both sides with respect to r, we get Σ_{n=1}^∞ n * r^{n-1} = 1/(1 - r)^2. So, that's the formula. \n\nBut in our case, the term is n / 4^{n-1} = n * (1/4)^{n-1}. So this is exactly the same form as n * r^{n-1} where r = 1/4. Therefore, applying the formula, the sum S would be 1/(1 - r)^2, right? Let me check that. \n\nWait, let me re-derive it to be sure. Let S = Σ_{n=1}^∞ n * r^{n-1}. Then, integrating the standard geometric series. Let me recall:\n\nStarting with the geometric series sum: Σ_{n=0}^∞ r^n = 1/(1 - r). If we take the derivative of both sides with respect to r, we have:\n\nd/dr [Σ_{n=0}^∞ r^n] = Σ_{n=0}^∞ d/dr [r^n] = Σ_{n=1}^∞ n * r^{n-1} = d/dr [1/(1 - r)] = 1/(1 - r)^2.\n\nTherefore, Σ_{n=1}^∞ n * r^{n-1} = 1/(1 - r)^2. So, indeed, for |r| < 1, this holds. \n\nSo in our problem, the term a_n = n / 4^{n-1} can be written as n * (1/4)^{n-1}. Therefore, r is 1/4, which is less than 1 in absolute value, so the formula applies. \n\nTherefore, the sum S is 1/(1 - 1/4)^2. Let's compute that. 1 - 1/4 is 3/4. So (3/4)^2 is 9/16. Then 1 divided by 9/16 is 16/9. So the sum is 16/9. \n\nWait, but let me confirm. Let me calculate the first few terms and see if they approach 16/9. \n\nSo S = 1 + 1/2 + 3/16 + 4/64 + 5/256 + ... Let's compute the partial sums:\n\nS1 = 1\n\nS2 = 1 + 0.5 = 1.5\n\nS3 = 1.5 + 3/16 = 1.5 + 0.1875 = 1.6875\n\nS4 = 1.6875 + 4/64 = 1.6875 + 0.0625 = 1.75\n\nS5 = 1.75 + 5/256 ≈ 1.75 + 0.01953125 ≈ 1.76953125\n\nS6 = 1.76953125 + 6/1024 ≈ 1.76953125 + 0.005859375 ≈ 1.775390625\n\nS7 ≈ 1.775390625 + 7/4096 ≈ 1.775390625 + 0.001708984375 ≈ 1.777099609375\n\nS8 ≈ 1.777099609375 + 8/16384 ≈ 1.777099609375 + 0.00048828125 ≈ 1.777587890625\n\nContinuing this, each term is getting smaller, and the sum is approaching approximately 1.777..., which is 16/9. Let me compute 16 divided by 9. 9*1=9, 9*1.7=15.3, 9*1.77=15.93, 9*1.777=15.993, so 16/9 is approximately 1.777..., which matches the partial sums. So that seems to check out.\n\nTherefore, the sum of the infinite series is 16/9. \n\nBut wait, let me make sure that we applied the formula correctly. The formula gives Σ_{n=1}^∞ n * r^{n-1} = 1/(1 - r)^2. However, in our problem, the term is n / 4^{n-1} = n * (1/4)^{n - 1}. So r is 1/4, so substituting into the formula gives 1/(1 - 1/4)^2 = 1/(3/4)^2 = 16/9, which matches our earlier calculation. Therefore, the answer should be 16/9. \n\nBut just to be thorough, let me consider another approach. Maybe using generating functions or another method. \n\nAlternatively, let's let S = Σ_{n=1}^∞ n / 4^{n-1}. \n\nThen, multiply both sides by 4: 4S = Σ_{n=1}^∞ n / 4^{n - 2} = Σ_{n=1}^∞ n / 4^{n - 2} = Σ_{n=1}^∞ n * 4^{2 - n} = Σ_{n=1}^∞ n * (1/4)^{n - 2} = Σ_{n=1}^∞ n * (1/4)^{n - 1} * 4. Wait, that might complicate things. Alternatively, maybe subtract terms. \n\nAlternatively, let me write 4S - S:\n\n4S = Σ_{n=1}^∞ n / 4^{n - 2} = Σ_{n=1}^∞ n * 4^{2 - n} = Σ_{n=1}^∞ n * (1/4)^{n - 2} = Σ_{n=1}^∞ n * (1/4)^{n - 1} * 4.\n\nWait, perhaps another way. Let me adjust the index. Let k = n - 1. Then, when n=1, k=0, so 4S = Σ_{k=0}^∞ (k + 1) / 4^{k - 1} }? Wait, no, maybe I should handle it differently. \n\nAlternatively, consider S = Σ_{n=1}^∞ n * (1/4)^{n - 1}. Let me denote r = 1/4. Then S = Σ_{n=1}^∞ n * r^{n - 1}. As before, the sum is 1/(1 - r)^2, which gives 16/9. So, maybe this method is correct. \n\nAlternatively, if I let S = Σ_{n=1}^∞ n * r^{n - 1} and multiply by (1 - r):\n\n(1 - r) S = Σ_{n=1}^∞ n * r^{n - 1} - Σ_{n=1}^∞ n * r^n.\n\nLet me write out the terms:\n\nFirst sum: n=1: 1 * r^{0} = 1\n\nn=2: 2 * r^{1}\n\nn=3: 3 * r^{2}\n\n...\n\nSecond sum: n=1: 1 * r^{1}\n\nn=2: 2 * r^{2}\n\nn=3: 3 * r^{3}\n\n...\n\nSubtracting these:\n\n(1 - r)S = 1 + (2r - r) + (3r^2 - 2r^2) + (4r^3 - 3r^3) + ... \n\nWhich simplifies to 1 + r + r^2 + r^3 + ... which is the geometric series. Therefore, (1 - r)S = Σ_{n=0}^\\infty r^n = 1/(1 - r). Therefore, S = 1/(1 - r)^2. Which is the same formula as before. So this confirms it.\n\nTherefore, applying this to our problem where r = 1/4, the sum is 1/(1 - 1/4)^2 = (1/(3/4))^2 = (4/3)^2 = 16/9. So that must be the answer.\n\nTherefore, the sum of the infinite geometric sequence is 16/9.\n</think>To find the sum of the infinite series where the \\( n \\)-th term is given by \\( a_n = \\frac{n}{4^{n-1}} \\), we can use the formula for the sum of an arithmetic-geometric series. \n\nFirst, we recognize that the series can be written as:\n\\[\n\\sum_{n=1}^{\\infty} \\frac{n}{4^{n-1}} = \\sum_{n=1}^{\\infty} n \\left( \\frac{1}{4} \\right)^{n-1}.\n\\]\n\nThe general formula for the sum of a series of the form \\( \\sum_{n=1}^{\\infty} n r^{n-1} \\) is:\n\\[\n\\sum_{n=1}^{\\infty} n r^{n-1} = \\frac{1}{(1 - r)^2},\n\\]\nwhere \\( |r| < 1 \\).\n\nIn our case, \\( r = \\frac{1}{4} \\). Substituting \\( r = \\frac{1}{4} \\) into the formula, we get:\n\\[\n\\sum_{n=1}^{\\infty} n \\left( \\frac{1}{4} \\right)^{n-1} = \\frac{1}{\\left(1 - \\frac{1}{4}\\right)^2}.\n\\]\n\nSimplifying the denominator:\n\\[\n1 - \\frac{1}{4} = \\frac{3}{4},\n\\]\nand squaring it:\n\\[\n\\left( \\frac{3}{4} \\right)^2 = \\frac{9}{16}.\n\\]\n\nThus, the sum is:\n\\[\n\\frac{1}{\\left( \\frac{3}{4} \\right)^2} = \\frac{1}{\\frac{9}{16}} = \\frac{16}{9}.\n\\]\n\nTherefore, the sum of the infinite series is:\n\\[\n\\boxed{\\frac{16}{9}}.\n\\]"}