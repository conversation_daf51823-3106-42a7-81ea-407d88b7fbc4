{"row_number": 2712, "expected_answer": "0", "problem": "Find \\(\\lim_{n\\to\\infty}\\int_{0}^{\\frac{\\pi}{2}}\\sin(n^{4}\\cdot \\cos t) \\, dt\\).", "generated_solution": "<think>\nOkay, so I have this limit to find: the limit as n approaches infinity of the integral from 0 to pi/2 of sin(n^4 * cos t) dt. Hmm, let me think about how to approach this. \n\nFirst, I remember that when dealing with limits involving integrals with oscillatory integrands, especially when there's a parameter that's getting large (like n^4 here), techniques like the Riemann-Le<PERSON> lemma might be useful. The Riemann-Lebesgue lemma says that the integral of a rapidly oscillating function over an interval tends to zero as the frequency of oscillation goes to infinity, provided the function is sufficiently smooth. But I need to check if the conditions for applying it are met here.\n\nThe integrand here is sin(n^4 * cos t). The function inside the sine is n^4 multiplied by cos t. As n goes to infinity, n^4 * cos t becomes very large except when cos t is zero. But cos t is zero at t = pi/2, which is the upper limit of integration. Wait, but cos(pi/2) is zero, so near t = pi/2, cos t is approximately (pi/2 - t), right? So near the upper limit, n^4 * cos t is roughly n^4*(pi/2 - t). But for t away from pi/2, cos t is bounded away from zero, so n^4 * cos t would be very large, causing the sine function to oscillate rapidly. However, maybe the main contribution to the integral comes from a neighborhood around t = pi/2 where cos t is small, since elsewhere the sine term oscillates rapidly and cancels out. \n\nSo perhaps I can split the integral into two parts: one near pi/2 where cos t is small, and the rest where cos t is not too small. Let me try that. Let’s set a small delta around t = pi/2. For t in [pi/2 - delta, pi/2], cos t is approximately (pi/2 - t), and for t in [0, pi/2 - delta], cos t is at least some positive number, say cos(pi/2 - delta) = sin(delta), which is approximately delta when delta is small. \n\nSo if I split the integral into [0, pi/2 - delta] and [pi/2 - delta, pi/2]. For the first part, the integral over [0, pi/2 - delta], the integrand is sin(n^4 * cos t). Since cos t is at least sin(delta) here, which is some positive number not approaching zero as n increases. Then n^4 * cos t is a very large number as n increases, leading to rapid oscillations in the sine function. By the Riemann-Lebesgue lemma, the integral over [0, pi/2 - delta] should go to zero as n approaches infinity. \n\nSo the main contribution is from the interval [pi/2 - delta, pi/2]. Let me change variables to make this integral easier. Let’s set u = pi/2 - t. Then when t = pi/2 - delta, u = delta, and when t = pi/2, u = 0. So the integral becomes the integral from u = 0 to u = delta of sin(n^4 * cos(pi/2 - u)) * du. Since cos(pi/2 - u) = sin u, so the integral becomes the integral from 0 to delta of sin(n^4 * sin u) du. \n\nNow, when delta is small (as n becomes large, perhaps we can take delta to depend on n such that n^4 * sin u is of order 1?), but maybe another substitution. Let me think. If u is small, sin u ≈ u, so sin(n^4 * sin u) ≈ sin(n^4 u). Then, if we substitute v = n^4 u, then u = v / n^4, du = dv / n^4. The limits of integration become from v = 0 to v = n^4 * delta. So the integral becomes approximately the integral from 0 to n^4 delta of sin(v) * (dv / n^4). \n\nBut the integral of sin(v) dv from 0 to L is 1 - cos(L). So this integral would be (1 - cos(n^4 delta)) / n^4. Now, if we can choose delta such that n^4 delta tends to some limit as n approaches infinity. Wait, but delta can be chosen as a small fixed number, but then n^4 delta would go to infinity, making cos(n^4 delta) oscillate between -1 and 1. However, as n approaches infinity, (1 - cos(n^4 delta))/n^4 would go to zero because the numerator is bounded between 0 and 2, and the denominator goes to infinity. \n\nWait, that seems like even the integral over [pi/2 - delta, pi/2] tends to zero. But that contradicts my initial thought that the main contribution is from near pi/2. Maybe I made a wrong substitution here? Let me check.\n\nWait, perhaps I need a different substitution. Let's think again. The original integral near t = pi/2: Let u = pi/2 - t, so t = pi/2 - u. Then cos t = sin u. So the integral becomes ∫₀^δ sin(n⁴ sin u) du. Now, for u near 0, sin u ≈ u. So approximating sin(n⁴ sin u) as sin(n⁴ u). Then, substitute v = n⁴ u, so u = v/n⁴, du = dv/n⁴. Then the integral becomes ∫₀^{n⁴ δ} sin(v) / n⁴ dv. The integral of sin(v) from 0 to K is 1 - cos(K). So this becomes (1 - cos(n⁴ δ)) / n⁴. As n → ∞, if δ is fixed, then n⁴ δ → ∞, so cos(n⁴ δ) oscillates, but the entire expression is O(1/n⁴) which tends to zero. Therefore, even this part tends to zero. But then both parts of the integral go to zero? Then the limit would be zero? \n\nBut that can't be right because maybe there's a cancellation in the integral over [0, pi/2 - delta], but Riemann-Lebesgue says that integral goes to zero. Then the entire integral would go to zero. But let me check for a specific example. For example, if instead of n^4, we have n. Then integrating sin(n cos t) from 0 to pi/2. Then using similar reasoning, the integral would go to zero. But maybe here, with n^4, it's similar. \n\nAlternatively, perhaps there is a stationary phase contribution. Wait, the method of stationary phase is used for integrals of the form ∫ e^{i n φ(t)} g(t) dt, where φ(t) has a stationary point (a point where φ’(t)=0), and the main contribution to the integral comes from the neighborhood of the stationary point. But in our case, the integral is ∫ sin(n^4 cos t) dt. So the phase here is cos t, but multiplied by n^4. Wait, but sin(n^4 cos t) is the imaginary part of e^{i n^4 cos t}. So perhaps the method of stationary phase applies here.\n\nSo let's try that approach. The integral is the imaginary part of ∫₀^{pi/2} e^{i n^4 cos t} dt. To apply stationary phase, we need to find the stationary points of the phase function φ(t) = cos t. The derivative φ’(t) = -sin t. Setting φ’(t) = 0 gives sin t = 0, which occurs at t=0 and t=pi in the interval [0, pi], but our interval is [0, pi/2]. So in [0, pi/2], the only critical point is at t=0, since sin(0)=0. But wait, t=0 is the lower limit. Hmm, but if the stationary point is at t=0, then we need to check the contribution near t=0.\n\nWait, but the phase function is cos t, which has a maximum at t=0 (since cos 0 = 1) and it's decreasing to cos(pi/2) = 0. So the maximum of the phase occurs at t=0, but since we are dealing with an integral from 0 to pi/2, the stationary point is at the endpoint t=0. But the method of stationary phase for endpoints is different. Let me recall.\n\nIn the method of stationary phase, if the phase has a stationary point in the interior of the interval, then the main contribution comes from that point. If the phase has no stationary points in the interior, but has a maximum or minimum at the endpoint, then the main contribution comes from the endpoint. But in our case, the phase is cos t, which has a maximum at t=0. So maybe the main contribution comes from t=0?\n\nWait, but when you have an integral ∫ e^{i n φ(t)} dt from a to b, if the phase φ(t) has its maximum at an endpoint, say t=a, then the leading term of the integral is of order n^{-1/2}, but if the maximum is at an interior point, it's n^{-1/2}. Wait, but in our case, the phase is cos t, which is decreasing from t=0 to t=pi/2. So the maximum is at t=0. So according to the method of stationary phase, the integral ∫₀^{pi/2} e^{i n^4 cos t} dt would have a leading term coming from the neighborhood of t=0. Let's try to approximate it.\n\nBut first, in our integral, the exponent is n^4 cos t. So the large parameter is n^4. Let's set k = n^4, so the integral becomes ∫₀^{pi/2} e^{i k cos t} dt with k → ∞. Then, the standard stationary phase result would give that the integral is approximated by e^{i k cos(0)} * sqrt(2π/(k |φ''(0)|)) * e^{i π/4 * sgn(φ''(0))} }, divided by 2, but since the stationary point is at the endpoint, the contribution is half of that.\n\nWait, let me recall the endpoint case. When the phase has its maximum at the endpoint, the leading term is (1/2) * e^{i k φ(a)} * sqrt(2π/(k |φ''(a)|))} * e^{i π/4 * sgn(φ''(a))}, assuming φ'(a) = 0 (but here, at the endpoint, φ(t) may not have a stationary point, but if the maximum is at the endpoint and φ'(a) ≠ 0, then the contribution is different). Wait, maybe I need to be careful here.\n\nWait, if the maximum of the phase is at t=0, which is an endpoint. But the derivative of the phase at t=0 is φ'(0) = -sin(0) = 0? Wait, φ(t) = cos t, so φ'(t) = -sin t. At t=0, φ'(0) = 0, so actually t=0 is a stationary point. So even though it's the endpoint, since the phase has a stationary point at t=0, which is a maximum, we need to use the stationary phase approximation for a stationary point at the endpoint.\n\nWait, in standard stationary phase, if the phase has a stationary point in the interior, the integral is approximated by contributions near that point. If the stationary point is at the endpoint, the contribution is similar but with a factor of 1/2. But I need to verify this. Let me check.\n\nAlternatively, perhaps expanding the phase around t=0. Let’s make a substitution. Let’s set t = 0 + s, where s is small. Then cos t ≈ 1 - s²/2. So the phase is φ(s) = cos s ≈ 1 - s²/2. Therefore, the integral becomes approximately e^{i k (1 - s²/2)}. So ∫₀^{pi/2} e^{i k (1 - s²/2)} ds ≈ e^{i k} ∫₀^{infty} e^{-i k s²/2} ds. Here, we extended the upper limit to infinity since the main contribution is near s=0. The integral ∫₀^infty e^{-i k s²/2} ds = (1/2) sqrt(2π/(k/2)) e^{-i π/4} } = sqrt(π/(k)) e^{-i π/4}. \n\nTherefore, the leading term is e^{i k} * sqrt(π/(2k)) e^{-i π/4}. \n\nBut in our case, k = n^4, so the leading term is e^{i n^4} * sqrt(π/(2 n^4)) e^{-i π/4} = e^{i (n^4 - π/4)} sqrt(π/(2 n^4)}. \n\nSince our original integral is the imaginary part of this expression, we take the imaginary part of e^{i (n^4 - π/4)} sqrt(π/(2 n^4)}. \n\nNow, e^{i (n^4 - π/4)} = cos(n^4 - π/4) + i sin(n^4 - π/4). The imaginary part is sin(n^4 - π/4). Therefore, the imaginary part of the integral is sin(n^4 - π/4) * sqrt(π/(2 n^4)}. \n\nSo as n approaches infinity, the integral behaves like sin(n^4 - π/4) * sqrt(π/(2 n^4)}. The amplitude decays like 1/n², since sqrt(1/n^4) is 1/n². Therefore, the entire expression tends to zero as n approaches infinity. \n\nTherefore, both approaches seem to suggest that the integral tends to zero. The Riemann-Lebesgue lemma says that the integral over [0, pi/2 - delta] goes to zero, and the integral near pi/2 also goes to zero as 1/n². So the overall limit is zero.\n\nBut wait, the method of stationary phase gives an oscillating term multiplied by 1/n², which tends to zero. So regardless of the oscillation, the amplitude decays, leading the whole expression to zero. Therefore, the limit should be zero.\n\nAlternatively, maybe I made a mistake in the substitution when considering the endpoint. Let me check another way. Suppose I change variables to make the integral near t=0. Let’s set t = s/n², so when s ranges from 0 to n² pi/2. Then, cos t ≈ 1 - t²/2 = 1 - s²/(2 n^4). Then, n^4 cos t ≈ n^4 - s²/2. Then sin(n^4 cos t) ≈ sin(n^4 - s²/2) = sin(n^4) cos(s²/2) - cos(n^4) sin(s²/2). Then the integral becomes ∫₀^{n² pi/2} [sin(n^4) cos(s²/2) - cos(n^4) sin(s²/2)] * (ds/n²). \n\nBut as n approaches infinity, the upper limit goes to infinity. So splitting the integral into two parts:\n\nsin(n^4) ∫₀^{infty} cos(s²/2)/n² ds - cos(n^4) ∫₀^{infty} sin(s²/2)/n² ds.\n\nBut each integral is O(1/n²), so the whole expression is O(1/n²) which tends to zero. Therefore, the integral tends to zero.\n\nTherefore, regardless of the approach, the integral tends to zero as n approaches infinity. Hence, the limit is zero.\n</think>To find the limit \\(\\lim_{n\\to\\infty}\\int_{0}^{\\frac{\\pi}{2}}\\sin(n^{4}\\cos t) \\, dt\\), we will use the Riemann-Lebesgue lemma and the method of stationary phase.\n\n### Step 1: Application of Riemann-Lebesgue Lemma\nFor \\(t \\in [0, \\frac{\\pi}{2} - \\delta]\\), where \\(\\delta\\) is a small positive number, \\(\\cos t\\) is bounded away from zero. As \\(n \\to \\infty\\), \\(n^4 \\cos t\\) becomes very large, causing \\(\\sin(n^4 \\cos t)\\) to oscillate rapidly. By the Riemann-Lebesgue lemma, the integral over this interval tends to zero:\n\\[\n\\int_{0}^{\\frac{\\pi}{2} - \\delta} \\sin(n^4 \\cos t) \\, dt \\to 0 \\quad \\text{as} \\quad n \\to \\infty.\n\\]\n\n### Step 2: Behavior Near \\(t = \\frac{\\pi}{2}\\)\nConsider the interval \\([ \\frac{\\pi}{2} - \\delta, \\frac{\\pi}{2} ]\\). Let \\(u = \\frac{\\pi}{2} - t\\), so \\(t = \\frac{\\pi}{2} - u\\) and \\(dt = -du\\). The integral becomes:\n\\[\n\\int_{0}^{\\delta} \\sin(n^4 \\cos(\\frac{\\pi}{2} - u)) \\, du = \\int_{0}^{\\delta} \\sin(n^4 \\sin u) \\, du.\n\\]\nFor small \\(u\\), \\(\\sin u \\approx u\\), so the integral is approximately:\n\\[\n\\int_{0}^{\\delta} \\sin(n^4 u) \\, du.\n\\]\nLet \\(v = n^4 u\\), then \\(u = \\frac{v}{n^4}\\) and \\(du = \\frac{dv}{n^4}\\). The integral transforms to:\n\\[\n\\frac{1}{n^4} \\int_{0}^{n^4 \\delta} \\sin(v) \\, dv.\n\\]\nThe integral \\(\\int_{0}^{n^4 \\delta} \\sin(v) \\, dv = 1 - \\cos(n^4 \\delta)\\) oscillates between 0 and 2. Therefore:\n\\[\n\\frac{1}{n^4} (1 - \\cos(n^4 \\delta)) \\to 0 \\quad \\text{as} \\quad n \\to \\infty.\n\\]\n\n### Step 3: Method of Stationary Phase\nConsider the integral \\(\\int_{0}^{\\frac{\\pi}{2}} \\sin(n^4 \\cos t) \\, dt\\). Rewrite it as the imaginary part of:\n\\[\n\\int_{0}^{\\frac{\\pi}{2}} e^{i n^4 \\cos t} \\, dt.\n\\]\nThe phase function is \\(\\phi(t) = \\cos t\\), which has a maximum at \\(t = 0\\). Expanding \\(\\cos t\\) around \\(t = 0\\):\n\\[\n\\cos t \\approx 1 - \\frac{t^2}{2}.\n\\]\nLet \\(t = s/n^2\\), then \\(dt = \\frac{ds}{n^2}\\) and the integral becomes:\n\\[\n\\int_{0}^{n^2 \\frac{\\pi}{2}} e^{i n^4 (1 - \\frac{s^2}{2n^4})} \\frac{ds}{n^2} = \\frac{1}{n^2} \\int_{0}^{n^2 \\frac{\\pi}{2}} e^{i (n^4 - \\frac{s^2}{2})} \\, ds.\n\\]\nAs \\(n \\to \\infty\\), the upper limit \\(n^2 \\frac{\\pi}{2} \\to \\infty\\). The integral splits into:\n\\[\n\\frac{1}{n^2} \\int_{0}^{\\infty} e^{i (n^4 - \\frac{s^2}{2})} \\, ds = \\frac{1}{n^2} e^{i n^4} \\int_{0}^{\\infty} e^{-i \\frac{s^2}{2}} \\, ds.\n\\]\nThe integral \\(\\int_{0}^{\\infty} e^{-i \\frac{s^2}{2}} \\, ds\\) is a known Fresnel integral, which evaluates to:\n\\[\n\\int_{0}^{\\infty} e^{-i \\frac{s^2}{2}} \\, ds = \\frac{1}{\\sqrt{2}} \\sqrt{\\frac{\\pi}{i}} = \\frac{1}{\\sqrt{2}} \\sqrt{\\frac{\\pi}{i}} = \\frac{1}{\\sqrt{2}} \\sqrt{\\frac{\\pi}{i}} = \\frac{1}{\\sqrt{2}} \\sqrt{\\frac{\\pi}{i}}.\n\\]\nThus, the integral becomes:\n\\[\n\\frac{1}{n^2} e^{i n^4} \\cdot \\frac{1}{\\sqrt{2}} \\sqrt{\\frac{\\pi}{i}}.\n\\]\nThe imaginary part of this expression decays as \\(\\frac{1}{n^2}\\), leading to:\n\\[\n\\lim_{n \\to \\infty} \\frac{1}{n^2} \\sin(n^4 - \\frac{\\pi}{4}) \\sqrt{\\frac{\\pi}{2}} = 0.\n\\]\n\n### Conclusion\nBoth the Riemann-Lebesgue lemma and the method of stationary phase confirm that the integral tends to zero as \\(n \\to \\infty\\). Therefore, the limit is:\n\\[\n\\boxed{0}.\n\\]"}