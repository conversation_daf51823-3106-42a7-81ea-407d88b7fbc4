{"row_number": 2690, "expected_answer": "\\(\\frac{\\sqrt{2\\pi}}{4}\\)", "problem": "Evaluate the integral \\(\\int\\limits_{0}^{+\\infty} \\cos(x^2) \\, dx\\).", "generated_solution": "<think>\nOkay, so I need to evaluate the integral of cos(x²) from 0 to infinity. Hmm, I remember that integrals involving cos(x²) and sin(x²) are called Fresnel integrals. But I'm not exactly sure how to approach them. Let me think.\n\nFirst, I know that integrating cos(x²) directly is tricky because there's no elementary antiderivative for it. So maybe there's a clever substitution or a method from complex analysis that can help here. Wait, maybe using the gamma function? But the gamma function involves integrals of x^n times e^{-x}, right? Not sure how that connects here.\n\nAlternatively, I recall that sometimes these integrals can be related to the Gaussian integral, which is the integral of e^{-x²} from 0 to infinity. That one equals √(π)/2. Maybe there's a way to relate cos(x²) to e^{i x²} since <PERSON><PERSON><PERSON>'s formula connects complex exponentials to trigonometric functions. So maybe using complex analysis here?\n\nLet me write down the integral I need: ∫₀^∞ cos(x²) dx. If I consider e^{i x²}, its integral from 0 to infinity would have both cosine and sine parts. The real part of that integral would be my desired integral. But how do I compute the integral of e^{i x²}?\n\nI think contour integration might be the key here. Let me recall that. The integral of e^{i x²} from -∞ to ∞ is known to be √(π) e^{i π/4}, which is derived using a contour integral. But since our limits are from 0 to ∞, maybe we can use that result.\n\nWait, yes! The integral from -∞ to ∞ of e^{i x²} dx is √(π) e^{i π/4}. Since the integrand is even, the integral from 0 to ∞ would be half of that. So that would be √(π) e^{i π/4} / 2. Then, taking the real part of this would give the integral of cos(x²) dx from 0 to ∞.\n\nLet me verify this step by step. First, the Gaussian integral with e^{-x²} is √π. But here we have e^{i x²}. The standard trick for that is to use a contour integral in the complex plane. Let's say we consider the integral of e^{i z²} over a contour that forms a sector in the complex plane from 0 to some angle. If we take the angle to be π/4, since the exponent becomes i (r e^{iθ})² = i r² e^{i2θ}. For θ = π/4, that becomes i r² e^{i π/2} = i r² i = -r². So then the integral over the arc at angle π/4 would be similar to the Gaussian integral.\n\nLet me sketch this. Consider the contour integral of e^{i z²} over the path from 0 to R on the real axis, then along a circular arc of angle π/4 to R e^{i π/4}, then back to 0 along the line at 45 degrees. Since e^{i z²} is entire (analytic everywhere), by Cauchy's theorem, the integral over the closed contour is zero. Then, the integral over the real axis plus the integral over the arc plus the integral over the radial line equals zero.\n\nAs R approaches infinity, the integral over the arc should go to zero. Let me check: the integral over the arc is from θ=0 to θ=π/4 of e^{i (R e^{iθ})²} i R e^{iθ} dθ. Substitute z = R e^{iθ}, dz = i R e^{iθ} dθ. Then the exponent becomes i R² e^{i2θ} = i R² (cos(2θ) + i sin(2θ)) = i R² cos(2θ) - R² sin(2θ). So the integrand becomes e^{i R² cos(2θ) - R² sin(2θ)} * i R e^{iθ} dθ.\n\nThe magnitude of this is e^{-R² sin(2θ)} * R. So to show this integral goes to zero as R→infty, we can use Jordan's lemma or the estimation lemma. Since sin(2θ) is positive for θ in (0, π/4], because 2θ goes up to π/2. So sin(2θ) ≥ sin(0) = 0 and actually, for θ in (0, π/4], 2θ is in (0, π/2], so sin(2θ) is positive, and in fact, sin(2θ) ≥ (2θ) * (2/π) for θ in [0, π/4], maybe? Wait, maybe not exactly, but the important thing is that sin(2θ) is bounded below by some positive multiple of θ as θ approaches 0. But as R→∞, the exponential term e^{-R² sin(2θ)} decays rapidly except near θ=0. However, the integral over the entire arc can be estimated by the maximum value times the length of the arc. The maximum of e^{-R² sin(2θ)} occurs at θ=0, but as R increases, even near θ=0, the exponential decay dominates. Wait, maybe another approach.\n\nThe integral's magnitude is ≤ ∫₀^{π/4} e^{-R² sin(2θ)} R dθ. Let me make substitution: let φ = 2θ, so θ = φ/2, dθ = dφ/2. Then, the integral becomes (R/2) ∫₀^{π/2} e^{-R² sinφ} dφ. Now, for φ in [0, π/2], sinφ ≥ (2/π)φ, so e^{-R² sinφ} ≤ e^{-R² (2/π) φ}. Therefore, the integral is ≤ (R/2) ∫₀^{π/2} e^{-(2 R² / π) φ} dφ. Compute that integral: [ -π/(2 R²) e^{-(2 R² / π) φ} ] from 0 to π/2. Evaluating, we get (R/2) [ -π/(2 R²) (e^{- R²} - 1) ] = (R/2)( π/(2 R²) (1 - e^{- R²}) ) = (π)/(4 R) (1 - e^{- R²}). As R approaches infinity, this goes to zero. Therefore, the integral over the arc goes to zero as R→infty.\n\nTherefore, the integral over the real axis from 0 to R plus the integral over the radial line from R e^{i π/4} to 0 equals zero. But the integral over the radial line can be parameterized by z = t e^{i π/4} for t from R to 0. So dz = e^{i π/4} dt. Then, the integral becomes ∫_R^0 e^{i (t e^{i π/4})²} e^{i π/4} dt. Compute the exponent: (t e^{i π/4})² = t² e^{i π/2} = i t². Therefore, the integral is ∫_R^0 e^{i (i t²)} e^{i π/4} dt = ∫_R^0 e^{- t²} e^{i π/4} dt. Flipping the limits, it's e^{i π/4} ∫_0^R e^{- t²} dt.\n\nPutting it all together, the integral over the real axis (from 0 to R) is ∫₀^R e^{i x²} dx. Then, as R→infty, we have ∫₀^∞ e^{i x²} dx + e^{i π/4} ∫₀^∞ e^{- t²} dt = 0. Therefore, ∫₀^∞ e^{i x²} dx = -e^{i π/4} ∫₀^∞ e^{- t²} dt. But since ∫₀^∞ e^{- t²} dt = √π / 2, we have ∫₀^∞ e^{i x²} dx = -e^{i π/4} (√π / 2). Wait, but hold on, a negative sign here. But the integral over the contour is zero, so the real axis integral equals negative the radial integral. But let me check the orientation. The radial integral was from R to 0, so when we flipped the limits, we got an integral from 0 to R with a negative sign. Then, putting into the equation: real axis integral (from 0 to R) + radial integral (from R to 0) = 0 → real axis integral - e^{i π/4} ∫₀^R e^{-t²} dt = 0. So real axis integral = e^{i π/4} ∫₀^R e^{-t²} dt. Then taking R→infty, ∫₀^∞ e^{i x²} dx = e^{i π/4} ∫₀^∞ e^{-t²} dt = e^{i π/4} (√π / 2).\n\nTherefore, the integral of e^{i x²} from 0 to infinity is (√π / 2) e^{i π/4}. Now, since e^{i π/4} = cos(π/4) + i sin(π/4) = (√2/2) + i (√2/2). Therefore, the real part of the integral is (√π / 2)(√2/2) = √π / (2√2) * √2/√2 = √(π/2) / 2. Wait, let me compute this again. The real part is (√π / 2) * cos(π/4) = (√π / 2)(√2/2) = √π * √2 / 4 = √(2π)/4 = √(π/2)/2? Wait, √(2π)/4 is equivalent to √(π)/ (2√2). Because √(2π) = √2 * √π, so √2π / 4 = √π / (2√2). Alternatively, √(π/2)/2. Hmm, not sure. Let me compute:\n\n(√π / 2) * (√2 / 2) = (√π * √2) / 4 = √(2π) / 4. Alternatively, factor 2: √(2π)/4 = (1/√2) * √π / 2. So that's equivalent to √(π/2)/2. Wait, no. √(2π)/4 = √π/(2√2). Because √(2π) is √2 * √π, divided by 4, which is √π/(2√2). Yeah, that's right. So √π divided by 2√2, which can also be written as √(π)/ (2√2) or (1/2)√(π/(2)).\n\nSo, since the original integral we needed is ∫₀^∞ cos(x²) dx, which is the real part of ∫₀^∞ e^{i x²} dx, so that's √(π)/(2√2). So the answer is √(2π)/4? Wait, hold on, because √π/(2√2) is equal to √(π/2)/2. Let me compute √π/(2√2). If I rationalize the denominator, √π/(2√2) = √π * √2 / (2 * 2) = √(2π)/4. So both forms are equivalent. So either √(π)/(2√2) or √(2π)/4. Which is the standard form? The Fresnel integrals are often written as √(π/(8)) for both cosine and sine integrals. Wait, no. Wait, the Fresnel integrals C(∞) and S(∞) both equal 1/2 √(π/2). So, the integral of cos(x²) from 0 to ∞ is (1/2)√(π/2), which is √(π)/(2√2) or √(2π)/4.\n\nYes, because √(π/2)/2 = √π/(2√2) and √(2π)/4 is equivalent. Let me check numerically. √(pi) ≈ 1.77245, divided by 2√2 ≈ 2.8284, so 1.77245 / 2.8284 ≈ 0.6267. Alternatively, √(2π) ≈ 2.5066, divided by 4 ≈ 0.6267. Same result. So either form is correct, but perhaps the more standard form is (1/2)√(π/2), which is √(π)/ (2√2). But sometimes it's written as √(2π)/4. Either way, both are equivalent. Let me confirm with a reference. Wait, I can't look it up, but I remember that the Fresnel integral C(∞) is √(π/8) * 2, because the integral from 0 to ∞ is √(π/8) * 2. Wait, maybe not. Let me think again.\n\nActually, the Fresnel integrals are defined as C(t) = ∫₀^t cos( (π/2) x² ) dx, and similarly for S(t). Then, C(∞) = S(∞) = 1/2. But if the integral is ∫₀^∞ cos(x²) dx, we need to adjust variables. Let me make substitution. Let y = x², then dy = 2x dx, but that might not help. Alternatively, if the Fresnel integral is ∫₀^∞ cos(π x² / 2) dx = 1/2. So to relate that to our integral, set (π/2) x² = t². Let t = x √(π/2), then x = t / √(π/2), dx = dt / √(π/2). Then, ∫₀^∞ cos( (π/2) x² ) dx = ∫₀^∞ cos(t²) * dt / √(π/2) = (1/√(π/2)) ∫₀^∞ cos(t²) dt. But we know that integral equals 1/2. Therefore, (1/√(π/2)) * ∫₀^∞ cos(t²) dt = 1/2 → ∫₀^∞ cos(t²) dt = (1/2) √(π/2). Therefore, the integral is (1/2)√(π/2). Which is equal to √(π)/(2√2) or √(2π)/4. So yes, that's the answer.\n\nSo going back, our initial calculation using contour integration gave us that the integral of cos(x²) from 0 to ∞ is √(π)/(2√2). So that's the answer.\n\nAlternatively, another method: using polar coordinates. Wait, but the Gaussian integral uses polar coordinates. Maybe we can relate the integral of cos(x²) to the Gaussian integral somehow. Let me think. If we consider the integral of e^{-a x²} as √(π/a)/2 for real a > 0. But here we have a = -i, which is complex. Wait, but maybe using analytic continuation. If we can consider a to be a complex variable with Re(a) > 0, then the integral ∫₀^∞ e^{-a x²} dx = 1/2 √(π/a). Then, if we set a = -i, which has Re(a) = 0, but maybe by analytic continuation, ∫₀^∞ e^{i x²} dx = 1/2 √(π/(-i)). Then, √(1/(-i)) = √(i) = e^{i π/4} √(1). Since -i = e^{-i π/2}, so √(1/(-i)) = √(e^{i π/2}) = e^{i π/4}. Therefore, ∫₀^∞ e^{i x²} dx = 1/2 √(π) e^{i π/4} / √i. Wait, but √i is e^{i π/4}, so √(1/(-i)) is √(e^{i π/2}) = e^{i π/4}, which is the same as before. So that gives ∫₀^∞ e^{i x²} dx = (1/2) √(π) e^{i π/4} / √i. Wait, but √i is e^{i π/4}, so √(1/(-i)) is e^{i π/4}, so perhaps I need to compute √(π/(-i)).\n\nLet me verify. Since a = -i, so √(π/a) = √(π/(-i)) = √(π) * √(1/(-i)). Now, 1/(-i) = i, so √(i) = e^{i π/4}. Therefore, √(π/(-i)) = √π * e^{i π/4}. Therefore, ∫₀^∞ e^{i x²} dx = (1/2) √π e^{i π/4}, which matches our previous result. Therefore, the real part is (1/2) √π cos(π/4) = (1/2) √π * √2/2 = √(2π)/4, which is the same as √(π)/(2√2). So either way, the answer is that.\n\nTherefore, the value of the integral ∫₀^∞ cos(x²) dx is √(2π)/4 or equivalently (1/2)√(π/2). So the final answer is \\boxed{\\dfrac{\\sqrt{2\\pi}}{4}} or \\boxed{\\dfrac{1}{2}\\sqrt{\\dfrac{\\pi}{2}}}. Both are correct. I think the former might be more common, but let me check the standard result.\n\nWait, according to standard references, the Fresnel integrals are C(∞) = ∫₀^∞ cos(π x² / 2) dx = 1/2. So if we adjust variables here, let x = t * √(2/π), then dx = √(2/π) dt. Then, ∫₀^∞ cos(π x² / 2) dx = ∫₀^∞ cos(t²) * √(2/π) dt = 1/2. Therefore, √(2/π) ∫₀^∞ cos(t²) dt = 1/2 → ∫₀^∞ cos(t²) dt = (1/2)√(π/2). So that's the same as (1/2)√(π/2), which is √(π)/(2√2) or √(2π)/4. All equivalent. So I can present the answer as either of these forms. Maybe the problem expects the answer in terms of √(2π)/4. But both are acceptable. Let me confirm.\n\nAlternatively, maybe the problem expects the answer as \\boxed{\\frac{\\sqrt{\\pi}}{2\\sqrt{2}}} or \\boxed{\\frac{1}{2}\\sqrt{\\frac{\\pi}{2}}}. Both are correct. But since in the calculation we arrived at √(π)/ (2√2), maybe that's the most straightforward. But let me check the calculation again.\n\nFrom the contour integral, we had ∫₀^∞ e^{i x²} dx = e^{i π/4} ∫₀^∞ e^{-t²} dt = e^{i π/4} (√π / 2). Then, taking the real part, Re(e^{i π/4} √π / 2) = √π / 2 * cos(π/4) = √π / 2 * √2 / 2 = √(2π)/4. So that's equivalent. Therefore, writing it as √(2π)/4 might be more direct.\n\nBut let me check what is √(2π)/4 numerically. √(2π) is approximately 2.5066, divided by 4 is about 0.6267. Which matches the known value for the Fresnel integral. So yes, that's correct.\n\nSo the answer is either √(2π)/4 or (1/2)√(π/2). Depending on how the problem expects it, but since both are correct, I think either is acceptable. Maybe the problem expects √(2π)/4. To check, maybe refer to the original Fresnel integral result. But in the problem statement, it's just asking for the integral of cos(x²) from 0 to infinity, which is known to be (1/2)√(π/2). So perhaps the answer should be written as \\boxed{\\dfrac{\\sqrt{2\\pi}}{4}}.\n\nAlternatively, another approach: using real analysis. Let me think. Maybe integrating over a double integral and switching to polar coordinates. For example, consider I = ∫₀^∞ cos(x²) dx. Consider I^2, but since I is a real number, I^2 would be (∫₀^∞ cos(x²) dx)^2, which might not be helpful. However, maybe consider integrating e^{-x²} and relate it through some substitution. Wait, but that seems unclear.\n\nAlternatively, consider I = ∫₀^∞ cos(x²) dx, J = ∫₀^∞ sin(x²) dx. Then, I + iJ = ∫₀^∞ e^{i x²} dx = √(π)/2 e^{i π/4}, as we had earlier. So taking real and imaginary parts, I = √(π)/2 cos(π/4) = √(π)/2 * √2/2 = √(2π)/4, and similarly J = √(2π)/4. So indeed, I = J = √(2π)/4. So that's another way to get here.\n\nTherefore, the integral evaluates to √(2π)/4. So the final answer is \\boxed{\\dfrac{\\sqrt{2\\pi}}{4}}.\n</think>To evaluate the integral \\(\\int\\limits_{0}^{+\\infty} \\cos(x^2) \\, dx\\), we use complex analysis and contour integration. The key steps are as follows:\n\n1. **Consider the complex integral**:\n   \\[\n   \\int_{0}^{\\infty} e^{i x^2} \\, dx\n   \\]\n   This integral can be related to the integral of \\(\\cos(x^2)\\) since the real part of \\(e^{i x^2}\\) is \\(\\cos(x^2)\\).\n\n2. **Use a sector contour**:\n   Consider a contour integral over a sector in the complex plane from \\(0\\) to \\(R\\) on the real axis, then along a circular arc of angle \\(\\pi/4\\) to \\(R e^{i \\pi/4}\\), and back to the origin along the line at \\(\\pi/4\\). By Cauchy's theorem, the integral over this closed contour is zero:\n   \\[\n   \\oint_{C} e^{i z^2} \\, dz = 0\n   \\]\n   where \\(C\\) is the contour described.\n\n3. **Integral over the arc**:\n   As \\(R \\to \\infty\\), the integral over the circular arc vanishes. To see this, parameterize the arc as \\(z = R e^{i \\theta}\\) with \\(\\theta \\in [0, \\pi/4]\\):\n   \\[\n   \\left| \\int_{0}^{\\pi/4} e^{i R^2 e^{2i\\theta}} i R e^{i\\theta} \\, d\\theta \\right| \\leq R \\int_{0}^{\\pi/4} e^{-R^2 \\sin(2\\theta)} \\, d\\theta\n   \\]\n   Using the inequality \\(\\sin(2\\theta) \\geq \\frac{4\\theta}{\\pi}\\) for \\(\\theta \\in [0, \\pi/4]\\):\n   \\[\n   \\int_{0}^{\\pi/4} e^{-R^2 \\sin(2\\theta)} \\, d\\theta \\leq \\int_{0}^{\\pi/4} e^{-\\frac{4R^2 \\theta}{\\pi}} \\, d\\theta\n   \\]\n   This integral evaluates to:\n   \\[\n   \\int_{0}^{\\pi/4} e^{-\\frac{4R^2 \\theta}{\\pi}} \\, d\\theta = \\left[ -\\frac{\\pi}{4R^2} e^{-\\frac{4R^2 \\theta}{\\pi}} \\right]_{0}^{\\pi/4} = \\frac{\\pi}{4R^2} (1 - e^{-R^2})\n   \\]\n   As \\(R \\to \\infty\\), this goes to zero.\n\n4. **Integrals along the real axis and the line at \\(\\pi/4\\)**:\n   The integral over the real axis is:\n   \\[\n   \\int_{0}^{R} e^{i x^2} \\, dx\n   \\]\n   The integral along the line at \\(\\pi/4\\) is parameterized by \\(z = t e^{i \\pi/4}\\) with \\(t \\in [0, R]\\):\n   \\[\n   \\int_{R}^{0} e^{i (t e^{i \\pi/4})^2} e^{i \\pi/4} \\, dt = e^{i \\pi/4} \\int_{R}^{0} e^{-t^2} \\, dt = -e^{i \\pi/4} \\int_{0}^{R} e^{-t^2} \\, dt\n   \\]\n   As \\(R \\to \\infty\\):\n   \\[\n   \\int_{0}^{\\infty} e^{i x^2} \\, dx - e^{i \\pi/4} \\int_{0}^{\\infty} e^{-t^2} \\, dt = 0\n   \\]\n   Therefore:\n   \\[\n   \\int_{0}^{\\infty} e^{i x^2} \\, dx = e^{i \\pi/4} \\int_{0}^{\\infty} e^{-t^2} \\, dt\n   \\]\n\n5. **Gaussian integral**:\n   Use the known result for the Gaussian integral:\n   \\[\n   \\int_{0}^{\\infty} e^{-t^2} \\, dt = \\frac{\\sqrt{\\pi}}{2}\n   \\]\n   Thus:\n   \\[\n   \\int_{0}^{\\infty} e^{i x^2} \\, dx = e^{i \\pi/4} \\frac{\\sqrt{\\pi}}{2}\n   \\]\n\n6. **Extract the real part**:\n   The integral of \\(\\cos(x^2)\\) is the real part of the above result:\n   \\[\n   \\int_{0}^{\\infty} \\cos(x^2) \\, dx = \\text{Re} \\left( e^{i \\pi/4} \\frac{\\sqrt{\\pi}}{2} \\right) = \\frac{\\sqrt{\\pi}}{2} \\cos\\left(\\frac{\\pi}{4}\\right) = \\frac{\\sqrt{\\pi}}{2} \\cdot \\frac{\\sqrt{2}}{2} = \\frac{\\sqrt{2\\pi}}{4}\n   \\]\n\nTherefore, the value of the integral is:\n\\[\n\\boxed{\\frac{\\sqrt{2\\pi}}{4}}\n\\]"}