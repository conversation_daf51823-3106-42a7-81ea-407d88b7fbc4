{"row_number": 2705, "expected_answer": "540", "problem": "In how many ways can 6 distinct computers be distributed to 3 schools such that each school receives at least one computer?", "generated_solution": "<think>\nOkay, so I need to figure out how many ways 6 distinct computers can be distributed to 3 schools, with each school getting at least one computer. Hmm, let's see. \n\nFirst, the problem is about distributing distinct items to distinct groups with each group getting at least one item. That makes me think of the principle of inclusion-exclusion or maybe using Stirling numbers of the second kind. Wait, but since the schools are distinct, maybe we can use the formula for onto functions? \n\nRight, the number of onto functions from a set A to a set B is given by the formula:\n\n$$\n\\sum_{k=0}^{n} (-1)^k \\binom{n}{k} (n - k)^m\n$$\n\nWait, no, actually, the formula for the number of onto functions (surjective functions) from a set of size m to a set of size n is:\n\n$$\n\\sum_{k=0}^{n} (-1)^k \\binom{n}{k} (n - k)^m\n$$\n\nBut here, m is the number of computers (6) and n is the number of schools (3). So substituting the values, it would be:\n\n$$\n\\sum_{k=0}^{3} (-1)^k \\binom{3}{k} (3 - k)^6\n$$\n\nLet me compute that step by step. \n\nFirst, when k=0:\n\n(-1)^0 * C(3,0) * (3-0)^6 = 1 * 1 * 3^6 = 729\n\nk=1:\n\n(-1)^1 * C(3,1) * (3-1)^6 = -1 * 3 * 2^6 = -3 * 64 = -192\n\nk=2:\n\n(-1)^2 * C(3,2) * (3-2)^6 = 1 * 3 * 1^6 = 3 * 1 = 3\n\nk=3:\n\n(-1)^3 * C(3,3) * (3-3)^6 = -1 * 1 * 0^6 = 0\n\nAdding them up: 729 - 192 + 3 + 0 = 729 - 192 is 537, plus 3 is 540.\n\nSo the total number of onto functions is 540. Therefore, the number of ways is 540. \n\nAlternatively, another way to think about this is using Stirling numbers of the second kind, which count the number of ways to partition a set into non-empty subsets. Then, since the schools are distinct, we multiply by 3! (the number of ways to assign the subsets to the schools). Wait, but wait, no. Wait, Stirling numbers of the second kind S(n, k) count the number of ways to partition a set of n elements into k non-empty subsets. Then, since each school is distinct, the number of distributions would be S(6, 3) multiplied by 3!.\n\nSo let me check: S(6, 3) is the Stirling number of the second kind for 6 elements into 3 subsets. Then 3! would be the number of ways to assign these subsets to the 3 schools. Let me compute S(6, 3) first.\n\nI remember that Stirling numbers of the second kind can be calculated using the formula:\n\n$$\nS(n, k) = \\frac{1}{k!} \\sum_{i=0}^{k} (-1)^{k-i} \\binom{k}{i} i^n}\n$$\n\nBut maybe I can look up the value of S(6,3) instead. From my memory or some tables, S(6,3) is 90. Let me verify that with the formula.\n\nUsing the formula:\n\nS(6,3) = (1/3!)[3^6 - 3*2^6 + 3*1^6]\n\nWhich is (1/6)[729 - 192 + 3] = (1/6)(540) = 90. Yes, that's correct.\n\nSo then, multiplying by 3! (which is 6) gives 90 * 6 = 540. So that's the same answer as before. \n\nTherefore, both methods give 540. So the answer should be 540.\n\nWait, just to double-check, maybe I can think of another approach. The total number of ways to distribute 6 distinct computers to 3 schools without any restrictions is 3^6 = 729. Then, subtract the distributions where at least one school gets no computers. \n\nUsing inclusion-exclusion: Let's subtract the cases where one school is empty, then add back the cases where two schools are empty, etc. \n\nNumber of distributions where at least one school is empty: C(3,1)*2^6 - C(3,2)*1^6 + C(3,3)*0^6. \n\nWait, let's calculate:\n\nTotal = 3^6 - C(3,1)*2^6 + C(3,2)*1^6 - C(3,3)*0^6\n\nWhich is 729 - 3*64 + 3*1 - 0 = 729 - 192 + 3 = 540. \n\nSo exactly the same result. \n\nSo all three methods agree. Hence, I can be confident that the answer is 540.\n</think>To determine the number of ways to distribute 6 distinct computers to 3 schools such that each school receives at least one computer, we can use the principle of inclusion-exclusion.\n\n1. **Calculate the total number of unrestricted distributions:**\n   Each computer can go to any of the 3 schools. Therefore, the total number of distributions is:\n   \\[\n   3^6 = 729\n   \\]\n\n2. **Subtract the cases where at least one school is empty:**\n   We need to subtract the cases where at least one school does not receive any computer. There are \\(\\binom{3}{1} = 3\\) ways to choose which one of the schools is empty, and for each of these choices, the remaining 6 computers can be distributed to the other 2 schools in \\(2^6\\) ways. Thus, the number of distributions where at least one school is empty is:\n   \\[\n   \\binom{3}{1} \\cdot 2^6 = 3 \\cdot 64 = 192\n   \\]\n\n3. **Add back the cases where at least two schools are empty:**\n   We have over-subtracted the cases where two schools are empty. There are \\(\\binom{3}{2} = 3\\) ways to choose which two schools are empty, and for each of these choices, the 6 computers must all go to the remaining school. Thus, the number of distributions where at least two schools are empty is:\n   \\[\n   \\binom{3}{2} \\cdot 1^6 = 3 \\cdot 1 = 3\n   \\]\n\n4. **Apply the principle of inclusion-exclusion:**\n   The number of valid distributions where each school gets at least one computer is:\n   \\[\n   3^6 - \\binom{3}{1} \\cdot 2^6 + \\binom{3}{2} \\cdot 1^6 = 729 - 192 + 3 = 540\n   \\]\n\nTherefore, the number of ways to distribute 6 distinct computers to 3 schools such that each school receives at least one computer is:\n\\[\n\\boxed{540}\n\\]"}