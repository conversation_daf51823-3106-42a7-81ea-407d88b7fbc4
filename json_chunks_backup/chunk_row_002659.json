{"row_number": 2659, "expected_answer": "\\(\\frac{1}{2}\\)", "problem": "Find \\[\\lim_{n\\to \\infty} \\sum_{k=1}^n \\sin\\left(\\frac{k}{n^2}\\right) .\\]", "generated_solution": "<think>\nOkay, so I need to find the limit as n approaches infinity of the sum from k=1 to n of sin(k/n²). Hmm, let's see. I remember that for limits involving sums, especially as n gets large, sometimes we can approximate them using integrals or maybe use some known asymptotic expansions. \n\nFirst, let me write down the problem again to make sure I have it right:\n\nlimₙ→∞ Σₖ₌₁ⁿ sin(k/n²)\n\nSo each term in the sum is sin(k divided by n squared). When n is large, n² is very big, so each argument of the sine function is small because k goes up to n, so the maximum value of k/n² would be n/n² = 1/n, which also tends to zero as n approaches infinity. So each term sin(k/n²) is approximately equal to its argument when n is large, since sin(x) ≈ x when x is near 0. Maybe I can use the approximation sin(x) ≈ x - x³/6 + ... for small x, but maybe just the first term x would be sufficient here. Let's try that.\n\nIf I approximate each sin(k/n²) by k/n², then the sum becomes approximately Σₖ₌₁ⁿ (k/n²). That sum would be (1 + 2 + 3 + ... + n)/n². The sum of the first n integers is n(n + 1)/2, so dividing by n² gives (n(n + 1))/(2n²) = (n + 1)/(2n) = (1 + 1/n)/2. As n approaches infinity, 1/n goes to 0, so this would approach 1/2. But wait, this is just the approximation using sin(x) ≈ x. But maybe the higher-order terms contribute something? Hmm.\n\nBut let's check if the error from this approximation becomes negligible as n approaches infinity. The next term in the Taylor series is -x³/6. So the error for each term would be approximately - (k/n²)^3 /6. So the total error in the sum would be approximately -Σₖ₌₁ⁿ (k³)/(6n⁶). Let's compute the magnitude of that. The sum of k³ from k=1 to n is [n(n + 1)/2]^2. So approximately (n²/2)^2 = n⁴/4 for large n. Then the total error is roughly (n⁴/4)/(6n⁶) = 1/(24n²), which tends to zero as n approaches infinity. So the error from the approximation is negligible. Therefore, the limit should be 1/2.\n\nWait, but let me confirm this with another approach. Maybe using the integral test or Riemann sums.\n\nAlternatively, consider that for large n, the sum Σₖ₌₁ⁿ sin(k/n²) can be approximated by an integral. But how? Because the sum is over k from 1 to n, and each term is sin(k/n²). If I let x = k/n², but k goes from 1 to n, so x ranges from 1/n² to n/n² = 1/n. So the interval is from 0 to 1/n, which is getting smaller as n increases. That doesn't seem like a standard Riemann sum. Wait, maybe not. Hmm.\n\nAlternatively, perhaps we can make a substitution. Let me set k = n t, so t = k/n. Then as n approaches infinity, t would range from 0 to 1. But if k goes from 1 to n, then t would go from 1/n to 1. So substituting, the argument inside the sine becomes (n t)/n² = t/n. So sin(t/n). Then the sum becomes Σₖ₌₁ⁿ sin(t/n) where t is k/n. Wait, but the sum is Σₜ=1/n to 1 (in steps of 1/n) of sin(t/n). But that seems like each term is sin(t/n) and the step between t's is 1/n. Hmm. So the sum would be approximately n terms each of size sin(t/n). Wait, but sin(t/n) ≈ t/n for small t/n. Then each term is approximately t/n, so the sum would be Σ t/n * (1/n) ? Wait, no. Wait, if t is k/n, and k goes from 1 to n, then the step between consecutive t's is 1/n. So the sum Σₖ₌₁ⁿ sin(t/n) where t = k/n. But then that's Σ sin(t/n) over t = 1/n, 2/n, ..., 1. So each term is sin(t/n) with t being the variable, but I'm not sure how that helps. Let me see:\n\nIf we approximate the sum as a Riemann sum, the standard Riemann sum is (1/n) Σ f(k/n) from k=1 to n approximates ∫₀¹ f(x) dx. But in our case, the sum is Σ sin(k/n²). If I factor out 1/n, maybe. Wait, let's see:\n\nAlternatively, note that sin(k/n²) can be written as sin((k/n) * (1/n)). Let x = k/n, which ranges from 1/n to 1. Then sin(x/n). So the sum is Σₓ=1/n, 2/n, ..., 1 of sin(x/n). But the sum would be Σ sin(x/n) * Δx where Δx is 1/n. Wait, but if we do that, the sum would be approximating the integral ∫₀¹ sin(x/n) dx. Wait, but n is going to infinity, so x/n goes to 0. Then sin(x/n) ≈ x/n, so the integral becomes ∫₀¹ x/n dx = (1/n)(1²/2 - 0) = 1/(2n) which tends to zero. But this contradicts our previous result of 1/2. So maybe this approach is wrong.\n\nHmm, so this seems conflicting. Wait, maybe I messed up the substitution. Let me think again.\n\nOriginal sum is Σₖ₌₁ⁿ sin(k/n²). Let me factor out 1/n² from the argument. Let’s write sin(k/n²) = sin( (k/n) * (1/n) ). If we set x = k/n, which runs from 1/n to 1, then each term is sin(x * 1/n). So the sum becomes Σₖ₌₁ⁿ sin(x/n) where x = k/n. But the step between consecutive x's is 1/n. Therefore, the sum is Σ sin(x/n) * (1/n) * n, because each term is spaced by 1/n, and we have n terms. Wait, that would be Σ sin(x/n) * (1/n) * n = Σ sin(x/n). But that's just the original sum, so that doesn't help. Alternatively, the sum is Σ sin(x/n) with x from 1/n to 1 in steps of 1/n, so if we think of it as a Riemann sum, perhaps with variable substitution?\n\nAlternatively, let's think of the sum as a Riemann sum with a different scaling. If I consider the sum Σₖ₌₁ⁿ sin(k/n²) = Σₖ₌₁ⁿ sin( (k/n) * 1/n ). Let me set t = k/n², then as n approaches infinity, t ranges from 1/n² to n/n² = 1/n, which approaches 0. But this seems like the sum is over points t that are approaching zero, so maybe not helpful.\n\nAlternatively, perhaps we can use the expansion sin(z) = z - z³/6 + ... for small z. Then, the sum becomes Σₖ₌₁ⁿ [k/n² - (k/n²)^3 /6 + ...]. Then the leading term is Σ k/n² = (n(n + 1))/(2n²) ≈ n²/(2n²) = 1/2. The next term is - (1/6) Σ k³/n⁶. As I calculated before, the sum of k³ is [n(n + 1)/2]^2 ≈ n⁴/4, so this term becomes - (1/6)(n⁴/4)/n⁶ = -1/(24n²) which tends to zero. Therefore, the entire sum approaches 1/2.\n\nAlternatively, another approach: using the formula for the sum of sines. There's a formula for Σₖ₌₁^N sin(a + (k-1)d), but in this case, the argument is k/n², so maybe it's possible to use some trigonometric identities. Let me check.\n\nBut for large n, the arguments k/n² are small, so sin(k/n²) ≈ k/n², so the sum is approximately Σₖ₌₁ⁿ k/n² = (n(n + 1))/2n² ≈ n²/(2n²) = 1/2. Therefore, the limit should be 1/2.\n\nAlternatively, let's see if the sum can be converted into an integral. For example, since each term is sin(k/n²), and k goes up to n, maybe we can write the sum as n² * Σₖ₌₁ⁿ sin(k/n²) * (1/n²). But that would be like a Riemann sum for the integral of sin(x) from 0 to 1/n, which is not the case. Wait, if we set x = k/n², then Δx = 1/n², and the sum becomes Σ sin(x) Δx with x from 1/n² to n/n² = 1/n. So the sum is approximately ∫₀^{1/n} sin(x) dx ≈ ∫₀^{1/n} x dx = [x²/2]₀^{1/n} = 1/(2n²). But multiplying by n² gives 1/2. Wait, no, because the sum is Σ sin(x) Δx, which would approximate the integral. Wait, but the sum is Σ sin(k/n²) = Σ sin(x) * (Δk) where Δk = 1. If we want to write it as a Riemann sum, we need to have Δx. So maybe if we set x = k/n², then Δx = (k+1)/n² - k/n² = 1/n². Then, the sum becomes Σ sin(x) * Δx * n². Because each term is sin(x) * 1/n², and the sum is over n terms. Wait, but if x = k/n², then the sum is Σ sin(x) * 1 = Σ sin(x) * (1/n²) * n². Since Δx = 1/n², the sum is Σ sin(x) * Δx * n² ≈ ∫₀^{1/n} sin(x) dx * n². As n approaches infinity, the integral from 0 to 1/n of sin(x) dx ≈ sin(0) * (1/n) = 0, but that's not helpful. Wait, maybe integrating from 0 to 1/n of sin(x) dx ≈ (1/n) - (1/n)^3/6. Then multiplying by n² gives n²*(1/n - 1/(6n³)) = n - 1/(6n), which goes to infinity. That can't be right. So this approach is conflicting with the previous result. Hmm, maybe this method isn't correct.\n\nWait, let's check my mistake here. If x = k/n², then k ranges from 1 to n, so x ranges from 1/n² to n/n² = 1/n. So the upper limit of integration is 1/n, not 1. As n approaches infinity, the upper limit approaches 0. Therefore, ∫₀^{1/n} sin(x) dx ≈ sin(0) * (1/n) + ... but actually, ∫₀^{1/n} sin(x) dx = -cos(1/n) + cos(0) = 1 - cos(1/n). Using the approximation cos(z) ≈ 1 - z²/2 for small z, so 1 - cos(1/n) ≈ 1 - (1 - 1/(2n²)) = 1/(2n²). Then, multiplying by n² gives n²*(1/(2n²)) = 1/2. Ah! So the sum, when written as a Riemann sum with Δx = 1/n², is equal to n² * ∫₀^{1/n} sin(x) dx ≈ n²*(1/(2n²)) = 1/2. So this also gives 1/2. Therefore, this confirms the previous result. So even though the integral is over a shrinking interval, when scaled appropriately, it gives the same answer. \n\nTherefore, both methods agree that the limit is 1/2. To make sure, perhaps I can test with a concrete example. Let's take n = 1000. Then, the sum would be Σₖ=1^1000 sin(k/(1000)^2). Each term sin(k/1000000) ≈ k/1000000 - (k/1000000)^3/6. The sum of k from 1 to 1000 is (1000)(1001)/2 ≈ 500500. Then, sum ≈ 500500 / 1e6 - (sum of k³)/(6e18). Sum of k³ is [1000^2 * 1001^2]/4 ≈ (1e6 * 1.002e6)/4 ≈ 2.506e11 / 4 ≈ 6.265e10. So the second term is 6.265e10 / 6e18 ≈ 1.04e-8, which is negligible. So the sum is approximately 500500 / 1e6 ≈ 0.5005, which is close to 0.5. So for n=1000, the sum is approximately 0.5005, which would approach 0.5 as n increases. Therefore, the limit is indeed 1/2.\n\nAnother way to see this is to use the expansion sin(x) = x - x^3/6 + ..., so the sum becomes Σₖ=1ⁿ [k/n² - (k^3)/(6n^6) + ...]. The first term's sum is (n(n + 1))/(2n²) = (n + 1)/(2n) → 1/2. The next term is -1/(6n^6) * [n²(n + 1)^2]/4 ≈ -n^4/(24n^6) = -1/(24n²) → 0. So the higher-order terms vanish, leaving the limit as 1/2.\n\nAlternatively, using the Stolz–Cesàro theorem. Maybe not necessary here, but let's think. The Stolz–Cesàro theorem is used when dealing with limits of sequences in the form a_n / b_n, where b_n is monotonically increasing and unbounded. But in this case, the sum S_n = Σₖ=1ⁿ sin(k/n²) is not immediately in a form where I can apply that theorem directly. Perhaps not the best approach here.\n\nSo, all methods point to the limit being 1/2. I think this is solid.\n</think>To find the limit \\(\\lim_{n \\to \\infty} \\sum_{k=1}^n \\sin\\left(\\frac{k}{n^2}\\right)\\), we start by analyzing the behavior of the sine function for small arguments. For large \\(n\\), the argument \\(\\frac{k}{n^2}\\) is very small, so we can use the approximation \\(\\sin(x) \\approx x\\) for small \\(x\\).\n\n1. **Approximation Using Small Angle Approximation:**\n   \\[\n   \\sin\\left(\\frac{k}{n^2}\\right) \\approx \\frac{k}{n^2}.\n   \\]\n   Therefore, the sum can be approximated as:\n   \\[\n   \\sum_{k=1}^n \\sin\\left(\\frac{k}{n^2}\\right) \\approx \\sum_{k=1}^n \\frac{k}{n^2}.\n   \\]\n\n2. **Summing the Approximated Series:**\n   The sum of the first \\(n\\) integers is given by:\n   \\[\n   \\sum_{k=1}^n k = \\frac{n(n+1)}{2}.\n   \\]\n   Substituting this into the approximated sum, we get:\n   \\[\n   \\sum_{k=1}^n \\frac{k}{n^2} = \\frac{1}{n^2} \\cdot \\frac{n(n+1)}{2} = \\frac{n+1}{2n}.\n   \\]\n\n3. **Taking the Limit:**\n   As \\(n \\to \\infty\\), the expression \\(\\frac{n+1}{2n}\\) approaches:\n   \\[\n   \\lim_{n \\to \\infty} \\frac{n+1}{2n} = \\lim_{n \\to \\infty} \\left( \\frac{n}{2n} + \\frac{1}{2n} \\right) = \\frac{1}{2} + 0 = \\frac{1}{2}.\n   \\]\n\n4. **Ensuring Higher-Order Terms Vanish:**\n   To ensure that higher-order terms do not affect the limit, we consider the next term in the Taylor expansion of \\(\\sin(x)\\), which is \\(-\\frac{x^3}{6}\\). The error term for each \\(k\\) is:\n   \\[\n   \\sin\\left(\\frac{k}{n^2}\\right) - \\frac{k}{n^2} \\approx -\\frac{\\left(\\frac{k}{n^2}\\right)^3}{6} = -\\frac{k^3}{6n^6}.\n   \\]\n   Summing these error terms, we get:\n   \\[\n   \\sum_{k=1}^n \\left( -\\frac{k^3}{6n^6} \\right) = -\\frac{1}{6n^6} \\sum_{k=1}^n k^3.\n   \\]\n   The sum of the first \\(n\\) cubes is:\n   \\[\n   \\sum_{k=1}^n k^3 = \\left( \\frac{n(n+1)}{2} \\right)^2 \\approx \\frac{n^4}{4}.\n   \\]\n   Therefore, the error term becomes:\n   \\[\n   -\\frac{1}{6n^6} \\cdot \\frac{n^4}{4} = -\\frac{n^4}{24n^6} = -\\frac{1}{24n^2}.\n   \\]\n   As \\(n \\to \\infty\\), \\(-\\frac{1}{24n^2} \\to 0\\), confirming that the higher-order terms do not affect the limit.\n\nThus, the limit is:\n\\[\n\\lim_{n \\to \\infty} \\sum_{k=1}^n \\sin\\left(\\frac{k}{n^2}\\right) = \\frac{1}{2}.\n\\]\n\n\\[\n\\boxed{\\frac{1}{2}}\n\\]"}