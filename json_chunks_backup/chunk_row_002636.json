{"row_number": 2636, "expected_answer": "2", "problem": "Evaluate explicitly the line integral \\(\\int (y \\, \\textup{d}x + x \\, \\textup{d}y + \\textup{d}z)\\) along (i) the straight line path joining the origin to \\(x = y = z = 1\\), and (ii) the parabolic path given parametrically by \\(x = t, y = t, z = t^2\\) with \\(0 \\le t \\le 1\\).", "generated_solution": "<think>\nOkay, let's see. I need to evaluate this line integral ∫(y dx + x dy + dz) along two different paths from the origin to the point (1,1,1). The first path is a straight line, and the second is a parabolic path defined by x = t, y = t, z = t² from t=0 to t=1. Hmm. Alright, let's tackle part (i) first.\n\nSo, for a line integral, I remember that the general approach is to parameterize the path, express dx, dy, dz in terms of a parameter (like t), and then integrate over t from the starting to the ending point. Let's confirm that.\n\nFor part (i), the straight line from (0,0,0) to (1,1,1). The parametric equations for a straight line can be written as x(t) = t, y(t) = t, z(t) = t, where t ranges from 0 to 1. That makes sense because when t=0, we're at (0,0,0), and at t=1, we reach (1,1,1). \n\nNow, compute dx/dt, dy/dt, dz/dt. Since x = t, dx = dt, so dx/dt = 1, so dx = dt. Similarly, dy = dt and dz = dt. Now, substitute x, y, z, dx, dy, dz into the integral.\n\nThe integral becomes ∫[y dx + x dy + dz] from t=0 to t=1. Substituting y = t, x = t, dx = dt, dy = dt, dz = dt. So:\n\n∫[ t*dt + t*dt + dt ] from 0 to 1. That's ∫( t + t + 1 ) dt from 0 to1. Simplify inside the integral: 2t + 1.\n\nIntegrating term by term: integral of 2t dt is t², integral of 1 dt is t. So the integral becomes t² + t evaluated from 0 to 1. Plugging in 1: 1 + 1 = 2. Plugging in 0: 0 + 0 = 0. So the result for part (i) is 2 - 0 = 2. So the answer for (i) is 2.\n\nWait, but let me check if I did everything correctly. Let's see. The parameterization is straightforward. The substitutions seem right. The integral becomes 2t + 1 dt, which integrates to t² + t. Between 0 and1, that's 2. Seems okay.\n\nNow part (ii): the parabolic path given by x = t, y = t, z = t², where t goes from 0 to1. So in this case, x and y are both t, and z is t squared. Let me check that when t=1, we get x=1, y=1, z=1, which is correct. \n\nFirst, compute dx, dy, dz. Since x = t, dx/dt = 1 ⇒ dx = dt. Similarly, y = t ⇒ dy = dt. z = t² ⇒ dz/dt = 2t ⇒ dz = 2t dt. \n\nNow substitute into the integral ∫(y dx + x dy + dz). Here, y = t, x = t. So substituting:\n\nFirst term: y dx = t * dx = t * dt.\n\nSecond term: x dy = t * dy = t * dt.\n\nThird term: dz = 2t dt.\n\nSo the integral becomes ∫[ t dt + t dt + 2t dt ] from t=0 to t=1. Simplify the integrand: t + t + 2t = 4t. So the integral is ∫4t dt from 0 to1.\n\nIntegrate 4t: 2t². Evaluated from 0 to1 gives 2(1)² - 2(0)² = 2 - 0 = 2. So the result is 2.\n\nWait, that's the same as part (i)? But the paths are different. Is that possible? Hmm. Let me verify again.\n\nWait, maybe the vector field is conservative? Let me check if the differential form y dx + x dy + dz is exact. That is, if there exists a function f such that df = y dx + x dy + dz.\n\nTo check if it's exact, we can check if the partial derivatives satisfy the conditions. Let's see.\n\nLet’s denote the components as follows: the differential form is M dx + N dy + P dz, where M = y, N = x, P = 1.\n\nFor the differential form to be exact, the curl should be zero. But since we are in 3D, the necessary conditions are that the curl of the vector field (M, N, P) is zero.\n\nCompute the curl: ∇ × F = (∂P/∂y - ∂N/∂z, ∂M/∂z - ∂P/∂x, ∂N/∂x - ∂M/∂y).\n\nCalculating each component:\n\nFirst component: ∂P/∂y - ∂N/∂z = ∂(1)/∂y - ∂x/∂z = 0 - 0 = 0.\n\nSecond component: ∂M/∂z - ∂P/∂x = ∂y/∂z - ∂1/∂x = 0 - 0 = 0.\n\nThird component: ∂N/∂x - ∂M/∂y = ∂x/∂x - ∂y/∂y = 1 - 1 = 0.\n\nSo the curl is zero, which implies that the vector field is conservative. Therefore, the line integral should be path-independent, and thus the integrals over both paths should be equal. Hence, both integrals are 2. That explains why both answers are the same.\n\nSo, both integrals evaluate to 2. Therefore, the answer for both parts is 2.\n\nBut let me make sure I didn't make a mistake in substituting the parabolic path. Let's go through part (ii) again step by step.\n\nGiven x = t, y = t, z = t². Then:\n\ndx/dt = 1 ⇒ dx = dt.\n\ndy/dt = 1 ⇒ dy = dt.\n\ndz/dt = 2t ⇒ dz = 2t dt.\n\nNow, substitute into y dx + x dy + dz:\n\ny dx = t * dt.\n\nx dy = t * dt.\n\ndz = 2t dt.\n\nSo the integrand becomes t dt + t dt + 2t dt = 4t dt.\n\nIntegrate from 0 to1: ∫4t dt = 4*(1/2)t² from 0 to1 = 4*(1/2)*(1 -0) = 2. So yes, that checks out.\n\nTherefore, both integrals are equal to 2. Since the vector field is conservative, this makes sense. Therefore, the answers are both 2.\n\n**Final Answer**\n(i) \\boxed{2}  \n(ii) \\boxed{2}\n</think>To evaluate the line integral \\(\\int (y \\, \\textup{d}x + x \\, \\textup{d}y + \\textup{d}z)\\) along the given paths, we proceed as follows:\n\n### (i) Straight Line Path from the Origin to \\(x = y = z = 1\\):\n\n1. **Parameterize the path**: \n   \\[\n   x(t) = t, \\quad y(t) = t, \\quad z(t) = t \\quad \\text{for} \\quad 0 \\le t \\le 1\n   \\]\n\n2. **Compute differentials**:\n   \\[\n   \\textup{d}x = \\textup{d}t, \\quad \\textup{d}y = \\textup{d}t, \\quad \\textup{d}z = \\textup{d}t\n   \\]\n\n3. **Substitute into the integral**:\n   \\[\n   \\int_0^1 (y \\, \\textup{d}x + x \\, \\textup{d}y + \\textup{d}z) = \\int_0^1 (t \\, \\textup{d}t + t \\, \\textup{d}t + \\textup{d}t) = \\int_0^1 (2t + 1) \\, \\textup{d}t\n   \\]\n\n4. **Integrate**:\n   \\[\n   \\int_0^1 (2t + 1) \\, \\textup{d}t = \\left[ t^2 + t \\right]_0^1 = (1^2 + 1) - (0^2 + 0) = 2\n   \\]\n\n### (ii) Parabolic Path Given by \\(x = t\\), \\(y = t\\), \\(z = t^2\\) for \\(0 \\le t \\le 1\\):\n\n1. **Parameterize the path**: \n   \\[\n   x(t) = t, \\quad y(t) = t, \\quad z(t) = t^2 \\quad \\text{for} \\quad 0 \\le t \\le 1\n   \\]\n\n2. **Compute differentials**:\n   \\[\n   \\textup{d}x = \\textup{d}t, \\quad \\textup{d}y = \\textup{d}t, \\quad \\textup{d}z = 2t \\, \\textup{d}t\n   \\]\n\n3. **Substitute into the integral**:\n   \\[\n   \\int_0^1 (y \\, \\textup{d}x + x \\, \\textup{d}y + \\textup{d}z) = \\int_0^1 (t \\, \\textup{d}t + t \\, \\textup{d}t + 2t \\, \\textup{d}t) = \\int_0^1 4t \\, \\textup{d}t\n   \\]\n\n4. **Integrate**:\n   \\[\n   \\int_0^1 4t \\, \\textup{d}t = \\left[ 2t^2 \\right]_0^1 = 2(1^2) - 2(0^2) = 2\n   \\]\n\nSince the vector field is conservative, the line integral is path-independent, and both integrals evaluate to 2.\n\n### Final Answer:\n\\[\n\\boxed{2}\n\\]"}