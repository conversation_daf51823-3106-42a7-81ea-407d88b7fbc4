{"row_number": 2573, "expected_answer": "109", "problem": "For each positive integer \\( n \\), let \\( f(n) = \\sum_{k = 1}^{100} \\lfloor \\log_{10} (kn) \\rfloor \\). Find the largest value of \\( n \\) for which \\( f(n) \\le 300 \\).", "generated_solution": "<think>\nOkay, let's tackle this problem. So, we need to find the largest positive integer n such that f(n) ≤ 300, where f(n) is the sum from k=1 to 100 of the floor of log base 10 of (kn). Hmm. Alright, let's break this down step by step.\n\nFirst, I know that the floor of log10(x) gives the number of digits of x minus 1. For example, log10(100) is 2, so floor(log10(100)) is 2, which is 3 digits minus 1. So, floor(log10(kn)) would be the number of digits of (kn) minus 1. Therefore, the sum f(n) is adding up (number of digits of kn - 1) for each k from 1 to 100. So, f(n) = sum_{k=1}^{100} (number of digits of kn - 1) = sum_{k=1}^{100} number of digits of kn - 100. Therefore, f(n) + 100 = sum of the number of digits of kn from k=1 to 100. But maybe it's easier to think directly in terms of floor(log10(kn)).\n\nWait, let me verify. For a number x, the number of digits d is given by d = floor(log10(x)) + 1. So, floor(log10(x)) = d - 1. So yes, sum_{k=1}^{100} floor(log10(kn)) = sum_{k=1}^{100} (digits(kn) - 1) = [sum digits(kn)] - 100. Therefore, if f(n) ≤ 300, then [sum digits(kn)] - 100 ≤ 300 => sum digits(kn) ≤ 400. Hmm, maybe that's a helpful way to think about it. The total number of digits across all products kn for k from 1 to 100 must be at most 400. But I'm not sure if that helps yet.\n\nAlternatively, let's recall that floor(log10(kn)) = m - 1 if kn has m digits. So, the floor of log10(kn) is the exponent when kn is written in scientific notation, i.e., kn = a * 10^m where 1 ≤ a < 10, then floor(log10(kn)) = m. Wait, no. Wait, log10(kn) is the exponent m such that 10^m ≤ kn < 10^(m+1). Therefore, floor(log10(kn)) is m. So, if kn is between 10^m and 10^(m+1), then floor(log10(kn)) is m. Therefore, floor(log10(kn)) is the exponent m where 10^m ≤ kn < 10^(m+1). So, for each k from 1 to 100, we can compute m_k such that 10^m_k ≤ kn < 10^{m_k + 1}, and then sum all m_k.\n\nSo, f(n) is the sum of m_k over k from 1 to 100. Our goal is to find the largest n such that this sum is ≤ 300.\n\nLet me see. So, for each k, kn determines m_k. Therefore, if we can find for each k the m_k such that 10^{m_k} ≤ kn < 10^{m_k + 1}, then sum all m_k. And the total should be ≤ 300.\n\nSince we need to maximize n, we need to find the largest n where the sum of m_k from k=1 to 100 is ≤ 300. Hmm. So, perhaps n is such that for some k, kn crosses into the next order of magnitude. The larger n is, the more likely kn is to be a larger number, hence with a larger m_k.\n\nSo, the idea is that as n increases, each term floor(log10(kn)) might increase for some k. Therefore, f(n) is a non-decreasing function of n. Hence, there is a maximum n where f(n) ≤ 300, and beyond that, f(n) exceeds 300.\n\nSo, we need to find the threshold n where f(n) just starts to exceed 300, then take the previous n.\n\nGiven that, perhaps we can model the problem by determining for each k, the values of n where floor(log10(kn)) increases by 1. That is, the points where kn crosses a power of 10. For example, when kn crosses 10^m, then floor(log10(kn)) increases from m-1 to m.\n\nTherefore, for each k, the critical n where floor(log10(kn)) increases is at n = 10^m / k, for integer m. So, each k has critical points at n = 10^m / k. Therefore, for each k, we can determine how many times floor(log10(kn)) increments as n increases.\n\nBut since n is an integer, we need to adjust that. For example, if n is such that kn is just over 10^m, then floor(log10(kn)) is m. If kn is just under 10^{m+1}, floor(log10(kn)) is still m. So, for each k, the value of floor(log10(kn)) increases by 1 each time n crosses 10^m / k for some m. Therefore, the critical n values for each k are 10^m / k, for m starting from 0 upwards.\n\nGiven that, perhaps we can model the total sum f(n) as being the sum over k of floor(log10(kn)).\n\nAlternatively, since kn can be written as 10^{log10(kn)}, floor(log10(kn)) is the integer part of log10(kn). Therefore, floor(log10(kn)) = floor(log10(k) + log10(n)).\n\nWait, log10(kn) = log10(k) + log10(n). Therefore, floor(log10(kn)) = floor(log10(k) + log10(n)).\n\nTherefore, for each k, floor(log10(kn)) is equal to floor(log10(k) + log10(n)). Therefore, f(n) = sum_{k=1}^{100} floor(log10(k) + log10(n)).\n\nHmm, that seems useful. So, the sum is over k=1 to 100 of floor(log10(k) + log10(n)). Let's denote log10(n) as t. Then, f(n) = sum_{k=1}^{100} floor(log10(k) + t). So, the problem reduces to finding the maximum t (and hence n, since n = 10^t) such that the sum of floor(log10(k) + t) from k=1 to 100 is ≤ 300.\n\nWait, but n must be an integer, so t is log10(n), and n is an integer, so t can take values such that 10^t is integer. But t doesn't have to be integer. Wait, but log10(n) is t, so n = 10^t. But unless n is a power of 10, t is not an integer. Hmm, perhaps my substitution is complicating things.\n\nWait, actually, n is a positive integer, so t = log10(n) is a real number. For example, if n = 2, then t ≈ 0.3010. If n = 10, t = 1. So, the value of floor(log10(k) + t) for each k depends on the fractional parts of log10(k) + t. Hmm. Wait, maybe this can be approached by considering for each k the critical values of t where log10(k) + t crosses an integer threshold. That is, when t is such that log10(k) + t = m, where m is integer. Then, for t < m - log10(k), floor(log10(k) + t) = m - 1. For t ≥ m - log10(k), floor(log10(k) + t) = m.\n\nTherefore, for each k, the critical t where floor(log10(k) + t) increases by 1 is at t = m - log10(k). Therefore, for each k, these critical points occur at t values of m - log10(k), for integer m.\n\nTherefore, the sum f(n) is a step function that increases by 1 at each t = m - log10(k) for some k. Therefore, the problem reduces to finding the maximum t such that the sum of floor(log10(k) + t) from k=1 to 100 is ≤ 300. Then, n is the floor(10^t) or ceiling(10^t - 1) or something similar? Hmm.\n\nBut t is log10(n), so n = 10^t. But n must be an integer, so t is in [log10(n), log10(n+1)). Therefore, as t increases, n increases by 1 each time t crosses log10(n+1).\n\nAlternatively, perhaps this approach is too abstract. Maybe we can model n as a variable and find for each k the interval of n where floor(log10(kn)) is constant. For each k, floor(log10(kn)) = m if and only if 10^m ≤ kn < 10^{m+1} => 10^m / k ≤ n < 10^{m+1}/k.\n\nTherefore, for each k, the function floor(log10(kn)) is a step function that increases by 1 at n = 10^m / k for integer m. Therefore, each time n crosses 10^m / k, floor(log10(kn)) increases by 1.\n\nTherefore, if we can compute for each k, the number of times m where floor(log10(kn)) is m for n up to some value, and sum over k, we can get f(n). But since n is a single variable, this might be complicated. Perhaps another approach is needed.\n\nAlternatively, for a given n, compute for each k, the value of floor(log10(kn)), then sum them up and check if it's ≤ 300. Then perform a binary search on n to find the maximum n where this holds. Since n can be up to, say, 10^something, but 10^m/k where k=1 would be 10^m. But since k ranges up to 100, the critical n for k=1 is 10^m, so n could be up to 10^m. But f(n) would be sum_{k=1}^{100} floor(log10(kn)). Let's think, for n = 1, kn is k, so floor(log10(k)) is 0 for k=1-9, 1 for 10-99, 2 for 100. Therefore, f(1) = 0*9 + 1*90 + 2*1 = 0 + 90 + 2 = 92. Then as n increases, f(n) increases. So when n increases to 10, let's compute f(10):\n\nkn ranges from 10*1=10 to 10*100=1000. So, floor(log10(10)) = 1, floor(log10(20))=1, ..., floor(log10(90))=1, floor(log10(100))=2, floor(log10(110))=2, ..., floor(log10(1000))=3.\n\nWait, for n=10, kn ranges from 10 to 1000. The numbers from 10 to 99: floor(log10(kn))=1 (since 10 ≤ kn < 100), which is kn=10 to 99. Then kn=100 to 999: floor(log10(kn))=2, and kn=1000: floor(log10(kn))=3.\n\nWait, when n=10, k ranges from 1 to 100, so kn=10,20,...,100,110,...,1000. So, how many k's give kn between 10 and 99? Let's see. For kn ≥10, so k ≥1 (since n=10). For kn < 100, k <10. So, k=1 to 9, kn=10 to 90. So kn=10 to 90 (increment by 10) gives numbers with two digits, but wait, kn=10 is 10, which has two digits, log10(10)=1, floor(log10(10))=1. Similarly, kn=99 is 990, which is three digits? Wait, no. Wait, n=10, so kn=10*k. For k=1, kn=10; k=9, kn=90; k=10, kn=100. So kn=10 to 90 (for k=1 to 9) would be 10,20,...,90. Each of these is two-digit numbers, so floor(log10(kn))=1. For k=10, kn=100, which is three digits, floor(log10(100))=2. For k=11 to 99, kn=110 to 990, all three-digit numbers, floor(log10(kn))=2. For k=100, kn=1000, which is four digits, floor(log10(1000))=3. So, f(10) = 9*1 (k=1-9) + 90*2 (k=10-99) + 1*3 (k=100) = 9 + 180 + 3 = 192. Hmm, so f(10)=192, which is higher than f(1)=92. Then, for n=100, let's compute f(100):\n\nkn=100*1=100 up to 100*100=10,000. Numbers from 100 to 999: floor(log10(kn))=2 (three digits), 1000 to 9999: floor(log10(kn))=3 (four digits), 10,000: floor(log10(kn))=4 (five digits). For n=100, k=1 gives 100 (three digits), k=10 gives 1000 (four digits), k=100 gives 10,000 (five digits). So, let's compute:\n\nHow many k's give kn between 100 and 999? kn=100 to 999. Since n=100, k=1: 100, k=2:200,...,k=9:900, then k=10:1000. Wait, so k=1 to 9: kn=100 to 900 (three digits), floor(log10(kn))=2. Then k=10 to 99: kn=1000 to 9900 (four digits), floor(log10(kn))=3. k=100: 10,000 (five digits), floor(log10(kn))=4. Therefore, f(100)=9*2 + 90*3 + 1*4 = 18 + 270 + 4 = 292. So f(100)=292. Which is still below 300.\n\nThen, let's check n=100, f(n)=292. So we need to go higher. Let's try n=120. Maybe?\n\nBut maybe a better approach is to model the problem with binary search. Since we need the maximum n with f(n) ≤ 300, and f(n) is non-decreasing as n increases, we can set up a binary search between lower and upper bounds.\n\nWe saw that n=100 gives f(n)=292. Let's check n=1000. Then, kn=1000 to 100,000. Then floor(log10(kn)) for k=1: 1000 (4 digits, floor=3), k=2:2000 (floor=3), ..., k=9:9000 (floor=3), k=10:10,000 (floor=4), ..., k=99:99,000 (floor=4), k=100:100,000 (floor=5). So f(n)=9*3 + 90*4 + 1*5=27+360+5=392, which is over 300. So f(1000)=392>300. So somewhere between n=100 and 1000.\n\nWait, that seems too big a range. Wait, maybe n=316 (since 10^2.5≈316) but let's see. Alternatively, maybe check n=200. For n=200, kn ranges from 200 to 20,000. So:\n\nk=1:200 (floor=2)\nk=2:400 (floor=2)\n...\nk=5:1000 (floor=3)\nWait, let's compute f(n) for n=200.\n\nFor k=1: 200, floor(log10(200))=2 (since 10^2=100 ≤ 200 < 10^3=1000). Similarly, k=2: 400, floor=2; k=3:600, floor=2; k=4:800, floor=2; k=5:1000, floor=3. So:\n\nFor k=1-4: floor=2 (4 terms)\nk=5: floor=3\nk=6-9: kn=1200, 1400, 1600, 1800, all ≥1000, so floor=3 (4 terms)\nk=10:2000, floor=3\n...\nWait, let's compute more carefully.\n\nFor n=200, kn=200*k.\n\nWhen does kn cross into the next order of magnitude?\n\nFor k=1, kn=200 (3 digits, floor=2)\nk=5, kn=1000 (4 digits, floor=3)\nk=10, kn=2000 (floor=3)\nk=50, kn=10,000 (floor=4)\nk=100, kn=20,000 (floor=4)\n\nSo, let's find how many k's give kn in each range:\n\nkn between 100 and 999 (floor=2): but kn=200,400,600,800. Wait, 200 is already above 100, but 200 is 3 digits, floor(log10(200))=2. But for kn=200, the floor is 2. For kn=1000, floor=3. So, for kn=200*k, how many k's give kn in [100, 999]?\n\nkn=200*k ≥100 => k ≥1 (since 200*1=200)\nkn=200*k <1000 => k <5 (since 200*5=1000). So k=1,2,3,4. So 4 values. Each of these gives floor=2.\n\nThen, kn=1000 to 9999 (floor=3):\n\nkn=1000*k, but wait, kn=200*k. So, kn=200*k must be in [1000,9999]. So k must be from 5 to floor(9999/200)=49.95, so k=5 to 49. That's 45 terms. Each floor=3.\n\nThen kn=10,000 to 99,999 (floor=4):\n\nkn=200*k from 10,000 to 99,999. So k=50 to 499.95, but since k only goes up to 100, k=50 to 100. So 51 terms. Each floor=4.\n\nk=50: kn=10,000 (floor=4)\n...\nk=100: kn=20,000 (floor=4)\n\nSo, total k=50-100: 51 terms.\n\nTherefore, f(200) = (4*2) + (45*3) + (51*4) = 8 + 135 + 204 = 347. Which is more than 300. So f(200)=347. So, somewhere between n=100 and 200.\n\nAt n=100, f(n)=292; n=200, f(n)=347. So, the desired n is between 100 and 200.\n\nLet me try n=150. Then, kn=150, 300, ..., 15000.\n\nCompute f(150):\n\nFind the floor(log10(150k)) for k=1 to 100.\n\nFirst, for kn=150,300,...,15000.\n\nFind how many k's give kn in [100, 999], [1000,9999], [10000, ...].\n\nFirst, kn ≥100: All k ≥1, since 150*1=150 ≥100.\n\nFor kn <1000: k <1000/150 ≈6.666. So k=1 to 6.\n\nWait, 150*6=900, which is less than 1000. 150*7=1050, which is over 1000. So k=1-6: kn=150,300,450,600,750,900. All of these are three digits (floor=2). Then, k=7-66: kn=1050 to 9900 (since 150*66=9900). These are four digits, so floor=3. Then k=67-100: kn=150*67=10050 up to 150*100=15000. These are five digits (10050 is five digits), floor=4.\n\nWait, 150*66=9900 (four digits), 150*67=10050 (five digits). So, for k=67 to 100: 34 terms (since 100-66=34).\n\nTherefore, f(150)= (6*2) + (60*3) + (34*4). Let's compute: 12 + 180 + 136 = 12+180=192, 192+136=328. So f(150)=328, which is over 300. So, the desired n is between 100 and 150.\n\nAt n=100, f(n)=292; n=150, f(n)=328. So we need to go lower than 150. Let's try n=125.\n\nCompute f(125):\n\nkn=125, 250, ...,125*100=12500.\n\nkn=125k.\n\nFind the ranges:\n\nkn <1000: k <1000/125=8. So k=1-7: kn=125,250,375,500,625,750,875 (seven terms), floor=2.\n\nkn >=1000 and <10000: k=8-79. 125*8=1000; 125*79=9875 (four digits). 79-8+1=72 terms. Wait, 79-8+1=72 terms. Then, kn=1000-9875, floor=3.\n\nkn >=10000: k=80-100: 125*80=10,000; 125*100=12,500. So k=80-100:21 terms, floor=4.\n\nSo, f(125)=7*2 +72*3 +21*4 =14 +216 +84=314. So f(125)=314, which is still over 300.\n\nSo, need to go lower. Next, let's try n=120.\n\nkn=120k.\n\nFor n=120:\n\nkn=120,240,...,12000.\n\nkn <1000: k <1000/120≈8.333. So k=1-8. kn=120,240,...,960 (eight terms). floor=2.\n\nkn >=1000 and <10000: k=9-83. 120*9=1080; 120*83=9960. 83-9+1=75 terms. floor=3.\n\nkn >=10000: k=84-100. 120*84=10,080; k=84-100:17 terms. floor=4.\n\nSo, f(120)=8*2 +75*3 +17*4=16 +225 +68=309. Still over 300. Close, though.\n\nNow, n=115.\n\nkn=115,230,...,115*100=11500.\n\nkn <1000: k <1000/115≈8.695. So k=1-8. 115*8=920. So eight terms, floor=2.\n\nkn >=1000 and <10000: k=9-86. 115*9=1035; 115*86=9890. 86-9+1=78 terms. floor=3.\n\nkn >=10000: k=87-100. 115*87=10,005. 100-87+1=14 terms. floor=4.\n\nThus, f(115)=8*2 +78*3 +14*4=16 +234 +56=306. Still over 300.\n\nHmm, need to go lower. Try n=110.\n\nkn=110,220,...,11000.\n\nkn <1000: k <1000/110≈9.09. So k=1-9. kn=110,220,...,990. Nine terms. floor=2.\n\nkn >=1000 and <10000: k=10-90. 110*10=1100; 110*90=9900. 90-10+1=81 terms. floor=3.\n\nkn >=10000: k=91-100. 110*91=10,010. 10 terms. floor=4.\n\nThus, f(110)=9*2 +81*3 +10*4=18 +243 +40=301. Just over 300. So, f(110)=301.\n\nWe need f(n) ≤300. So n must be less than 110. Let's check n=109.\n\nn=109.\n\nkn=109,218,...,10900.\n\nCompute kn ranges.\n\nkn <1000: k <1000/109≈9.174. So k=1-9. kn=109,218,...,981. Nine terms. Each floor(log10(kn))=2.\n\nkn >=1000 and <10000: k=10-91. 109*10=1090; 109*91=9919. 91-10+1=82 terms. floor=3.\n\nkn >=10000: k=92-100. 109*92=10,028. 9 terms. floor=4.\n\nTherefore, f(109)=9*2 +82*3 +9*4=18 +246 +36=300. Exactly 300. So, n=109 gives f(n)=300.\n\nBut we need to check if n=109 is the maximum n where f(n) ≤300. Let's check n=110 again, which gives f(n)=301, which is over. Therefore, n=109 is the maximum. Therefore, the answer is 109.\n\nWait, but let's confirm this with another check. For n=109, compute f(n)=sum_{k=1}^{100} floor(log10(109k)).\n\nAs above, k=1-9: kn=109,218,...,981. These are three-digit numbers. So floor(log10(kn))=2. 9 terms.\n\nk=10-91: kn=1090 to 9919. Four-digit numbers, floor=3. 82 terms.\n\nk=92-100: kn=10,028 to 10,900. Five-digit numbers, floor=4. 9 terms.\n\nSo sum=9*2 +82*3 +9*4=18 +246 +36=300. Correct.\n\nIf we check n=110, k=1-9: kn=110,220,...,990. All three-digit, floor=2. 9 terms. k=10-90: kn=1100 to 9900, four-digit, floor=3. 81 terms. k=91-100: kn=10,010 to 11,000. Five-digit, floor=4. 10 terms. Sum=9*2 +81*3 +10*4=18 +243 +40=301>300.\n\nTherefore, the maximum n where f(n) ≤300 is 109.\n\nBut wait, wait. The problem says \"Find the largest value of n for which f(n) ≤ 300.\" So n=109 is the answer.\n\nBut let me just confirm for n=109. Maybe I made a mistake in the count.\n\nFor n=109:\n\nNumber of k=1-9: kn=109*1=109 (3 digits), up to 109*9=981 (3 digits). All three digits, so floor=2. That's 9 terms.\n\nFor k=10: 109*10=1090 (4 digits) up to k=91:109*91=9919 (4 digits). 91-10+1=82 terms, floor=3.\n\nFor k=92:109*92=10,028 (5 digits) up to k=100:109*100=10,900 (5 digits). 100-92+1=9 terms, floor=4.\n\nSo total sum:9*2 +82*3 +9*4=18 +246 +36=300. Correct.\n\nIf n=109.5 (non-integer), then kn would be non-integer, but n must be integer. Therefore, n=109 is the largest integer where the sum is 300. Increasing n to 110 makes the sum 301, which exceeds 300.\n\nTherefore, the answer is 109.\n\n**Final Answer**\n\\boxed{109}\n</think>To solve the problem of finding the largest positive integer \\( n \\) such that \\( f(n) \\leq 300 \\), where \\( f(n) = \\sum_{k=1}^{100} \\lfloor \\log_{10}(kn) \\rfloor \\), we need to carefully analyze the behavior of \\( \\lfloor \\log_{10}(kn) \\rfloor \\).\n\n### Step-by-Step Solution\n\n1. **Understanding \\( \\lfloor \\log_{10}(kn) \\rfloor \\)**:\n   - For a number \\( x \\), \\( \\lfloor \\log_{10}(x) \\rfloor \\) gives the exponent \\( m \\) such that \\( 10^m \\leq x < 10^{m+1} \\). This is equivalent to the number of digits of \\( x \\) minus 1.\n\n2. **Critical Values of \\( n \\)**:\n   - For each \\( k \\), the value \\( \\lfloor \\log_{10}(kn) \\rfloor \\) increases when \\( kn \\) crosses a power of 10. This happens at \\( n = \\frac{10^m}{k} \\) for integers \\( m \\).\n\n3. **Calculations for Specific \\( n \\)**:\n   - We need to compute \\( f(n) \\) for different values of \\( n \\) and find the maximum \\( n \\) such that \\( f(n) \\leq 300 \\).\n\n### Detailed Calculation for \\( n = 109 \\)\n\nFor \\( n = 109 \\):\n\n- **Range for \\( k = 1 \\) to \\( 9 \\)**:\n  - \\( kn \\) ranges from \\( 109 \\times 1 = 109 \\) to \\( 109 \\times 9 = 981 \\).\n  - All these values are three-digit numbers, so \\( \\lfloor \\log_{10}(kn) \\rfloor = 2 \\).\n  - Number of terms: 9.\n  - Contribution to \\( f(n) \\): \\( 9 \\times 2 = 18 \\).\n\n- **Range for \\( k = 10 \\) to \\( 91 \\)**:\n  - \\( kn \\) ranges from \\( 109 \\times 10 = 1090 \\) to \\( 109 \\times 91 = 9919 \\).\n  - All these values are four-digit numbers, so \\( \\lfloor \\log_{10}(kn) \\rfloor = 3 \\).\n  - Number of terms: \\( 91 - 10 + 1 = 82 \\).\n  - Contribution to \\( f(n) \\): \\( 82 \\times 3 = 246 \\).\n\n- **Range for \\( k = 92 \\) to \\( 100 \\)**:\n  - \\( kn \\) ranges from \\( 109 \\times 92 = 10028 \\) to \\( 109 \\times 100 = 10900 \\).\n  - All these values are five-digit numbers, so \\( \\lfloor \\log_{10}(kn) \\rfloor = 4 \\).\n  - Number of terms: \\( 100 - 92 + 1 = 9 \\).\n  - Contribution to \\( f(n) \\): \\( 9 \\times 4 = 36 \\).\n\n- **Total Sum**:\n  \\[\n  f(109) = 18 + 246 + 36 = 300\n  \\]\n\n### Verification for \\( n = 110 \\)\n\nFor \\( n = 110 \\):\n\n- **Range for \\( k = 1 \\) to \\( 9 \\)**:\n  - \\( kn \\) ranges from \\( 110 \\times 1 = 110 \\) to \\( 110 \\times 9 = 990 \\).\n  - All these values are three-digit numbers, so \\( \\lfloor \\log_{10}(kn) \\rfloor = 2 \\).\n  - Number of terms: 9.\n  - Contribution to \\( f(n) \\): \\( 9 \\times 2 = 18 \\).\n\n- **Range for \\( k = 10 \\) to \\( 90 \\)**:\n  - \\( kn \\) ranges from \\( 110 \\times 10 = 1100 \\) to \\( 110 \\times 90 = 9900 \\).\n  - All these values are four-digit numbers, so \\( \\lfloor \\log_{10}(kn) \\rfloor = 3 \\).\n  - Number of terms: \\( 90 - 10 + 1 = 81 \\).\n  - Contribution to \\( f(n) \\): \\( 81 \\times 3 = 243 \\).\n\n- **Range for \\( k = 91 \\) to \\( 100 \\)**:\n  - \\( kn \\) ranges from \\( 110 \\times 91 = 10010 \\) to \\( 110 \\times 100 = 11000 \\).\n  - All these values are five-digit numbers, so \\( \\lfloor \\log_{10}(kn) \\rfloor = 4 \\).\n  - Number of terms: \\( 100 - 91 + 1 = 10 \\).\n  - Contribution to \\( f(n) \\): \\( 10 \\times 4 = 40 \\).\n\n- **Total Sum**:\n  \\[\n  f(110) = 18 + 243 + 40 = 301\n  \\]\n\n### Conclusion\n\nThe largest value of \\( n \\) for which \\( f(n) \\leq 300 \\) is \\( n = 109 \\).\n\n\\[\n\\boxed{109}\n\\]"}