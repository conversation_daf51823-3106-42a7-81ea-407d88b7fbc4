{"row_number": 2635, "expected_answer": "8", "problem": "How many non-congruent triangles with integer side lengths $a \\leq b \\leq c$ satisfy the condition $a + b + c = 20$?", "generated_solution": "<think>\nOkay, so I need to find how many non-congruent triangles there are with integer side lengths a ≤ b ≤ c, such that a + b + c = 20. Alright, let's start by recalling the triangle inequality theorem. For a triangle with sides a, b, c, the sum of any two sides must be greater than the third side. Since the sides are ordered a ≤ b ≤ c, the key inequality here will be a + b > c. Because if a + b > c, then automatically the other inequalities like a + c > b and b + c > a will hold since c is the largest side.\n\nSo, the problem reduces to finding all ordered triples (a, b, c) where a, b, c are positive integers, a ≤ b ≤ c, a + b + c = 20, and a + b > c. Also, since they are non-congruent, we need to consider each set of sides only once, which is why the ordering a ≤ b ≤ c is important—it prevents counting permutations of the same triangle multiple times.\n\nLet me try to approach this systematically. Let's first express c in terms of a and b. Since a + b + c = 20, then c = 20 - a - b. Then, substituting into the triangle inequality: a + b > c ⇒ a + b > 20 - a - b ⇒ 2(a + b) > 20 ⇒ a + b > 10. So, a + b must be at least 11. But since a and b are positive integers and a ≤ b, let's see how we can iterate through possible values of a and b such that a + b > 10 and a + b + c = 20, with c ≥ b.\n\nAlso, since c ≥ b, substituting c = 20 - a - b gives 20 - a - b ≥ b ⇒ 20 - a ≥ 2b ⇒ 2b ≤ 20 - a. So, for each a, b can be up to floor((20 - a)/2). But also, since a ≤ b, we have a ≤ b ≤ floor((20 - a)/2). Wait, but maybe that's conflicting. Let's clarify.\n\nWait, since a ≤ b ≤ c, and c = 20 - a - b, then:\n\nFrom a ≤ b ≤ c, we have:\n\n1. a ≤ b\n2. b ≤ c = 20 - a - b\n\nSo from the second inequality, b ≤ 20 - a - b ⇒ 2b + a ≤ 20 ⇒ 2b ≤ 20 - a ⇒ b ≤ (20 - a)/2\n\nSo for each a, b can range from max(a, ceil((20 - a - b)/something)) Hmm, perhaps better to approach step by step.\n\nLet me try to structure the possible values of a, then find possible b for each a.\n\nFirst, a can be at least 1 (since sides must be positive integers). The maximum possible value for a can be found by considering that a ≤ b ≤ c, so a can't be more than 20/3, because a ≤ b ≤ c and a + b + c = 20. If a were greater than 20/3 ≈ 6.666, then even if b and c are equal to a, the total sum would be 3a > 20. Therefore, a can be at most 6 (since 7*3=21 > 20). So a can range from 1 to 6.\n\nNow, for each a from 1 to 6, let's find the possible b's.\n\nBut for each a, we need:\n\n1. a ≤ b ≤ c\n2. a + b + c = 20\n3. a + b > c\n\nSince c = 20 - a - b, the triangle inequality a + b > c becomes a + b > 20 - a - b ⇒ 2(a + b) > 20 ⇒ a + b > 10. So for each a, the sum a + b must be greater than 10. Since b ≥ a, then b must be at least max(a, 11 - a). Wait, let's see: if a + b > 10, then b > 10 - a. But since b must be an integer, b ≥ 11 - a. However, also, since b ≥ a, then the lower bound for b is the maximum of a and 11 - a). Let me check for each a:\n\nWhen a is from 1 to 6:\n\nFor example, if a=1:\n\nThen 11 - a = 10, so b must be ≥10. But since a=1 and b must be ≥a=1, but 11 - a =10, so the lower bound is max(1,10) =10. Wait, but 10 would require that b=10. Then c=20 -1 -10=9. But that contradicts c ≥ b, since c=9 and b=10. That can't be. Wait, so there must be a mistake here. Let me check.\n\nWait, c = 20 - a - b. So if a=1 and b=10, then c=9, which would not satisfy b ≤ c. Hence, that combination is invalid. Therefore, our constraints are not only a + b > c but also b ≤ c. So c must be at least b, so 20 - a - b ≥ b ⇒ 20 - a ≥ 2b ⇒ 2b ≤ 20 - a ⇒ b ≤ (20 -a)/2.\n\nSo for a=1:\n\nb must be ≥ a=1, and b ≤ (20 -1)/2=19/2=9.5 ⇒ b ≤9. But also, from triangle inequality, a + b > c ⇒ a + b > 20 - a - b ⇒ 2(a + b) > 20 ⇒ a + b >10 ⇒ b >10 -a. Since a=1, so 10 -a=9, so b>9. But since b must be integer, b≥10. But earlier, from the upper bound, b ≤9. So there is no possible b for a=1. So a=1 cannot form a valid triangle. That's interesting. So a=1 is invalid.\n\nWait, but let me verify this. If a=1, then possible b's would have to satisfy 1 ≤ b ≤9.5 ⇒ 1 ≤b≤9, but also from a + b >10 ⇒ b>9. But b is integer, so b must be at least 10. But 10 is greater than 9, so there's no solution here. Therefore, a=1 is impossible.\n\nSimilarly, let's check a=2:\n\nFor a=2:\n\nLower bound of b is max(a, 11 - a) = max(2,9) =9. But upper bound for b is (20 - a)/2=18/2=9. So b can only be 9. Let's check:\n\nIf a=2, b=9, then c=20 -2 -9=9. Then c=9, which is equal to b, so it's allowed (since b ≤c). The sides would be 2,9,9. Check triangle inequality: 2 +9>9? 11>9, yes. 2 +9>9, same, and 9 +9>2. All satisfy. So this is a valid triangle. So for a=2, only b=9 is possible.\n\nNext, a=3:\n\nLower bound for b is max(3, 11-3)=max(3,8)=8.\n\nUpper bound for b is (20 -3)/2=17/2=8.5 ⇒ b≤8. So the lower bound is 8, upper bound 8. Therefore, only b=8.\n\nCheck: a=3, b=8, c=20 -3 -8=9. So triangle 3,8,9. Check 3 +8>9? 11>9, yes. Also, 8≤9. Valid.\n\na=4:\n\nLower bound: max(4,11-4)=max(4,7)=7.\n\nUpper bound: (20-4)/2=16/2=8. So b can be from 7 to8. But since a=4 ≤b ≤8 and also a + b >10. Wait, 11 -4=7, so b must be ≥7, but since a=4 ≤b, the possible b's are 7 and 8.\n\nCheck for b=7:\n\nc=20 -4 -7=9. So sides 4,7,9. Check 4+7>9: 11>9, yes. Also 7≤9.\n\nFor b=8:\n\nc=20 -4 -8=8. So 4,8,8. Check 4+8>8? 12>8, yes. Valid.\n\nSo two triangles for a=4.\n\na=5:\n\nLower bound: max(5,11-5)=max(5,6)=6.\n\nUpper bound: (20 -5)/2=15/2=7.5 ⇒ b≤7.\n\nSo possible b from 6 to7.\n\nCheck b=6:\n\nc=20 -5 -6=9. So triangle 5,6,9. Check 5+6>9? 11>9, yes. Valid.\n\nb=7:\n\nc=20 -5 -7=8. So 5,7,8. Check 5+7>8? 12>8. Valid.\n\nSo two triangles for a=5.\n\na=6:\n\nLower bound: max(6,11-6)=max(6,5)=6.\n\nUpper bound: (20 -6)/2=14/2=7 ⇒ b≤7.\n\nSo possible b from 6 to7.\n\nBut also, since a=6, and a ≤b, then b starts at6.\n\nFor b=6:\n\nc=20 -6 -6=8. So 6,6,8. Check 6+6>8? 12>8. Valid.\n\nFor b=7:\n\nc=20 -6 -7=7. So 6,7,7. Check 6+7>7? 13>7. Valid.\n\nSo two triangles for a=6.\n\nWait, but let's check if a=6, b=7, c=7 is valid. Since 6 ≤7 ≤7, yes, and 6 +7 +7=20. Sums to 20, triangle inequality holds. Correct.\n\nNow let's compile all the valid triangles found so far:\n\na=2: (2,9,9)\n\na=3: (3,8,9)\n\na=4: (4,7,9), (4,8,8)\n\na=5: (5,6,9), (5,7,8)\n\na=6: (6,6,8), (6,7,7)\n\nWait, that's 1 +1 +2 +2 +2 =8 triangles.\n\nBut let's check if we missed any. Let me verify each a.\n\na=1: none, as discussed.\n\na=2: only b=9. Correct.\n\na=3: only b=8. Correct.\n\na=4: b=7 and 8. Correct.\n\na=5: b=6 and7. Correct.\n\na=6: b=6 and7. Correct.\n\nSo total 8 triangles. But I need to confirm this.\n\nAlternatively, maybe I made a miscalculation.\n\nWait, let's list them all:\n\n1. (2,9,9)\n2. (3,8,9)\n3. (4,7,9)\n4. (4,8,8)\n5. (5,6,9)\n6. (5,7,8)\n7. (6,6,8)\n8. (6,7,7)\n\nYes, 8 triangles. Let me check if there's any other possible combinations. For example, is there a possible a=5, b=8, c=7? But since we have the ordering a ≤b ≤c, c must be at least b, so if b=8, c=7 is invalid. So no. All ordered correctly.\n\nAlternatively, maybe for a=5, b=7, c=8, which is same as (5,7,8). But that's already counted.\n\nAlternatively, let's check for a=4, b=6: Would that work? If a=4, b=6, then c=10. Then 4,6,10. Check triangle inequality: 4 +6 >10? 10=10, which is not greater, so invalid. So that's why b=6 is not allowed for a=4. Because c=10, and 4 +6 is not greater than 10. So that's why b=7 is the starting point for a=4.\n\nSimilarly, for a=5, if b=5, then c=10. 5 +5 >10? 10>10? No. So invalid. Thus, b must start from 6 for a=5.\n\nWait, but with a=5, b=6, c=9: 5+6=11>9. So that's okay. But in that case, c=9, which is greater than b=6, so okay.\n\nWait, but when a=5 and b=5, c=10, which would be 5,5,10. But 5+5=10, not greater. So invalid.\n\nTherefore, all possible combinations seem to have been considered.\n\nHence, the total number of non-congruent triangles is 8. Therefore, the answer should be 8.\n\nBut wait, I remember solving similar problems before and the answer was 8, but maybe I should double-check. Alternatively, maybe there's another approach to confirm.\n\nAnother way: Let's consider that for each triangle with a ≤ b ≤ c and a + b + c =20, we can use the conditions that c < a + b and c ≥ b. So, given that a + b + c =20, then c =20 -a -b.\n\nWe can substitute into c < a + b: 20 - a -b < a + b ⇒ 20 < 2a + 2b ⇒ 10 < a + b. Also, since a ≤ b ≤ c, then c =20 -a -b ≥ b ⇒ 20 -a -b ≥ b ⇒ 20 -a ≥ 2b ⇒ 2b ≤20 -a ⇒ b ≤ (20 -a)/2. Also, since a ≤ b, then b ≥a. So combining:\n\n10 < a + b ≤ a + (20 -a)/2 ⇒ 10 < a + (20 -a)/2. Let's solve for a:\n\nMultiply both sides by 2: 20 < 2a +20 -a ⇒20 <a +20 ⇒ 0 <a. Which is always true since a ≥1.\n\nSo the main constraints are:\n\nFor each a from 1 to 6:\n\n- a ≤ b ≤ floor((20 -a)/2)\n\nBut wait, no, wait. The upper bound for b is floor((20 -a)/2), but also, since a + b >10.\n\nAlternatively, for each a, b must satisfy:\n\nmax(a, 11 -a) ≤b ≤ floor((20 -a)/2)\n\nWait, because from a + b >10 ⇒b >10 -a, and since b must be integer, b ≥11 -a.\n\nBut since a ≤b, we have b ≥ max(a, 11 -a).\n\nSo for each a, the lower bound is max(a, 11 -a), upper bound is floor((20 -a)/2). Then the number of possible b's is floor((20 -a)/2) - max(a, 11 -a) +1, if floor((20 -a)/2) ≥ max(a,11 -a).\n\nLet's compute this for each a from 1 to6:\n\na=1:\n\nmax(1,10)=10. floor((20 -1)/2)=9. So 10 >9 ⇒ no solutions.\n\na=2:\n\nmax(2,9)=9. floor(18/2)=9. So 9 ≤9. So number of b's: 9 -9 +1=1.\n\na=3:\n\nmax(3,8)=8. floor(17/2)=8. So 8 ≤8. So 8-8 +1=1.\n\na=4:\n\nmax(4,7)=7. floor(16/2)=8. So 7 ≤8. So 8 -7 +1=2.\n\na=5:\n\nmax(5,6)=6. floor(15/2)=7. 7 -6 +1=2.\n\na=6:\n\nmax(6,5)=6. floor(14/2)=7. 7 -6 +1=2.\n\nThus, total triangles: 0+1+1+2+2+2=8. Same as before. So the answer is 8. Therefore, the number of non-congruent triangles is 8.\n\n**Final Answer**\nThe number of non-congruent triangles is \\boxed{8}.\n</think>To determine the number of non-congruent triangles with integer side lengths \\(a \\leq b \\leq c\\) such that \\(a + b + c = 20\\), we need to satisfy the triangle inequality theorem and the given conditions.\n\n1. **Triangle Inequality**: For sides \\(a \\leq b \\leq c\\), the key inequality is \\(a + b > c\\). Additionally, we must have \\(a + b + c = 20\\) and \\(c \\geq b \\geq a\\).\n\n2. **Expressing \\(c\\)**: Since \\(a + b + c = 20\\), we can express \\(c\\) as \\(c = 20 - a - b\\). Substituting into the triangle inequality gives:\n   \\[\n   a + b > 20 - a - b \\implies 2(a + b) > 20 \\implies a + b > 10\n   \\]\n\n3. **Range of \\(a\\)**: Since \\(a \\leq b \\leq c\\), \\(a\\) must be at most \\(6\\) (since \\(3a \\leq 20 \\implies a \\leq \\left\\lfloor \\frac{20}{3} \\right\\rfloor = 6\\)).\n\n4. **Finding valid \\(b\\) for each \\(a\\)**:\n   - For each \\(a\\), \\(b\\) must satisfy:\n     \\[\n     a \\leq b \\leq \\left\\lfloor \\frac{20 - a}{2} \\right\\rfloor\n     \\]\n     and\n     \\[\n     a + b > 10 \\implies b > 10 - a \\implies b \\geq \\max(a, 11 - a)\n     \\]\n\n5. **Checking each \\(a\\)**:\n   - **\\(a = 1\\)**:\n     \\[\n     \\max(1, 10) = 10 \\quad \\text{and} \\quad \\left\\lfloor \\frac{19}{2} \\right\\rfloor = 9 \\quad \\text{(no valid \\(b\\))}\n     \\]\n   - **\\(a = 2\\)**:\n     \\[\n     \\max(2, 9) = 9 \\quad \\text{and} \\quad \\left\\lfloor \\frac{18}{2} \\right\\rfloor = 9 \\quad \\text{(valid \\(b = 9\\))}\n     \\]\n     Triangle: \\((2, 9, 9)\\)\n   - **\\(a = 3\\)**:\n     \\[\n     \\max(3, 8) = 8 \\quad \\text{and} \\quad \\left\\lfloor \\frac{17}{2} \\right\\rfloor = 8 \\quad \\text{(valid \\(b = 8\\))}\n     \\]\n     Triangle: \\((3, 8, 9)\\)\n   - **\\(a = 4\\)**:\n     \\[\n     \\max(4, 7) = 7 \\quad \\text{and} \\quad \\left\\lfloor \\frac{16}{2} \\right\\rfloor = 8 \\quad \\text{(valid \\(b = 7\\) and \\(b = 8\\))}\n     \\]\n     Triangles: \\((4, 7, 9)\\) and \\((4, 8, 8)\\)\n   - **\\(a = 5\\)**:\n     \\[\n     \\max(5, 6) = 6 \\quad \\text{and} \\quad \\left\\lfloor \\frac{15}{2} \\right\\rfloor = 7 \\quad \\text{(valid \\(b = 6\\) and \\(b = 7\\))}\n     \\]\n     Triangles: \\((5, 6, 9)\\) and \\((5, 7, 8)\\)\n   - **\\(a = 6\\)**:\n     \\[\n     \\max(6, 5) = 6 \\quad \\text{and} \\quad \\left\\lfloor \\frac{14}{2} \\right\\rfloor = 7 \\quad \\text{(valid \\(b = 6\\) and \\(b = 7\\))}\n     \\]\n     Triangles: \\((6, 6, 8)\\) and \\((6, 7, 7)\\)\n\nSummarizing the valid triangles, we have 8 non-congruent triangles:\n\n1. \\((2, 9, 9)\\)\n2. \\((3, 8, 9)\\)\n3. \\((4, 7, 9)\\)\n4. \\((4, 8, 8)\\)\n5. \\((5, 6, 9)\\)\n6. \\((5, 7, 8)\\)\n7. \\((6, 6, 8)\\)\n8. \\((6, 7, 7)\\)\n\nThus, the number of non-congruent triangles is \\(\\boxed{8}\\)."}