{"row_number": 2591, "expected_answer": "\\(1 - \\frac{1}{2021!}\\)", "problem": "Evaluate the sum $\\frac{1}{2!} + \\frac{2}{3!} + \\frac{3}{4!} + \\cdots + \\frac{2020}{2021!}$.", "generated_solution": "<think>\nOkay, so I need to evaluate the sum from n=1 to 2020 of n divided by (n+1) factorial. That is, the series is 1/(2!) + 2/(3!) + 3/(4!) + ... + 2020/(2021!). Hmm, let me think about how to approach this.\n\nFirst, I remember that factorials grow really fast, so maybe there's a telescoping series here. Telescoping series often cancel out terms when written properly. Let me see if I can rewrite each term in the series as a difference between two fractions. If I can express each term as f(n) - f(n+1), then when I sum them up, most terms should cancel out.\n\nLooking at the general term n/( (n+1)! ). Let's see. Let's take n/(n+1)! and try to split it into two terms. Maybe express it as A/n! - B/(n+1)! or something like that. Let's try.\n\nSuppose n/(n+1)! = A/n! - A/(n+1)! Let's check if that's possible. Let's solve for A.\n\nn/(n+1)! = A/n! - A/(n+1)!\n\nMultiply both sides by (n+1)! to eliminate denominators:\n\nn = A(n+1) - A\n\nSimplify the right side:\n\nA(n+1 - 1) = A*n\n\nSo n = A*n => A = 1.\n\nWait, so that means n/(n+1)! = 1/n! - 1/(n+1)!.\n\nIs that correct? Let me verify with n=1:\n\nLeft side: 1/2! = 1/2\n\nRight side: 1/1! - 1/2! = 1 - 1/2 = 1/2. Yes, that works.\n\nn=2: Left side: 2/6 = 1/3\n\nRight side: 1/2! - 1/3! = 1/2 - 1/6 = 1/3. Correct.\n\nSo yes, the general term can be written as 1/n! - 1/(n+1)!.\n\nTherefore, the sum from n=1 to 2020 of n/(n+1)! is equal to the sum from n=1 to 2020 of [1/n! - 1/(n+1)!].\n\nNow, let's write out the terms of this sum:\n\nFor n=1: 1/1! - 1/2!\n\nn=2: 1/2! - 1/3!\n\nn=3: 1/3! - 1/4!\n\n...\n\nn=2020: 1/2020! - 1/2021!\n\nWhen we add all these up, most of the middle terms should cancel out. Let's see:\n\nTotal sum = (1/1! - 1/2!) + (1/2! - 1/3!) + (1/3! - 1/4!) + ... + (1/2020! - 1/2021!)\n\nNotice that each -1/2! cancels with the next +1/2!, similarly for -1/3!, etc. So all the intermediate terms cancel, and we're left with the first term of the first expression and the last term of the last expression. Therefore, the total sum is:\n\n1/1! - 1/2021!\n\nSince 1/1! is just 1, this simplifies to 1 - 1/2021!.\n\nBut wait, let me check if I started the sum correctly. The original problem starts at n=1, so the first term is 1/2!, which corresponds to n=1. Then according to our decomposition, that term is 1/1! - 1/2!. Then when we sum all terms up to n=2020, the last term is 1/2020! - 1/2021!.\n\nSo the total sum telescopes to 1/1! - 1/2021!.\n\nTherefore, the answer is 1 - 1/2021!.\n\nBut let me verify with a small example. Suppose instead of up to 2020, we go up to n=2. Then the sum is 1/2! + 2/3! = 1/2 + 2/6 = 1/2 + 1/3 = 5/6.\n\nUsing the telescoping sum: 1 - 1/3! = 1 - 1/6 = 5/6. Correct. So the formula works here.\n\nAnother test: n=3. Sum is 1/2! + 2/3! + 3/4! = 1/2 + 1/3 + 3/24 = 1/2 + 1/3 + 1/8. Let's compute that: 12/24 + 8/24 + 3/24 = 23/24.\n\nTelescoping sum: 1 - 1/4! = 1 - 1/24 = 23/24. Correct.\n\nSo the formula holds. Therefore, the answer is indeed 1 - 1/2021!.\n\nTherefore, the final answer is 1 minus 1 over 2021 factorial, which is written as 1 - \\frac{1}{2021!}\n\nWait, but the problem says to put the answer inside a box and only the answer. So \\boxed{1 - \\dfrac{1}{2021!}}.\n\nAlternatively, sometimes answers are written as combined fractions, but in this case, 1 - 1/2021! is the simplest form. Since 2021! is a huge number, there's no need to combine them into a single fraction. So that's the answer.\n\n**Final Answer**\n\\boxed{1 - \\dfrac{1}{2021!}}\n</think>To evaluate the sum \\(\\frac{1}{2!} + \\frac{2}{3!} + \\frac{3}{4!} + \\cdots + \\frac{2020}{2021!}\\), we start by expressing each term \\(\\frac{n}{(n+1)!}\\) in a telescoping form.\n\nFirst, observe that:\n\\[\n\\frac{n}{(n+1)!} = \\frac{n****}{(n+1)!} = \\frac{n+1}{(n+1)!} - \\frac{1}{(n+1)!} = \\frac{1}{n!} - \\frac{1}{(n+1)!}\n\\]\n\nUsing this, we can rewrite the given sum as:\n\\[\n\\sum_{n=1}^{2020} \\left( \\frac{1}{n!} - \\frac{1}{(n+1)!} \\right)\n\\]\n\nNow, let's write out the terms explicitly:\n\\[\n\\left( \\frac{1}{1!} - \\frac{1}{2!} \\right) + \\left( \\frac{1}{2!} - \\frac{1}{3!} \\right) + \\left( \\frac{1}{3!} - \\frac{1}{4!} \\right) + \\cdots + \\left( \\frac{1}{2020!} - \\frac{1}{2021!} \\right)\n\\]\n\nNotice that this is a telescoping series, where most terms cancel out:\n\\[\n\\frac{1}{1!} - \\frac{1}{2!} + \\frac{1}{2!} - \\frac{1}{3!} + \\frac{1}{3!} - \\frac{1}{4!} + \\cdots + \\frac{1}{2020!} - \\frac{1}{2021!}\n\\]\n\nAfter cancellation, only the first and last terms remain:\n\\[\n\\frac{1}{1!} - \\frac{1}{2021!}\n\\]\n\nSince \\(\\frac{1}{1!} = 1\\), the sum simplifies to:\n\\[\n1 - \\frac{1}{2021!}\n\\]\n\nThus, the final answer is:\n\\[\n\\boxed{1 - \\dfrac{1}{2021!}}\n\\]"}